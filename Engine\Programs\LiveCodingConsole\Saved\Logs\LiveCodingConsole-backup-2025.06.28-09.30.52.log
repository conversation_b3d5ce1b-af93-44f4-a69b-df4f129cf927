﻿Log file open, 06/28/25 14:05:39
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-B07D477342F858742BEFCEAC367DAB69
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../Engine/Programs/LiveCodingConsole/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogInit: ExecutableName: LiveCodingConsole.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:41:40
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -Group=UE_ToonTank_0x08ddcd9e -Hidden -ProjectName="ToonTank"
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 35
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogInit: Presizing for max 100000 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
LogConfig: CVar [[r.DumpShaderDebugInfo:2]] deferred - dummy variable created
[2025.06.28-06.05.39:998][  0]LogConfig: CVar [[p.chaos.AllowCreatePhysxBodies:1]] deferred - dummy variable created
[2025.06.28-06.05.39:998][  0]LogConfig: CVar [[fx.SkipVectorVMBackendOptimizations:1]] deferred - dummy variable created
[2025.06.28-06.05.39:998][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.28-06.05.39:998][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.28-06.05.39:998][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.28-06.05.39:998][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.28-06.05.39:998][  0]LogInit: Computer: ADMINISTRATOR
[2025.06.28-06.05.39:998][  0]LogInit: User: maxwe
[2025.06.28-06.05.39:998][  0]LogInit: CPU Page size=4096, Cores=8
[2025.06.28-06.05.39:998][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.28-06.05.39:998][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.28-06.05.39:998][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.06.28-06.05.39:998][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.28-06.05.39:998][  0]LogMemory: Process Physical Memory: 55.86 MB used, 55.86 MB peak
[2025.06.28-06.05.39:998][  0]LogMemory: Process Virtual Memory: 123.57 MB used, 123.57 MB peak
[2025.06.28-06.05.39:998][  0]LogMemory: Physical Memory: 17195.28 MB used,  48245.31 MB free, 65440.59 MB total
[2025.06.28-06.05.39:998][  0]LogMemory: Virtual Memory: 19060.42 MB used,  56108.17 MB free, 75168.59 MB total
[2025.06.28-06.05.40:012][  0]LogUObjectArray: 590 objects as part of root set at end of initial load.
[2025.06.28-06.05.40:012][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.28-06.05.40:013][  0]LogInit: Using OS detected language (en-GB).
[2025.06.28-06.05.40:013][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.28-06.05.40:013][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.28-06.05.40:068][  0]LogInit: Using OS detected language (en-GB).
[2025.06.28-06.05.40:068][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.28-06.05.40:068][  0]LogTextLocalizationManager: No localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.28-06.05.40:068][  0]LogTextLocalizationManager: No localization for 'en-HK' exists, so 'en' will be used for the locale.
[2025.06.28-06.05.40:069][  0]LogPackageLocalizationCache: Processed 2 localized package path(s) for 1 prioritized culture(s) in 0.000094 seconds
[2025.06.28-06.05.40:069][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.06.28-06.05.40:077][  0]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.06.28-06.05.40:092][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.28-06.05.40:096][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.28-06.05.40:096][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.28-06.05.40:111][  0]LogSlate: Using FreeType 2.10.0
[2025.06.28-06.05.40:111][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.28-06.05.40:114][  0]LogStandaloneRenderer: D3D11 adapters settings:
[2025.06.28-06.05.40:114][  0]LogStandaloneRenderer: D3D11 adapters:
[2025.06.28-06.05.40:114][  0]LogStandaloneRenderer: Testing D3D11 adapter: 0. Description: 'NVIDIA GeForce RTX 2060'. VendorId: 10de. DeviceId: 1f08.
[2025.06.28-06.05.40:253][  0]LogStandaloneRenderer:   0. 'NVIDIA GeForce RTX 2060'. Feature level: 11_1
[2025.06.28-06.05.40:253][  0]LogStandaloneRenderer: Testing D3D11 adapter: 1. Description: 'Microsoft Basic Render Driver'. VendorId: 1414. DeviceId: 008c.
[2025.06.28-06.05.40:256][  0]LogStandaloneRenderer:   1. 'Microsoft Basic Render Driver'. Feature level: 11_1
[2025.06.28-06.05.40:256][  0]LogStandaloneRenderer:   Skip adapter.
[2025.06.28-06.05.40:256][  0]LogStandaloneRenderer: Selected D3D11 Description: 'NVIDIA GeForce RTX 2060'. VendorId: 10de. DeviceId: 1f08.
[2025.06.28-06.05.40:495][  0]LogLiveCodingServer: Display: Registered restarted process Y:\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe (PID: 16460, previous PID: 13968)
[2025.06.28-06.05.46:012][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll (0.129 MB)
[2025.06.28-06.05.46:103][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 823 lazy load module(s), and 385 reserved page ranges (0.000s, 7 translation units)
[2025.06.28-06.05.46:103][  0]LogLiveCodingServer: Display: Live coding ready - Save changes and press Ctrl+Alt+F11 to re-compile code
[2025.06.28-06.33.30:509][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.33.30:509][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.33.30:555][  0]LogSlate: Took 0.000305 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.28-06.33.30:557][  0]LogSlate: Took 0.000174 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.28-06.33.30:651][  0]LogSlate: Took 0.000181 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.28-06.33.35:069][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.33.35:069][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.33.35:069][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.33.35:069][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.33.35:069][  0]LogLiveCodingServer: Display: Building patch from 4 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 20 more times)
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 20 more times)
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.33.35:109][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.35:110][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.33.35:110][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.35:110][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.35:110][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.36:778][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.36:778][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debug909b8d83-7e4b-4ed1-a086-3b80ac7345b3 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\70C8.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_0.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_0.exp
Detoured run took 1.4s
[2025.06.28-06.33.36:779][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.33.36:880][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.33.36:881][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.36:904][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.33.36:904][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.36:905][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.33.36:905][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.33.36:905][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.36:905][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.33.37:192][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.33.37:200][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.33.37:202][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.33.39:674][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.37.47:346][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.37.47:346][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.37.51:222][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.37.51:222][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.37.51:222][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.37.51:222][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.37.51:222][  0]LogLiveCodingServer: Display: Building patch from 4 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.37.52:849][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debuge9ef0844-3beb-42bc-8c1e-1e17817f80ed ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\5955.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_1.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_1.exp
Detoured run took 1.4s
[2025.06.28-06.37.52:850][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.37.52:964][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.37.52:966][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.37.52:986][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.37.52:986][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.37.52:987][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.37.52:987][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.37.52:987][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.37.52:987][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.37.53:163][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.37.53:171][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.37.53:173][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.37.55:747][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.40.52:429][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.40.52:429][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.41.05:116][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.42.09:986][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.42.09:986][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.42.13:615][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.42.13:615][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.42.13:615][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.42.13:615][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.42.13:615][  0]LogLiveCodingServer: Display: Building patch from 4 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.42.14:881][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debugfff2ecd0-63e5-418b-9288-308d1b1a6786 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\5A2E.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_2.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_2.exp
Detoured run took 1.1s
[2025.06.28-06.42.14:882][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.42.15:038][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.42.15:039][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.42.15:056][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.42.15:056][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.42.15:057][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.42.15:057][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.42.15:057][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.42.15:057][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.42.15:229][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.42.15:237][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.42.15:239][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.42.17:814][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.43.16:987][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.43.16:987][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.43.19:328][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.43.19:328][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.43.19:328][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.43.19:328][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.43.19:328][  0]LogLiveCodingServer: Display: Building patch from 4 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.43.20:917][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debugf29c7eb5-f4e7-48c6-9d62-2ec618374811 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\5AF5.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_3.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_3.exp
Detoured run took 1.4s
[2025.06.28-06.43.20:918][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.43.21:013][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.43.21:014][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.43.21:033][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.43.21:033][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.43.21:034][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.43.21:034][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.43.21:034][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.43.21:034][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.43.21:214][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.43.21:221][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.43.21:223][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.43.23:798][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.46.49:211][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.46.49:211][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Projectile.cpp.obj was modified or is new
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.46.55:759][  0]LogLiveCodingServer: Display: Building patch from 5 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.46.55:776][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find contributions for compiland Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Projectile.cpp.obj
[2025.06.28-06.46.57:148][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debugaba4c5f3-2cc3-4483-8c0b-9f7d3b3355cc ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\A87B.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_4.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_4.exp
Detoured run took 1.1s
[2025.06.28-06.46.57:148][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:505][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.46.57:506][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:525][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.46.57:525][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:526][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.46.57:526][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.46.57:526][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:526][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.46.57:759][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.46.57:768][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.46.57:770][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.47.00:328][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.54.38:447][  0]LogLiveCodingServer: Display: Manual recompile triggered
[2025.06.28-06.54.38:447][  0]LogLiveCodingServer: Display: ---------- Creating patch ----------
[2025.06.28-06.54.41:869][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\BasePawn.cpp.obj was modified or is new
[2025.06.28-06.54.41:869][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Module.ToonTank.gen.cpp.obj was modified or is new
[2025.06.28-06.54.41:869][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Projectile.cpp.obj was modified or is new
[2025.06.28-06.54.41:869][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tank.cpp.obj was modified or is new
[2025.06.28-06.54.41:869][  0]LogLiveCodingServer: Display: File Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\Tower.cpp.obj was modified or is new
[2025.06.28-06.54.41:870][  0]LogLiveCodingServer: Display: Building patch from 5 file(s) for Live coding module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll
[2025.06.28-06.54.43:210][  0]LogLiveCodingServer: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debug7200e972-f8de-43ff-885d-c1673dd91d7b ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\C50B.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_5.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_5.exp
Detoured run took 1.1s
[2025.06.28-06.54.43:210][  0]LogLiveCodingServer: Display: Successfully linked patch (0.000s)
[2025.06.28-06.54.43:510][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.54.43:511][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 11 more times)
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 24 more times)
[2025.06.28-06.54.43:512][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:542][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.54.43:542][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:543][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 5 more times)
[2025.06.28-06.54.43:543][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl
[2025.06.28-06.54.43:543][  0]LogLiveCodingServer: Error: ERROR:         o Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:543][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl
[2025.06.28-06.54.43:797][  0]LogLiveCodingServer: Error: ERROR:       * Cannot find image section .voltbl (repeated 2 more times)
[2025.06.28-06.54.43:805][  0]LogLiveCodingServer: Display: Patch creation for module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll successful (0.000s)
[2025.06.28-06.54.43:809][  0]LogLiveCodingServer: Display: ---------- Finished (0.000s) ----------
[2025.06.28-06.54.46:345][  0]LogSlate: Request Window '' being destroyed
[2025.06.28-06.55.51:047][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-1162.dll (0.150 MB)
[2025.06.28-06.55.51:123][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-06.56.07:122][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-5248.dll (0.150 MB)
[2025.06.28-06.56.07:188][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-06.56.18:275][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0003.dll (0.150 MB)
[2025.06.28-06.56.18:341][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-06.56.30:420][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0004.dll (0.150 MB)
[2025.06.28-06.56.30:474][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-08.40.01:786][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0008.dll (0.153 MB)
[2025.06.28-08.40.01:862][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-08.41.16:872][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0009.dll (0.153 MB)
[2025.06.28-08.41.16:927][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-09.27.21:565][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0011.dll (0.153 MB)
[2025.06.28-09.27.21:674][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-09.30.00:294][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0012.dll (0.153 MB)
[2025.06.28-09.30.00:370][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-09.30.40:579][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0013.dll (0.153 MB)
[2025.06.28-09.30.40:622][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
[2025.06.28-09.30.51:875][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0014.dll (0.153 MB)
[2025.06.28-09.30.51:919][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 0 lazy load module(s), and 0 reserved page ranges (0.000s, 8 translation units)
