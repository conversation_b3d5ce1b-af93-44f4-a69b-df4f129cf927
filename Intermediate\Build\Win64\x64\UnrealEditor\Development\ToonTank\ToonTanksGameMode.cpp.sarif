{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C2653", "level": "error", "message": {"text": "'UGameplayStatics': is not a class or namespace name"}, "analysisTarget": {"uri": "file:///Y:/UE Project/ToonTank/Source/ToonTank/ToonTanksGameMode.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///Y:/UE Project/ToonTank/Source/ToonTank/ToonTanksGameMode.cpp"}, "region": {"startLine": 7, "startColumn": 24, "snippet": {"text": "    Tank = Cast<ATank>(UGameplayStatics::GetPlayerPawn(this, 0));"}}}}]}, {"ruleId": "C3861", "level": "error", "message": {"text": "'GetPlayerPawn': identifier not found"}, "analysisTarget": {"uri": "file:///Y:/UE Project/ToonTank/Source/ToonTank/ToonTanksGameMode.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///Y:/UE Project/ToonTank/Source/ToonTank/ToonTanksGameMode.cpp"}, "region": {"startLine": 7, "startColumn": 42, "snippet": {"text": "    Tank = Cast<ATank>(UGameplayStatics::GetPlayerPawn(this, 0));"}}}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}