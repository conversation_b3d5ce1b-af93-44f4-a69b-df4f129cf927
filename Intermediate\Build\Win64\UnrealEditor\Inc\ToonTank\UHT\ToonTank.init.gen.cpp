// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeToonTank_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_ToonTank;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_ToonTank()
	{
		if (!Z_Registration_Info_UPackage__Script_ToonTank.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/ToonTank",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0xB77CDE09,
				0x42A69A2C,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_ToonTank.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_ToonTank.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_ToonTank(Z_Construct_UPackage__Script_ToonTank, TEXT("/Script/ToonTank"), Z_Registration_Info_UPackage__Script_ToonTank, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xB77CDE09, 0x42A69A2C));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
