[{"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\BasePawn.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\BasePawn.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\HealthComponent.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\HealthComponent.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Projectile.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Projectile.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Tank.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Tank.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\ToonTank.Build.cs", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\ToonTank.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\ToonTank.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\ToonTanksGameMode.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\ToonTanksGameMode.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Tower.cpp", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}, {"file": "Y:\\UE Project\\ToonTank\\Source\\ToonTank\\Tower.h", "arguments": ["C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "@Y:\\UE_5.6\\.vscode\\compileCommands_Default\\ToonTank.1.rsp"], "directory": "Y:\\UE_5.6\\Engine\\Source"}]