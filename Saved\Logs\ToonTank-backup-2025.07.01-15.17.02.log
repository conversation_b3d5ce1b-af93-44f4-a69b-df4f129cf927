﻿Log file open, 07/01/25 16:42:42
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=30776)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: ToonTank
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.5965] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 7 3700X 8-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "Y:\UE Project\ToonTank\ToonTank.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="736956f4414f060eb3ef6884c4fdf35b"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.333468
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-31396C9B4161344677994AAF1F34203D
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../UE Project/ToonTank/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogAssetRegistry: Display: PlatformFileJournal is not available on volume 'Y:' of project directory 'Y:/UE Project/ToonTank/', so AssetDiscovery cache will not be read or written. Unavailability reason:
	NTFS Journal is not active for volume 'Y:'. Launch cmd.exe as admin and run command `fsutil usn createJournal Y: m=<SizeInBytes>`. Recommended <SizeInBytes> is 0x40000000 (1GB).
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.06 seconds
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 66.7 MiB from ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.07 seconds
LogConfig: Display: Loading TVOS ini files took 0.07 seconds
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "Y:/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.07.01-08.42.43:090][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.07.01-08.42.43:090][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.07.01-08.42.43:091][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.07.01-08.42.43:094][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.07.01-08.42.43:094][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.07.01-08.42.43:094][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.07.01-08.42.43:094][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.07.01-08.42.43:095][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.07.01-08.42.43:095][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.07.01-08.42.43:098][  0]LogRHI: Using Default RHI: D3D12
[2025.07.01-08.42.43:098][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.01-08.42.43:098][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.01-08.42.43:101][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.07.01-08.42.43:101][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.01-08.42.43:278][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2060 (VendorId: 10de, DeviceId: 1f08, SubSysId: 37551462, Revision: 00a1
[2025.07.01-08.42.43:278][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.07.01-08.42.43:278][  0]LogD3D12RHI:   Adapter has 5954MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 2 output[s], UMA:false
[2025.07.01-08.42.43:279][  0]LogD3D12RHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.07.01-08.42.43:279][  0]LogD3D12RHI:      Driver Date: 6-1-2024
[2025.07.01-08.42.43:285][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.07.01-08.42.43:285][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.07.01-08.42.43:285][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 0 output[s], UMA:true
[2025.07.01-08.42.43:285][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.07.01-08.42.43:285][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.07.01-08.42.43:285][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.01-08.42.43:285][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.07.01-08.42.43:285][  0]LogHAL: Display: Platform has ~ 64 GB [68619431936 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.07.01-08.42.43:286][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.07.01-08.42.43:286][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.07.01-08.42.43:286][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.07.01-08.42.43:286][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.07.01-08.42.43:286][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.01-08.42.43:286][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.07.01-08.42.43:286][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.07.01-08.42.43:286][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.07.01-08.42.43:286][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.07.01-08.42.43:286][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.07.01-08.42.43:286][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.01-08.42.43:286][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.07.01-08.42.43:286][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Y:/UE Project/ToonTank/Saved/Config/WindowsEditor/Editor.ini]
[2025.07.01-08.42.43:286][  0]LogInit: Computer: ADMINISTRATOR
[2025.07.01-08.42.43:286][  0]LogInit: User: maxwe
[2025.07.01-08.42.43:286][  0]LogInit: CPU Page size=4096, Cores=8
[2025.07.01-08.42.43:286][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.07.01-08.42.43:286][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.07.01-08.42.43:286][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.07.01-08.42.43:286][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.07.01-08.42.43:287][  0]LogMemory: Process Physical Memory: 639.62 MB used, 657.39 MB peak
[2025.07.01-08.42.43:287][  0]LogMemory: Process Virtual Memory: 660.70 MB used, 660.70 MB peak
[2025.07.01-08.42.43:287][  0]LogMemory: Physical Memory: 22061.36 MB used,  43379.23 MB free, 65440.59 MB total
[2025.07.01-08.42.43:287][  0]LogMemory: Virtual Memory: 27283.87 MB used,  47884.72 MB free, 75168.59 MB total
[2025.07.01-08.42.43:287][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.07.01-08.42.43:289][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.07.01-08.42.43:291][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.07.01-08.42.43:291][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.07.01-08.42.43:292][  0]LogInit: Using OS detected language (en-GB).
[2025.07.01-08.42.43:292][  0]LogInit: Using OS detected locale (en-HK).
[2025.07.01-08.42.43:294][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.07.01-08.42.43:294][  0]LogInit: Setting process to per monitor DPI aware
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.07.01-08.42.43:778][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United Kingdom) - (Keyboard).
[2025.07.01-08.42.43:790][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.07.01-08.42.43:793][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.07.01-08.42.43:797][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.07.01-08.42.43:798][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.07.01-08.42.43:975][  0]LogRHI: Using Default RHI: D3D12
[2025.07.01-08.42.43:975][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.01-08.42.43:975][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.01-08.42.43:975][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.01-08.42.43:975][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.01-08.42.43:975][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.07.01-08.42.43:975][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.07.01-08.42.43:976][  0]LogWindows: Attached monitors:
[2025.07.01-08.42.43:976][  0]LogWindows:     resolution: 1920x1080, work area: (1920, 0) -> (3840, 1040), device: '\\.\DISPLAY1'
[2025.07.01-08.42.43:976][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1040), device: '\\.\DISPLAY2' [PRIMARY]
[2025.07.01-08.42.43:976][  0]LogWindows: Found 2 attached monitors.
[2025.07.01-08.42.43:976][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.07.01-08.42.43:976][  0]LogRHI: RHI Adapter Info:
[2025.07.01-08.42.43:976][  0]LogRHI:             Name: NVIDIA GeForce RTX 2060
[2025.07.01-08.42.43:976][  0]LogRHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.07.01-08.42.43:976][  0]LogRHI:      Driver Date: 6-1-2024
[2025.07.01-08.42.43:976][  0]LogD3D12RHI:     GPU DeviceId: 0x1f08 (for the marketing name, search the web for "GPU Device Id")
[2025.07.01-08.42.43:976][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.07.01-08.42.44:067][  0]LogNvidiaAftermath: Aftermath initialized
[2025.07.01-08.42.44:068][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.07.01-08.42.44:123][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.07.01-08.42.44:123][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.07.01-08.42.44:123][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Bindless resources are supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Raster order views are supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.07.01-08.42.44:124][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.07.01-08.42.44:199][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000002517C7722C0)
[2025.07.01-08.42.44:200][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000002517C772580)
[2025.07.01-08.42.44:200][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000002517C772840)
[2025.07.01-08.42.44:200][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.07.01-08.42.44:200][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.07.01-08.42.44:353][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.07.01-08.42.44:353][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.07.01-08.42.44:353][  0]LogRHI: Texture pool is 3267 MB (70% of 4667 MB)
[2025.07.01-08.42.44:353][  0]LogD3D12RHI: Async texture creation enabled
[2025.07.01-08.42.44:353][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.07.01-08.42.44:362][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.07.01-08.42.44:364][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.07.01-08.42.44:364][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.07.01-08.42.44:364][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.07.01-08.42.44:364][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.07.01-08.42.44:364][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.07.01-08.42.44:366][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all'
[2025.07.01-08.42.44:367][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all" ]
[2025.07.01-08.42.44:376][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.07.01-08.42.44:377][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.07.01-08.42.44:377][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.07.01-08.42.44:377][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.07.01-08.42.44:377][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.07.01-08.42.44:409][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.07.01-08.42.44:425][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.07.01-08.42.44:425][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.07.01-08.42.44:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.07.01-08.42.44:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.07.01-08.42.44:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.07.01-08.42.44:441][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.07.01-08.42.44:456][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.07.01-08.42.44:456][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.07.01-08.42.44:456][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.07.01-08.42.44:456][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.07.01-08.42.44:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.07.01-08.42.44:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.07.01-08.42.44:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.07.01-08.42.44:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.07.01-08.42.44:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.07.01-08.42.44:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.07.01-08.42.44:487][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.07.01-08.42.44:512][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   VVM_1_0
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.07.01-08.42.44:520][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.07.01-08.42.44:520][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.07.01-08.42.44:523][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.07.01-08.42.44:523][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.07.01-08.42.44:523][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.07.01-08.42.44:523][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.01-08.42.44:523][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.07.01-08.42.44:753][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.07.01-08.42.44:753][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.01-08.42.44:753][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.07.01-08.42.44:754][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.07.01-08.42.44:754][  0]LogZenServiceInstance: InTree version at 'Y:/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.01-08.42.44:755][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.01-08.42.44:755][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.07.01-08.42.44:755][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 9868  --child-id Zen_9868_Startup'
[2025.07.01-08.42.44:832][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.07.01-08.42.44:832][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.077 seconds
[2025.07.01-08.42.44:833][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.07.01-08.42.44:840][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.07.01-08.42.44:840][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=874.01MBs, RandomWriteSpeed=187.46MBs. Assigned SpeedClass 'Local'
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.07.01-08.42.44:841][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.07.01-08.42.44:842][  0]LogShaderCompilers: Guid format shader working directory is 15 characters bigger than the processId version (../../../../UE Project/ToonTank/Intermediate/Shaders/WorkingDirectory/9868/).
[2025.07.01-08.42.44:842][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/53817CC1484DF31B619E4F81C98378A7/'.
[2025.07.01-08.42.44:851][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.07.01-08.42.44:851][  0]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.07.01-08.42.44:861][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.07.01-08.42.44:861][  0]LogShaderCompilers: Display: Using XGE Controller for shader compilation
[2025.07.01-08.42.44:861][  0]LogShaderCompilers: Display: Using 8 local workers for shader compilation
[2025.07.01-08.42.44:863][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../UE Project/ToonTank/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.07.01-08.42.44:863][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.07.01-08.42.45:272][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.07.01-08.42.46:360][  0]LogSlate: Using FreeType 2.10.0
[2025.07.01-08.42.46:360][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.07.01-08.42.46:363][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.01-08.42.46:363][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.01-08.42.46:363][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.01-08.42.46:363][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.01-08.42.46:384][  0]LogAssetRegistry: FAssetRegistry took 0.0023 seconds to start up
[2025.07.01-08.42.46:385][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.07.01-08.42.46:474][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin.
[2025.07.01-08.42.46:672][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.07.01-08.42.46:672][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.07.01-08.42.46:715][  0]LogDeviceProfileManager: Active device profile: [000002512D53E600][000002512A642800 66] WindowsEditor
[2025.07.01-08.42.46:715][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.07.01-08.42.46:718][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-08.42.46:720][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.07.01-08.42.46:720][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.07.01-08.42.46:720][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.07.01-08.42.46:728][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.07.01-08.42.46:729][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR'
[2025.07.01-08.42.46:729][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR" -nocompile -nocompileuat ]
[2025.07.01-08.42.46:755][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:755][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.07.01-08.42.46:755][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:755][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:756][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.07.01-08.42.46:756][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:756][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:758][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.07.01-08.42.46:758][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.07.01-08.42.46:759][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.07.01-08.42.46:760][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:760][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:761][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:761][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.07.01-08.42.46:762][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.07.01-08.42.46:763][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.07.01-08.42.46:763][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:793][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.07.01-08.42.46:793][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:809][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.07.01-08.42.46:809][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.46:823][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.07.01-08.42.46:823][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.01-08.42.47:015][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.07.01-08.42.47:015][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.07.01-08.42.47:015][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.07.01-08.42.47:015][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.07.01-08.42.47:015][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.07.01-08.42.47:479][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.07.01-08.42.47:532][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.07.01-08.42.47:532][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.07.01-08.42.47:532][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.07.01-08.42.47:532][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.07.01-08.42.47:555][  0]LogSlate: Border
[2025.07.01-08.42.47:555][  0]LogSlate: BreadcrumbButton
[2025.07.01-08.42.47:555][  0]LogSlate: Brushes.Title
[2025.07.01-08.42.47:555][  0]LogSlate: ColorPicker.ColorThemes
[2025.07.01-08.42.47:555][  0]LogSlate: Default
[2025.07.01-08.42.47:555][  0]LogSlate: Icons.Save
[2025.07.01-08.42.47:555][  0]LogSlate: Icons.Toolbar.Settings
[2025.07.01-08.42.47:555][  0]LogSlate: ListView
[2025.07.01-08.42.47:555][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.07.01-08.42.47:555][  0]LogSlate: SoftwareCursor_Grab
[2025.07.01-08.42.47:555][  0]LogSlate: TableView.DarkRow
[2025.07.01-08.42.47:555][  0]LogSlate: TableView.Row
[2025.07.01-08.42.47:555][  0]LogSlate: TreeView
[2025.07.01-08.42.47:633][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.07.01-08.42.47:636][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 3.203 ms
[2025.07.01-08.42.47:655][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.07.01-08.42.47:655][  0]LogInit: XR: MultiViewport is Disabled
[2025.07.01-08.42.47:655][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.07.01-08.42.47:669][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.07.01-08.42.47:679][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.07.01-08.42.47:688][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 02AA7508660A4BD0800000000000EB00 | Instance: 413A83594DD92BD33D558CBD820BA7FC (ADMINISTRATOR-9868).
[2025.07.01-08.42.47:772][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.07.01-08.42.47:774][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.07.01-08.42.47:775][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.07.01-08.42.47:775][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:51629'.
[2025.07.01-08.42.47:778][  0]LogUdpMessaging: Display: Added local interface '192.168.0.177' to multicast group '230.0.0.1:6666'
[2025.07.01-08.42.47:780][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   NPU:       no
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: Interface availability:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   GPU: yes
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   RDG: yes
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT:   NPU: no
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.01-08.42.47:794][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.01-08.42.48:253][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.07.01-08.42.48:253][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.07.01-08.42.48:277][  0]LogMetaSound: MetaSound Engine Initialized
[2025.07.01-08.42.48:352][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.07.01-08.42.48:352][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.07.01-08.42.48:359][  0]LogTimingProfiler: Initialize
[2025.07.01-08.42.48:359][  0]LogTimingProfiler: OnSessionChanged
[2025.07.01-08.42.48:359][  0]LoadingProfiler: Initialize
[2025.07.01-08.42.48:359][  0]LoadingProfiler: OnSessionChanged
[2025.07.01-08.42.48:359][  0]LogNetworkingProfiler: Initialize
[2025.07.01-08.42.48:359][  0]LogNetworkingProfiler: OnSessionChanged
[2025.07.01-08.42.48:359][  0]LogMemoryProfiler: Initialize
[2025.07.01-08.42.48:359][  0]LogMemoryProfiler: OnSessionChanged
[2025.07.01-08.42.48:438][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.07.01-08.42.48:533][  0]SourceControl: Revision control is disabled
[2025.07.01-08.42.48:540][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.01-08.42.48:541][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.01-08.42.48:541][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.01-08.42.48:541][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.01-08.42.48:549][  0]SourceControl: Revision control is disabled
[2025.07.01-08.42.48:727][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.07.01-08.42.48:733][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.01-08.42.48:733][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.01-08.42.48:765][  0]LogCollectionManager: Loaded 0 collections in 0.001291 seconds
[2025.07.01-08.42.48:767][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Saved/Collections/' took 0.00s
[2025.07.01-08.42.48:770][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Developers/maxwe/Collections/' took 0.00s
[2025.07.01-08.42.48:772][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Collections/' took 0.00s
[2025.07.01-08.42.48:827][  0]LogTurnkeySupport: Turnkey Device: Win64@Administrator: (Name=Administrator, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.07.01-08.42.48:832][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.01-08.42.48:832][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.01-08.42.48:832][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.01-08.42.48:832][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.01-08.42.48:851][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.01-08.42.48:851][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.01-08.42.48:851][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.01-08.42.48:851][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.01-08.42.48:870][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-07-01T08:42:48.870Z using C
[2025.07.01-08.42.48:870][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=ToonTank, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.07.01-08.42.48:870][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.07.01-08.42.48:871][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.07.01-08.42.48:876][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.07.01-08.42.48:876][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.07.01-08.42.48:876][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.07.01-08.42.48:876][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000060
[2025.07.01-08.42.48:928][  0]LogUObjectArray: 42373 objects as part of root set at end of initial load.
[2025.07.01-08.42.48:929][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.07.01-08.42.49:047][  0]LogEngine: Initializing Engine...
[2025.07.01-08.42.49:200][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.07.01-08.42.49:200][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.07.01-08.42.49:459][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.07.01-08.42.49:472][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.07.01-08.42.49:480][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.07.01-08.42.49:490][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.07.01-08.42.49:490][  0]LogInit: Texture streaming: Enabled
[2025.07.01-08.42.49:495][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.07.01-08.42.49:499][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.07.01-08.42.49:502][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.07.01-08.42.49:502][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.07.01-08.42.49:503][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.07.01-08.42.49:503][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-08.42.49:503][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-08.42.49:503][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-08.42.49:503][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-08.42.49:503][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-08.42.49:503][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-08.42.49:508][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-08.42.49:554][  0]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.07.01-08.42.49:555][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-08.42.49:556][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-08.42.49:556][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-08.42.49:557][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.07.01-08.42.49:557][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.07.01-08.42.49:559][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.07.01-08.42.49:559][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.07.01-08.42.49:559][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.07.01-08.42.49:559][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.07.01-08.42.49:560][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.07.01-08.42.49:564][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.07.01-08.42.49:570][  0]LogInit: Undo buffer set to 256 MB
[2025.07.01-08.42.49:570][  0]LogInit: Transaction tracking system initialized
[2025.07.01-08.42.49:577][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.07.01-08.42.49:607][  0]LocalizationService: Localization service is disabled
[2025.07.01-08.42.49:736][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/' took 0.00s
[2025.07.01-08.42.49:778][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.07.01-08.42.49:779][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.07.01-08.42.49:779][  0]LogPython: Using Python 3.11.8
[2025.07.01-08.42.49:806][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.07.01-08.42.50:101][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.07.01-08.42.50:176][  0]LogEditorDataStorage: Initializing
[2025.07.01-08.42.50:179][  0]LogEditorDataStorage: Initialized
[2025.07.01-08.42.50:182][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.07.01-08.42.50:190][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.07.01-08.42.50:279][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.07.01-08.42.50:283][  0]SourceControl: Revision control is disabled
[2025.07.01-08.42.50:283][  0]LogUnrealEdMisc: Loading editor; pre map load, took 8.102
[2025.07.01-08.42.50:284][  0]Cmd: MAP LOAD FILE="../../../../UE Project/ToonTank/Content/Maps/Main.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.07.01-08.42.50:286][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.07.01-08.42.50:286][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.42.50:306][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-08.42.50:308][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.07.01-08.42.50:398][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Main'.
[2025.07.01-08.42.50:398][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-08.42.50:415][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.07.01-08.42.50:439][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.07.01-08.42.50:439][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.07.01-08.42.50:439][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.058ms to complete.
[2025.07.01-08.42.50:447][  0]LogUnrealEdMisc: Total Editor Startup Time, took 8.266
[2025.07.01-08.42.50:563][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.07.01-08.42.50:691][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.42.50:775][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.42.50:858][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.42.50:940][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.42.50:976][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:976][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.07.01-08.42.50:977][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:977][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.07.01-08.42.50:978][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:978][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.07.01-08.42.50:978][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:979][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.07.01-08.42.50:979][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:980][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.07.01-08.42.50:980][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:981][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.07.01-08.42.50:981][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:982][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.07.01-08.42.50:982][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:982][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.07.01-08.42.50:983][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:983][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.07.01-08.42.50:984][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.01-08.42.50:984][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.07.01-08.42.51:073][  0]LogSlate: Took 0.000280 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.07.01-08.42.51:140][  0]LogAssetRegistry: Display: Asset registry cache written as 66.7 MiB to ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin
[2025.07.01-08.42.51:268][  0]LogSlate: Took 0.000240 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.07.01-08.42.51:314][  0]LogSlate: Took 0.000198 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.07.01-08.42.51:746][  0]LogStall: Startup...
[2025.07.01-08.42.51:748][  0]LogStall: Startup complete.
[2025.07.01-08.42.51:775][  0]LogLoad: (Engine Initialization) Total time: 9.59 seconds
[2025.07.01-08.42.52:121][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.07.01-08.42.52:122][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.07.01-08.42.52:181][  0]LogSlate: Took 0.000126 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.07.01-08.42.52:222][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.42.52:224][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.07.01-08.42.52:257][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.07.01-08.42.52:267][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 42.448 ms
[2025.07.01-08.42.52:541][  1]LogAssetRegistry: AssetRegistryGather time 0.1521s: AssetDataDiscovery 0.0226s, AssetDataGather 0.0599s, StoreResults 0.0696s. Wall time 6.1590s.
	NumCachedDirectories 0. NumUncachedDirectories 1473. NumCachedFiles 7409. NumUncachedFiles 5.
	BackgroundTickInterruptions 0.
[2025.07.01-08.42.52:564][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.07.01-08.42.52:575][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000001 seconds (updated 0 objects)
[2025.07.01-08.42.52:576][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.07.01-08.42.52:706][  4]LogSourceControl: Uncontrolled asset discovery finished in 0.129978 seconds (Found 7390 uncontrolled assets)
[2025.07.01-08.42.53:700][  7]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 4.489458
[2025.07.01-08.42.53:702][  7]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.07.01-08.42.53:703][  7]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4.823649
[2025.07.01-08.42.55:035][ 11]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.07.01-08.42.55:368][ 23]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 6.459628
[2025.07.01-08.42.55:370][ 23]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.07.01-08.42.55:371][ 23]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6.459628, Update Interval: 345.437164
[2025.07.01-08.42.56:520][134]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-08.42.56:528][134]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-08.42.56:532][134]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.07.01-08.42.56:532][134]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.07.01-08.42.56:532][134]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-08.42.56:539][134]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006324s)
[2025.07.01-08.42.56:539][134]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006380s)
[2025.07.01-08.42.56:607][134]LogUObjectHash: Compacting FUObjectHashTables data took   0.62ms
[2025.07.01-08.42.56:609][134]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-08.42.56:611][134]LogPlayLevel: PIE: World Init took: (0.001964s)
[2025.07.01-08.42.56:612][134]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-08.42.56:612][134]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-08.42.56:612][134]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-08.42.56:612][134]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-08.42.56:612][134]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-08.42.56:612][134]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-08.42.56:614][134]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-08.42.56:640][134]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.07.01-08.42.56:640][134]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-08.42.56:640][134]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-08.42.56:640][134]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-08.42.56:640][134]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.07.01-08.42.56:640][134]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.07.01-08.42.56:642][134]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.07.01-08.42.56:643][134]LogInit: FAudioDevice initialized with ID 2.
[2025.07.01-08.42.56:643][134]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.07.01-08.42.56:643][134]LogAudio: Display: Audio Device (ID: 2) registered with world 'Main'.
[2025.07.01-08.42.56:643][134]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.07.01-08.42.56:647][134]LogStreaming: Display: FlushAsyncLoading(330): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-08.42.56:657][134]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-08.42.56:659][134]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-16.42.56
[2025.07.01-08.42.56:659][134]LogWorld: Bringing up level for play took: 0.001957
[2025.07.01-08.42.56:662][134]LogOnline: OSS: Created online subsystem instance for: :Context_1
[2025.07.01-08.42.56:664][134]LogStaticMesh: Display: Waiting on static mesh StaticMesh /Game/Assets/Meshes/SM_TankBase.SM_TankBase being ready before playing
[2025.07.01-08.42.56:664][134]LogStaticMesh: Display: Waiting on static mesh StaticMesh /Game/Assets/Meshes/SM_TankTurret.SM_TankTurret being ready before playing
[2025.07.01-08.42.56:667][134]PIE: Server logged in
[2025.07.01-08.42.56:669][134]PIE: Play in editor total start time 0.142 seconds.
[2025.07.01-08.43.03:238][876]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.43.03:238][876]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-08.43.03:239][876]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-08.43.03:239][876]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.43.03:240][876]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-08.43.03:244][876]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.43.03:265][876]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.43.03:265][876]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.07.01-08.43.03:265][876]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.07.01-08.43.03:268][876]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.07.01-08.43.03:274][876]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.07.01-08.43.03:313][877]LogPlayLevel: Display: Destroying online subsystem :Context_1
[2025.07.01-08.43.06:226][207]LogSlate: Took 0.000241 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.07.01-08.43.07:841][371]LogStreaming: Display: FlushAsyncLoading(331): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-08.43.07:843][371]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/PlayerController/BP_ToonTanksPlayerController.BP_ToonTanksPlayerController
[2025.07.01-08.43.07:844][371]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.07.01-08.43.18:741][520]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode
[2025.07.01-08.43.18:742][520]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.07.01-08.43.29:738][717]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.07.01-08.43.30:466][796]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-08.43.30:529][796]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-08.43.30:529][796]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-08.43.30:529][796]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-08.43.30:532][796]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-08.43.30:533][796]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameModeCA70741D41593D156C86879537449582.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-08.43.30:542][796]LogFileHelpers: InternalPromptForCheckoutAndSave took 76.066 ms
[2025.07.01-08.43.30:593][796]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-08.43.30:593][796]LogContentValidation: Enabled validators:
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-08.43.30:593][796]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-08.43.30:593][796]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-08.43.32:410][975]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-08.43.32:417][975]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-08.43.32:438][975]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-08.43.32:443][975]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005768s)
[2025.07.01-08.43.32:443][975]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005819s)
[2025.07.01-08.43.32:463][975]LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
[2025.07.01-08.43.32:464][975]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-08.43.32:466][975]LogPlayLevel: PIE: World Init took: (0.001868s)
[2025.07.01-08.43.32:467][975]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-08.43.32:467][975]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-08.43.32:467][975]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-08.43.32:467][975]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-08.43.32:467][975]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-08.43.32:467][975]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-08.43.32:470][975]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-08.43.32:495][975]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.07.01-08.43.32:495][975]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-08.43.32:495][975]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-08.43.32:495][975]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-08.43.32:496][975]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.07.01-08.43.32:496][975]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.07.01-08.43.32:498][975]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.07.01-08.43.32:498][975]LogInit: FAudioDevice initialized with ID 3.
[2025.07.01-08.43.32:498][975]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.07.01-08.43.32:498][975]LogAudio: Display: Audio Device (ID: 3) registered with world 'Main'.
[2025.07.01-08.43.32:498][975]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.07.01-08.43.32:502][975]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-08.43.32:504][975]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-16.43.32
[2025.07.01-08.43.32:505][975]LogWorld: Bringing up level for play took: 0.001833
[2025.07.01-08.43.32:507][975]LogOnline: OSS: Created online subsystem instance for: :Context_4
[2025.07.01-08.43.32:510][975]PIE: Server logged in
[2025.07.01-08.43.32:512][975]PIE: Play in editor total start time 0.095 seconds.
[2025.07.01-08.43.39:723][751]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.07.01-08.43.43:144][112]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-08.43.43:294][128]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-08.43.43:661][167]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.07.01-08.43.43:829][185]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.07.01-08.43.44:011][204]LogTemp: Warning: Tower Destroyed
[2025.07.01-08.43.45:591][374]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.07.01-08.43.45:714][387]LogTemp: Warning: BP_PawnTurret_C_6 Health: 0.000000
[2025.07.01-08.43.46:627][460]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-08.43.46:769][473]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-08.43.50:978][834]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.43.50:978][834]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-08.43.50:979][834]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-08.43.50:979][834]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.43.50:980][834]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-08.43.50:985][834]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.43.51:006][834]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-08.43.51:006][834]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.07.01-08.43.51:006][834]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=4
[2025.07.01-08.43.51:008][834]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=2
[2025.07.01-08.43.51:014][834]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.07.01-08.43.51:104][835]LogPlayLevel: Display: Destroying online subsystem :Context_4
[2025.07.01-08.44.44:842][ 92]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-08.48.41:243][801]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 352.366913
[2025.07.01-08.48.42:243][804]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-08.48.42:243][804]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 353.033447, Update Interval: 302.098450
[2025.07.01-08.49.47:922][  1]LogHotReload: New module detected: UnrealEditor-ToonTank-0001.dll
[2025.07.01-08.49.48:264][  2]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-08.49.48:338][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.98ms
[2025.07.01-08.49.48:367][  2]LogClass: UPackage /Script/ToonTank Reload.
[2025.07.01-08.49.48:367][  2]LogClass: UClass ToonTanksGameMode Reload.
[2025.07.01-08.49.48:376][  2]LogClass: Could not find existing class ToonTanksGameMode in package /Script/ToonTank for reload, assuming new or modified class
[2025.07.01-08.49.48:397][  2]Re-instancing ToonTanksGameMode after reload.
[2025.07.01-08.49.48:524][  2]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.07.01-08.49.48:561][  2]LogStreaming: Display: FlushAsyncLoading(526): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-08.49.48:563][  2]Display: HotReload took  0.3s.
[2025.07.01-08.49.48:563][  2]Display: Reload/Re-instancing Complete: 1 package changed, 1 class changed, 6 classes unchanged, 2 functions remapped
[2025.07.01-08.49.56:229][454]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.07.01-08.49.56:229][454]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-08.49.56:252][454]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode
[2025.07.01-08.49.56:252][454]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.07.01-08.49.56:286][454]LogStreaming: Display: FlushAsyncLoading(527): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-08.49.57:681][454]LogUObjectHash: Compacting FUObjectHashTables data took   1.16ms
[2025.07.01-08.49.57:686][454]LogSlate: Took 0.000297 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.07.01-08.50.43:893][499]LogHotReload: New module detected: UnrealEditor-ToonTank-0002.dll
[2025.07.01-08.50.44:231][500]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-08.50.44:295][500]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-08.50.44:488][500]LogUObjectHash: Compacting FUObjectHashTables data took   2.33ms
[2025.07.01-08.50.44:546][500]Display: HotReload took  0.3s.
[2025.07.01-08.50.44:546][500]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.01-08.52.05:538][860]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.07.01-08.52.08:277][157]LogContentBrowser: Deferred new asset folder creation: NewFolder
[2025.07.01-08.52.08:283][157]LogContentBrowser: Creating deferred item: NewFolder
[2025.07.01-08.52.08:306][158]LogContentBrowser: Renaming the item being created (Deferred Item: NewFolder).
[2025.07.01-08.52.09:298][276]LogContentBrowser: Attempting asset rename: NewFolder -> Widget
[2025.07.01-08.52.09:299][276]LogContentBrowser: End creating deferred item NewFolder
[2025.07.01-08.52.53:031][884]LogSlate: Window 'Pick Parent Class' being destroyed
[2025.07.01-08.53.47:608][733]LogSlate: Window 'Pick Parent Class for New Widget Blueprint' being destroyed
[2025.07.01-08.53.47:641][733]LogContentBrowser: Deferred new asset file creation: NewWidgetBlueprint
[2025.07.01-08.53.47:647][733]LogContentBrowser: Creating deferred item: NewWidgetBlueprint
[2025.07.01-08.53.47:668][735]LogContentBrowser: Renaming the item being created (Deferred Item: NewWidgetBlueprint).
[2025.07.01-08.53.49:780][988]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 660.902344
[2025.07.01-08.53.50:064][ 22]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-08.53.50:064][ 22]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 661.177856, Update Interval: 330.953094
[2025.07.01-08.54.00:784][153]LogContentBrowser: Attempting asset rename: NewWidgetBlueprint -> WBP_StartGameWidget
[2025.07.01-08.54.00:819][153]LogContentBrowser: End creating deferred item NewWidgetBlueprint
[2025.07.01-08.54.02:478][336]LogUObjectHash: Compacting FUObjectHashTables data took   2.08ms
[2025.07.01-08.54.02:484][336]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-08.54.02:547][336]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-08.54.02:547][336]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-08.54.02:547][336]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-08.54.02:551][336]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-08.54.02:551][336]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameModeEAC8EAC74AD4481A35D92E9B90FC810E.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-08.54.02:552][336]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-08.54.02:559][336]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-08.54.02:559][336]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-08.54.02:559][336]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-08.54.02:562][336]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-08.54.02:562][336]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget8B04FC50455975405792A9A1310C43F9.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-08.54.02:574][336]LogFileHelpers: InternalPromptForCheckoutAndSave took 89.338 ms (total: 165.405 ms)
[2025.07.01-08.54.02:629][336]LogContentValidation: Display: Starting to validate 2 assets
[2025.07.01-08.54.02:629][336]LogContentValidation: Enabled validators:
[2025.07.01-08.54.02:629][336]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-08.54.02:629][336]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-08.54.02:629][336]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-08.54.02:629][336]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-08.54.02:630][336]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-08.54.02:630][336]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-08.54.02:630][336]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-08.54.02:630][336]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-08.54.02:646][336]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.07.01-08.54.03:477][415]LogAssetEditorSubsystem: Opening Asset editor for WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget
[2025.07.01-08.54.03:478][415]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.07.01-08.54.03:484][415]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.07.01-08.54.03:495][415]LogSlate: Warning: The command 'UMGEditor.OpenAnimDrawer' has the same default chord as 'EditorViewport.Next' [Ctrl+Shift+Space Bar]
[2025.07.01-08.54.04:297][415]LogSlate: Took 0.000200 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.07.01-08.54.08:195][850]LogSlate: Window 'WBP_StartGameWidget' being destroyed
[2025.07.01-08.54.09:540][980]LogSlate: Window 'WBP_StartGameWidget' being destroyed
[2025.07.01-08.55.29:579][ 90]LogUObjectHash: Compacting FUObjectHashTables data took   1.57ms
[2025.07.01-08.55.30:490][147]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-08.55.30:549][147]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-08.55.30:554][147]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-08.55.30:554][147]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-08.55.30:554][147]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-08.55.30:558][147]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-08.55.30:558][147]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget21C706DC4E920E61156B309F409C106F.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-08.55.30:582][147]LogFileHelpers: InternalPromptForCheckoutAndSave took 91.659 ms (total: 257.064 ms)
[2025.07.01-08.55.30:632][147]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-08.55.30:632][147]LogContentValidation: Enabled validators:
[2025.07.01-08.55.30:632][147]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-08.55.30:632][147]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-08.55.30:632][147]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-08.55.30:632][147]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-08.55.30:633][147]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-08.55.30:633][147]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-08.55.30:633][147]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-08.56.49:296][ 33]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.07.01-08.56.49:302][ 33]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.038
[2025.07.01-08.56.49:303][ 33]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-08.56.49:308][ 33]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-08.56.49:308][ 33]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-08.56.49:311][ 33]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto1
[2025.07.01-08.56.49:311][ 33]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget_Auto17101040F4082BE45CACD0DB32271AAF7.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto1.uasset'
[2025.07.01-08.56.49:312][ 33]LogFileHelpers: Auto-saving content packages took 0.009
[2025.07.01-08.58.40:680][368]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.07.01-08.58.40:680][368]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.07.01-08.58.40:716][368]LogAudioMixer: Warning: FMixerPlatformXAudio2::OnCriticalError: 0x88880001: UNKNOWN
[2025.07.01-08.58.40:901][368]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}]
[2025.07.01-08.58.40:901][368]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.07.01-08.58.40:901][368]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.07.01-08.58.41:236][369]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=48000, DeviceID={0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}, Name=Speakers (Realtek(R) Audio)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=163.81
[2025.07.01-08.58.41:236][369]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.07.01-08.58.41:236][369]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.07.01-08.58.41:238][369]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.07.01-08.59.26:252][504]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 997.375000
[2025.07.01-08.59.27:252][507]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-08.59.27:253][507]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 998.046082, Update Interval: 315.811646
[2025.07.01-09.04.57:003][496]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1328.126099
[2025.07.01-09.04.58:005][499]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.04.58:005][499]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1328.796997, Update Interval: 352.104248
[2025.07.01-09.11.07:763][608]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1698.883057
[2025.07.01-09.11.08:764][611]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.11.08:764][611]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1699.550659, Update Interval: 304.385498
[2025.07.01-09.17.01:514][669]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2052.635986
[2025.07.01-09.17.02:514][672]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.17.02:514][672]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2053.303955, Update Interval: 340.562744
[2025.07.01-09.23.31:603][839]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2442.724365
[2025.07.01-09.23.32:603][842]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.23.32:603][842]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2443.392578, Update Interval: 308.219849
[2025.07.01-09.29.26:358][903]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2797.478760
[2025.07.01-09.29.27:361][906]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.29.27:361][906]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2798.147705, Update Interval: 319.190033
[2025.07.01-09.35.39:123][ 21]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3170.242188
[2025.07.01-09.35.40:122][ 24]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.35.40:122][ 24]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3170.910156, Update Interval: 346.546814
[2025.07.01-09.42.14:222][206]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3565.346191
[2025.07.01-09.42.15:222][209]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.42.15:222][209]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3566.014160, Update Interval: 342.534851
[2025.07.01-09.44.44:842][658]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-09.48.55:650][410]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3966.778320
[2025.07.01-09.48.56:651][413]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.48.56:651][413]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3967.445068, Update Interval: 323.480347
[2025.07.01-09.55.13:738][544]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4344.862793
[2025.07.01-09.55.14:737][547]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-09.55.14:737][547]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4345.530273, Update Interval: 326.302063
[2025.07.01-10.01.22:961][651]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4714.091797
[2025.07.01-10.01.23:961][654]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.01.23:961][654]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4714.759766, Update Interval: 308.256470
[2025.07.01-10.07.06:065][680]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5057.203125
[2025.07.01-10.07.07:066][683]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.07.07:066][683]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5057.871094, Update Interval: 304.445923
[2025.07.01-10.13.03:493][752]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5414.637207
[2025.07.01-10.13.04:493][755]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.13.04:493][755]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5415.304688, Update Interval: 344.016235
[2025.07.01-10.19.30:245][912]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5801.391602
[2025.07.01-10.19.31:246][915]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.19.31:246][915]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5802.059570, Update Interval: 343.248993
[2025.07.01-10.25.32:115][238]LogStreaming: Display: FlushAsyncLoading(532): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-10.25.46:482][ 60]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6177.645020
[2025.07.01-10.25.47:484][ 63]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.25.47:484][ 63]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6178.311035, Update Interval: 349.518738
[2025.07.01-10.26.04:896][115]LogUObjectHash: Compacting FUObjectHashTables data took   1.64ms
[2025.07.01-10.26.04:901][115]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.038
[2025.07.01-10.26.04:901][115]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-10.26.04:904][115]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-10.26.04:904][115]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-10.26.04:908][115]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto2
[2025.07.01-10.26.04:908][115]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget_Auto271D013264E01AB4607F135875AC524EC.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto2.uasset'
[2025.07.01-10.26.04:909][115]LogFileHelpers: Auto-saving content packages took 0.008
[2025.07.01-10.26.53:558][987]LogEditorTransaction: Undo Edit Bottom
[2025.07.01-10.27.27:490][922]LogUObjectHash: Compacting FUObjectHashTables data took   1.74ms
[2025.07.01-10.27.28:346][969]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-10.27.28:398][969]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-10.27.28:402][969]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-10.27.28:402][969]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-10.27.28:402][969]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-10.27.28:406][969]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-10.27.28:406][969]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetA3F745CE4FB798A1DDCD099125CF7CC5.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-10.27.28:416][969]LogFileHelpers: InternalPromptForCheckoutAndSave took 70.055 ms (total: 327.120 ms)
[2025.07.01-10.27.28:481][969]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-10.27.28:481][969]LogContentValidation: Enabled validators:
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-10.27.28:481][969]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-10.27.28:482][969]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-10.28.27:576][711]LogUObjectHash: Compacting FUObjectHashTables data took   1.48ms
[2025.07.01-10.32.08:734][ 39]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6559.903320
[2025.07.01-10.32.09:734][ 42]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.32.09:734][ 42]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6560.573242, Update Interval: 331.756958
[2025.07.01-10.36.05:530][749]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.01-10.36.05:536][749]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.039
[2025.07.01-10.36.05:537][749]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-10.36.05:537][749]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-10.36.05:540][749]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto3
[2025.07.01-10.36.05:540][749]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto3F3AE9CD748889C42257BA782A12A4378.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto3.uasset'
[2025.07.01-10.36.05:541][749]LogFileHelpers: Auto-saving content packages took 0.005
[2025.07.01-10.38.32:490][190]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6943.689941
[2025.07.01-10.38.33:492][193]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.38.33:493][193]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6944.356934, Update Interval: 348.163696
[2025.07.01-10.44.44:843][307]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-10.45.04:574][366]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7335.798828
[2025.07.01-10.45.05:574][369]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.45.05:575][369]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7336.465820, Update Interval: 317.987000
[2025.07.01-10.50.57:308][424]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7688.554688
[2025.07.01-10.50.58:307][427]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.50.58:307][427]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7689.221680, Update Interval: 328.693512
[2025.07.01-10.57.09:393][540]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8060.661133
[2025.07.01-10.57.10:393][543]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-10.57.10:393][543]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8061.328613, Update Interval: 350.221863
[2025.07.01-11.03.53:797][753]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8465.015625
[2025.07.01-11.03.54:841][756]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.03.54:841][756]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8465.685547, Update Interval: 331.952881
[2025.07.01-11.09.59:915][851]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8831.080078
[2025.07.01-11.10.00:914][854]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.10.00:915][854]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8831.749023, Update Interval: 312.804962
[2025.07.01-11.15.48:663][897]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9179.798828
[2025.07.01-11.15.49:663][900]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.15.49:663][900]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9180.465820, Update Interval: 308.064209
[2025.07.01-11.21.46:083][969]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9537.187500
[2025.07.01-11.21.47:083][972]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.21.47:083][972]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9537.855469, Update Interval: 335.820190
[2025.07.01-11.28.16:495][140]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9927.553711
[2025.07.01-11.28.17:496][143]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.28.17:496][143]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9928.219727, Update Interval: 338.424011
[2025.07.01-11.34.32:573][268]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10303.596680
[2025.07.01-11.34.33:573][271]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.34.33:573][271]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10304.262695, Update Interval: 347.982422
[2025.07.01-11.41.07:644][453]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10698.642578
[2025.07.01-11.41.08:644][456]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.41.08:644][456]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10699.309570, Update Interval: 346.869110
[2025.07.01-11.44.44:844][105]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-11.47.35:396][616]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11086.369141
[2025.07.01-11.47.36:396][619]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.47.36:396][619]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11087.037109, Update Interval: 312.175049
[2025.07.01-11.53.44:132][722]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11455.067383
[2025.07.01-11.53.45:132][725]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.53.45:132][725]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11455.734375, Update Interval: 314.403503
[2025.07.01-11.59.39:546][788]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11810.461914
[2025.07.01-11.59.40:547][791]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-11.59.40:548][791]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11811.129883, Update Interval: 312.964264
[2025.07.01-12.05.39:958][869]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12170.852539
[2025.07.01-12.05.40:959][872]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.05.40:959][872]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12171.519531, Update Interval: 301.851257
[2025.07.01-12.11.39:364][947]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12530.239258
[2025.07.01-12.11.40:365][950]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.11.40:366][950]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12530.907227, Update Interval: 355.479004
[2025.07.01-12.18.16:771][139]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12927.606445
[2025.07.01-12.18.17:772][142]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.18.17:772][142]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12928.273438, Update Interval: 334.818573
[2025.07.01-12.24.38:185][283]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13309.008789
[2025.07.01-12.24.39:186][286]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.24.39:186][286]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13309.675781, Update Interval: 305.039215
[2025.07.01-12.30.21:251][312]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13652.061523
[2025.07.01-12.30.22:251][315]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.30.22:251][315]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13652.727539, Update Interval: 355.319672
[2025.07.01-12.36.57:677][501]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14048.472656
[2025.07.01-12.36.58:677][504]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.36.58:677][504]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14049.141602, Update Interval: 306.906952
[2025.07.01-12.42.50:630][693]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14401.388672
[2025.07.01-12.42.50:914][706]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.42.50:914][706]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14401.664062, Update Interval: 354.482849
[2025.07.01-12.43.42:785][876]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.01-12.43.42:787][876]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.035
[2025.07.01-12.43.42:788][876]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-12.43.42:788][876]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-12.43.42:792][876]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto4
[2025.07.01-12.43.42:792][876]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto4CE9548944382490A7DCC0386C16CDFF5.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto4.uasset'
[2025.07.01-12.43.42:792][876]LogFileHelpers: Auto-saving content packages took 0.005
[2025.07.01-12.44.14:915][551]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-12.44.15:754][642]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-12.44.15:802][642]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-12.44.15:802][642]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-12.44.15:802][642]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-12.44.15:807][642]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-12.44.15:807][642]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode36575FEA4FCEE4DA30BE4D95EB53D262.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-12.44.15:816][642]LogFileHelpers: InternalPromptForCheckoutAndSave took 62.402 ms (total: 389.522 ms)
[2025.07.01-12.44.15:866][642]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-12.44.15:866][642]LogContentValidation: Enabled validators:
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-12.44.15:866][642]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-12.44.15:866][642]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-12.44.16:551][718]LogUObjectHash: Compacting FUObjectHashTables data took   1.39ms
[2025.07.01-12.44.18:034][867]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.07.01-12.44.18:626][919]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-12.44.18:637][919]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-12.44.18:637][919]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-12.44.18:644][919]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006182s)
[2025.07.01-12.44.18:644][919]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006222s)
[2025.07.01-12.44.18:676][919]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-12.44.18:677][919]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-12.44.18:680][919]LogPlayLevel: PIE: World Init took: (0.002268s)
[2025.07.01-12.44.18:681][919]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-12.44.18:681][919]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-12.44.18:681][919]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-12.44.18:681][919]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-12.44.18:681][919]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-12.44.18:681][919]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-12.44.18:683][919]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-12.44.18:772][919]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-12.44.18:772][919]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-12.44.18:772][919]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-12.44.18:772][919]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-12.44.18:773][919]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.07.01-12.44.18:773][919]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.07.01-12.44.18:776][919]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.07.01-12.44.18:776][919]LogInit: FAudioDevice initialized with ID 4.
[2025.07.01-12.44.18:776][919]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.07.01-12.44.18:776][919]LogAudio: Display: Audio Device (ID: 4) registered with world 'Main'.
[2025.07.01-12.44.18:776][919]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.07.01-12.44.18:781][919]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-12.44.18:782][919]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-20.44.18
[2025.07.01-12.44.18:783][919]LogWorld: Bringing up level for play took: 0.001839
[2025.07.01-12.44.18:785][919]LogOnline: OSS: Created online subsystem instance for: :Context_9
[2025.07.01-12.44.18:789][919]PIE: Server logged in
[2025.07.01-12.44.18:792][919]PIE: Play in editor total start time 0.158 seconds.
[2025.07.01-12.44.25:473][494]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-12.44.25:473][494]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-12.44.25:473][494]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-12.44.25:474][494]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-12.44.25:476][494]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-12.44.25:484][494]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-12.44.25:515][494]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-12.44.25:515][494]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.07.01-12.44.25:515][494]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=4
[2025.07.01-12.44.25:517][494]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=2
[2025.07.01-12.44.25:526][494]LogUObjectHash: Compacting FUObjectHashTables data took   1.75ms
[2025.07.01-12.44.25:611][495]LogPlayLevel: Display: Destroying online subsystem :Context_9
[2025.07.01-12.44.44:844][688]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-12.49.36:046][124]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14806.704102
[2025.07.01-12.49.37:046][127]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.49.37:046][127]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14807.372070, Update Interval: 311.398651
[2025.07.01-12.53.49:852][885]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.01-12.53.49:856][885]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.038
[2025.07.01-12.53.49:857][885]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-12.53.49:857][885]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-12.53.49:861][885]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto5
[2025.07.01-12.53.49:861][885]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto5171BD574483CBF248EB65DB5923339BD.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto5.uasset'
[2025.07.01-12.53.49:862][885]LogFileHelpers: Auto-saving content packages took 0.005
[2025.07.01-12.55.26:452][175]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15156.964844
[2025.07.01-12.55.27:453][178]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-12.55.27:453][178]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15157.633789, Update Interval: 335.518066
[2025.07.01-13.01.58:198][350]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15548.541016
[2025.07.01-13.01.59:198][353]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.01.59:198][353]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15549.209961, Update Interval: 338.099915
[2025.07.01-13.08.23:629][506]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15933.806641
[2025.07.01-13.08.25:295][511]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.08.25:295][511]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15935.139648, Update Interval: 324.035156
[2025.07.01-13.14.33:053][614]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16303.076172
[2025.07.01-13.14.34:054][617]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.14.34:054][617]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16303.743164, Update Interval: 339.246185
[2025.07.01-13.21.04:801][789]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16695.251953
[2025.07.01-13.21.05:802][792]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.21.05:802][792]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16695.919922, Update Interval: 348.932770
[2025.07.01-13.27.42:574][982]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17093.314453
[2025.07.01-13.27.43:574][985]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.27.43:574][985]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17093.980469, Update Interval: 342.366394
[2025.07.01-13.34.21:316][178]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17492.382812
[2025.07.01-13.34.22:316][181]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.34.22:316][181]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17493.050781, Update Interval: 320.228271
[2025.07.01-13.40.29:395][282]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17860.710938
[2025.07.01-13.40.30:392][285]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.40.30:392][285]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17861.376953, Update Interval: 338.585175
[2025.07.01-13.41.07:769][397]LogHotReload: New module detected: UnrealEditor-ToonTank-0003.dll
[2025.07.01-13.41.08:442][399]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-13.41.08:536][399]LogUObjectHash: Compacting FUObjectHashTables data took   0.94ms
[2025.07.01-13.41.08:566][399]LogClass: UPackage /Script/ToonTank Reload.
[2025.07.01-13.41.08:566][399]LogClass: UClass ToonTanksGameMode Reload.
[2025.07.01-13.41.08:582][399]LogClass: Could not find existing class ToonTanksGameMode in package /Script/ToonTank for reload, assuming new or modified class
[2025.07.01-13.41.08:615][399]Re-instancing ToonTanksGameMode after reload.
[2025.07.01-13.41.08:832][399]LogUObjectHash: Compacting FUObjectHashTables data took   1.87ms
[2025.07.01-13.41.08:898][399]Display: HotReload took  0.5s.
[2025.07.01-13.41.08:899][399]Display: Reload/Re-instancing Complete: 1 package changed, 1 class changed, 6 classes unchanged, 2 functions remapped
[2025.07.01-13.42.56:235][444]LogEditorTransaction: Undo Delete current selection
[2025.07.01-13.43.30:909][922]LogUObjectHash: Compacting FUObjectHashTables data took   2.35ms
[2025.07.01-13.43.30:913][922]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.198
[2025.07.01-13.43.30:914][922]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-13.43.30:914][922]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-13.43.30:919][922]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto6
[2025.07.01-13.43.30:919][922]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto639EDE49F4A1D212246E2048EBEDE2DBF.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto6.uasset'
[2025.07.01-13.43.30:920][922]LogFileHelpers: Auto-saving content packages took 0.006
[2025.07.01-13.44.09:570][518]LogSlate: Took 0.000272 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensedItalic.ttf' (160K)
[2025.07.01-13.44.32:050][ 88]LogUObjectHash: Compacting FUObjectHashTables data took   1.53ms
[2025.07.01-13.44.44:845][466]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-13.45.53:308][536]LogUObjectHash: Compacting FUObjectHashTables data took   1.49ms
[2025.07.01-13.45.53:946][603]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.45.53:999][603]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.45.54:001][603]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.45.54:001][603]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.45.54:001][603]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.45.54:012][603]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.45.54:012][603]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget03AC8D6F409E19A589498B91606FF877.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.45.54:020][603]LogFileHelpers: InternalPromptForCheckoutAndSave took 74.628 ms (total: 464.150 ms)
[2025.07.01-13.45.54:082][603]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.45.54:082][603]LogContentValidation: Enabled validators:
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.45.54:083][603]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.45.54:083][603]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.46.08:494][554]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.46.08:504][554]LogPlayLevel: [PlayLevel] Compiling BP_ToonTanksGameMode before play...
[2025.07.01-13.46.08:565][554]LogUObjectHash: Compacting FUObjectHashTables data took   1.39ms
[2025.07.01-13.46.08:570][554]LogPlayLevel: PlayLevel: Blueprint regeneration took 66 ms (1 blueprints)
[2025.07.01-13.46.08:597][554]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.46.08:603][554]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006753s)
[2025.07.01-13.46.08:603][554]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006832s)
[2025.07.01-13.46.08:648][554]LogUObjectHash: Compacting FUObjectHashTables data took   1.35ms
[2025.07.01-13.46.08:650][554]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.46.08:652][554]LogPlayLevel: PIE: World Init took: (0.002188s)
[2025.07.01-13.46.08:653][554]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.46.08:653][554]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.46.08:653][554]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.46.08:653][554]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.46.08:653][554]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.46.08:653][554]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.46.08:657][554]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.46.08:755][554]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.46.08:756][554]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.46.08:756][554]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.46.08:756][554]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.46.08:756][554]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.07.01-13.46.08:756][554]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.07.01-13.46.08:759][554]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.07.01-13.46.08:759][554]LogInit: FAudioDevice initialized with ID 5.
[2025.07.01-13.46.08:759][554]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.07.01-13.46.08:759][554]LogAudio: Display: Audio Device (ID: 5) registered with world 'Main'.
[2025.07.01-13.46.08:759][554]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.07.01-13.46.08:764][554]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.46.08:766][554]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.46.08
[2025.07.01-13.46.08:766][554]LogWorld: Bringing up level for play took: 0.002192
[2025.07.01-13.46.08:769][554]LogOnline: OSS: Created online subsystem instance for: :Context_10
[2025.07.01-13.46.08:773][554]PIE: Server logged in
[2025.07.01-13.46.08:776][554]PIE: Play in editor total start time 0.273 seconds.
[2025.07.01-13.46.08:787][554]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.875
[2025.07.01-13.46.08:840][555]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.837523
[2025.07.01-13.46.08:943][556]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.718392
[2025.07.01-13.46.08:994][557]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.666543
[2025.07.01-13.46.09:006][558]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.65561
[2025.07.01-13.46.09:018][559]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.643629
[2025.07.01-13.46.09:028][560]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.631463
[2025.07.01-13.46.09:037][561]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.623101
[2025.07.01-13.46.09:077][562]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.584705
[2025.07.01-13.46.09:088][563]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.573015
[2025.07.01-13.46.09:099][564]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.561294
[2025.07.01-13.46.09:111][565]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.549682
[2025.07.01-13.46.09:123][566]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.538241
[2025.07.01-13.46.09:134][567]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.526629
[2025.07.01-13.46.09:145][568]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.515721
[2025.07.01-13.46.09:155][569]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.505791
[2025.07.01-13.46.09:165][570]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.495083
[2025.07.01-13.46.09:177][571]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.484212
[2025.07.01-13.46.09:191][572]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.470815
[2025.07.01-13.46.09:204][573]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.458468
[2025.07.01-13.46.09:216][574]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.445582
[2025.07.01-13.46.09:228][575]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.433534
[2025.07.01-13.46.09:240][576]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.421833
[2025.07.01-13.46.09:252][577]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.410395
[2025.07.01-13.46.09:264][578]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.397958
[2025.07.01-13.46.09:276][579]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.386157
[2025.07.01-13.46.09:288][580]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.373777
[2025.07.01-13.46.09:300][581]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.361149
[2025.07.01-13.46.09:313][582]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.348349
[2025.07.01-13.46.09:326][583]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.336365
[2025.07.01-13.46.09:337][584]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.3241
[2025.07.01-13.46.09:349][585]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.311833
[2025.07.01-13.46.09:361][586]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.299991
[2025.07.01-13.46.09:374][587]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.288018
[2025.07.01-13.46.09:387][588]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.27448
[2025.07.01-13.46.09:402][589]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.259974
[2025.07.01-13.46.09:415][590]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.245918
[2025.07.01-13.46.09:427][591]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.233936
[2025.07.01-13.46.09:440][592]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.221787
[2025.07.01-13.46.09:451][593]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.209739
[2025.07.01-13.46.09:464][594]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.19741
[2025.07.01-13.46.09:475][595]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.185846
[2025.07.01-13.46.09:486][596]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.174489
[2025.07.01-13.46.09:498][597]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.163379
[2025.07.01-13.46.09:509][598]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.152483
[2025.07.01-13.46.09:523][599]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.140121
[2025.07.01-13.46.09:537][600]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.125517
[2025.07.01-13.46.09:553][601]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.110561
[2025.07.01-13.46.09:565][602]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.096181
[2025.07.01-13.46.09:576][603]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.086413
[2025.07.01-13.46.09:586][604]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.074592
[2025.07.01-13.46.09:597][605]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.063868
[2025.07.01-13.46.09:608][606]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.053006
[2025.07.01-13.46.09:619][607]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.041548
[2025.07.01-13.46.09:629][608]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.030548
[2025.07.01-13.46.09:641][609]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.020956
[2025.07.01-13.46.09:651][610]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2.009758
[2025.07.01-13.46.09:661][611]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.999288
[2025.07.01-13.46.09:698][612]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.962849
[2025.07.01-13.46.09:709][613]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.952218
[2025.07.01-13.46.09:720][614]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.94093
[2025.07.01-13.46.09:731][615]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.930078
[2025.07.01-13.46.09:742][616]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.919555
[2025.07.01-13.46.09:753][617]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.908019
[2025.07.01-13.46.09:764][618]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.897174
[2025.07.01-13.46.09:775][619]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.885961
[2025.07.01-13.46.09:786][620]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.875049
[2025.07.01-13.46.09:797][621]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.864183
[2025.07.01-13.46.09:807][622]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.853611
[2025.07.01-13.46.09:819][623]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.842098
[2025.07.01-13.46.09:830][624]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.830821
[2025.07.01-13.46.09:842][625]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.819788
[2025.07.01-13.46.09:853][626]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.808212
[2025.07.01-13.46.09:864][627]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.796477
[2025.07.01-13.46.09:876][628]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.785768
[2025.07.01-13.46.09:887][629]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.77439
[2025.07.01-13.46.09:898][630]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.763154
[2025.07.01-13.46.09:910][631]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.752428
[2025.07.01-13.46.09:921][632]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.740749
[2025.07.01-13.46.09:932][633]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.72901
[2025.07.01-13.46.09:943][634]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.71816
[2025.07.01-13.46.09:954][635]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.706697
[2025.07.01-13.46.09:966][636]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.695667
[2025.07.01-13.46.09:977][637]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.684581
[2025.07.01-13.46.09:987][638]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.673597
[2025.07.01-13.46.09:999][639]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.662506
[2025.07.01-13.46.10:010][640]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.651173
[2025.07.01-13.46.10:022][641]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.639298
[2025.07.01-13.46.10:032][642]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.628365
[2025.07.01-13.46.10:044][643]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.617646
[2025.07.01-13.46.10:055][644]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.606231
[2025.07.01-13.46.10:066][645]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.594808
[2025.07.01-13.46.10:077][646]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.583972
[2025.07.01-13.46.10:087][647]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.573374
[2025.07.01-13.46.10:099][648]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.561472
[2025.07.01-13.46.10:110][649]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.550748
[2025.07.01-13.46.10:121][650]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.540278
[2025.07.01-13.46.10:132][651]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.528532
[2025.07.01-13.46.10:145][652]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.517168
[2025.07.01-13.46.10:156][653]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.505583
[2025.07.01-13.46.10:167][654]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.494594
[2025.07.01-13.46.10:178][655]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.483124
[2025.07.01-13.46.10:189][656]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.472033
[2025.07.01-13.46.10:200][657]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.461376
[2025.07.01-13.46.10:212][658]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.449447
[2025.07.01-13.46.10:224][659]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.437399
[2025.07.01-13.46.10:234][660]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.426284
[2025.07.01-13.46.10:245][661]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.415309
[2025.07.01-13.46.10:256][662]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.404585
[2025.07.01-13.46.10:267][663]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.392933
[2025.07.01-13.46.10:279][664]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.381807
[2025.07.01-13.46.10:290][665]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.370551
[2025.07.01-13.46.10:301][666]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.358148
[2025.07.01-13.46.10:310][667]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.34939
[2025.07.01-13.46.10:318][668]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.341225
[2025.07.01-13.46.10:327][669]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.33237
[2025.07.01-13.46.10:335][670]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.324187
[2025.07.01-13.46.10:344][671]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.315402
[2025.07.01-13.46.10:352][672]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.307278
[2025.07.01-13.46.10:361][673]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.298517
[2025.07.01-13.46.10:370][674]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.290453
[2025.07.01-13.46.10:383][675]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.277443
[2025.07.01-13.46.10:405][676]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.262983
[2025.07.01-13.46.10:416][677]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.24458
[2025.07.01-13.46.10:427][678]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.233502
[2025.07.01-13.46.10:437][679]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.222972
[2025.07.01-13.46.10:450][680]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.211991
[2025.07.01-13.46.10:461][681]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.198755
[2025.07.01-13.46.10:472][682]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.188272
[2025.07.01-13.46.10:483][683]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.177613
[2025.07.01-13.46.10:494][684]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.167291
[2025.07.01-13.46.10:505][685]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.156292
[2025.07.01-13.46.10:516][686]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.1445
[2025.07.01-13.46.10:527][687]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.133947
[2025.07.01-13.46.10:540][688]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.121832
[2025.07.01-13.46.10:552][689]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.10829
[2025.07.01-13.46.10:565][690]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.095053
[2025.07.01-13.46.10:577][691]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.083815
[2025.07.01-13.46.10:589][692]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.072313
[2025.07.01-13.46.10:600][693]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.060375
[2025.07.01-13.46.10:612][694]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.048244
[2025.07.01-13.46.10:624][695]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.036466
[2025.07.01-13.46.10:638][696]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.024443
[2025.07.01-13.46.10:652][697]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1.009756
[2025.07.01-13.46.10:665][698]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.995801
[2025.07.01-13.46.10:678][699]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.983178
[2025.07.01-13.46.10:689][700]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.971702
[2025.07.01-13.46.10:700][701]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.960007
[2025.07.01-13.46.10:712][702]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.94875
[2025.07.01-13.46.10:724][703]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.93671
[2025.07.01-13.46.10:735][704]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.925492
[2025.07.01-13.46.10:748][705]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.91302
[2025.07.01-13.46.10:760][706]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.900396
[2025.07.01-13.46.10:773][707]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.889249
[2025.07.01-13.46.10:784][708]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.87715
[2025.07.01-13.46.10:794][709]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.866649
[2025.07.01-13.46.10:806][710]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.856501
[2025.07.01-13.46.10:818][711]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.843734
[2025.07.01-13.46.10:828][712]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.832548
[2025.07.01-13.46.10:840][713]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.821407
[2025.07.01-13.46.10:851][714]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.809781
[2025.07.01-13.46.10:864][715]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.797033
[2025.07.01-13.46.10:875][716]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.785996
[2025.07.01-13.46.10:886][717]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.774729
[2025.07.01-13.46.10:898][718]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.763946
[2025.07.01-13.46.10:910][719]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.751262
[2025.07.01-13.46.10:920][720]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.740066
[2025.07.01-13.46.10:931][721]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.729255
[2025.07.01-13.46.10:942][722]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.719056
[2025.07.01-13.46.10:953][723]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.707633
[2025.07.01-13.46.10:964][724]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.696483
[2025.07.01-13.46.10:976][725]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.685931
[2025.07.01-13.46.10:987][726]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.673811
[2025.07.01-13.46.10:998][727]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.662753
[2025.07.01-13.46.11:010][728]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.652344
[2025.07.01-13.46.11:022][729]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.639311
[2025.07.01-13.46.11:033][730]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.628089
[2025.07.01-13.46.11:045][731]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.616703
[2025.07.01-13.46.11:057][732]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.605363
[2025.07.01-13.46.11:069][733]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.592768
[2025.07.01-13.46.11:082][734]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.578401
[2025.07.01-13.46.11:093][735]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.568259
[2025.07.01-13.46.11:105][736]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.557245
[2025.07.01-13.46.11:116][737]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.544989
[2025.07.01-13.46.11:139][738]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.531095
[2025.07.01-13.46.11:152][739]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.509411
[2025.07.01-13.46.11:163][740]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.498495
[2025.07.01-13.46.11:174][741]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.487208
[2025.07.01-13.46.11:185][742]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.476148
[2025.07.01-13.46.11:197][743]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.464537
[2025.07.01-13.46.11:207][744]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.453777
[2025.07.01-13.46.11:219][745]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.442487
[2025.07.01-13.46.11:231][746]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.430056
[2025.07.01-13.46.11:243][747]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.418391
[2025.07.01-13.46.11:255][748]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.406587
[2025.07.01-13.46.11:268][749]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.393399
[2025.07.01-13.46.11:280][750]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.381275
[2025.07.01-13.46.11:292][751]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.368845
[2025.07.01-13.46.11:305][752]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.356414
[2025.07.01-13.46.11:317][753]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.343414
[2025.07.01-13.46.11:329][754]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.331378
[2025.07.01-13.46.11:341][755]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.320586
[2025.07.01-13.46.11:352][756]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.30837
[2025.07.01-13.46.11:363][757]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.297168
[2025.07.01-13.46.11:390][758]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.28457
[2025.07.01-13.46.11:401][759]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.259514
[2025.07.01-13.46.11:413][760]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.247346
[2025.07.01-13.46.11:425][761]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.236992
[2025.07.01-13.46.11:435][762]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.225085
[2025.07.01-13.46.11:446][763]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.21388
[2025.07.01-13.46.11:457][764]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.203662
[2025.07.01-13.46.11:468][765]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.192754
[2025.07.01-13.46.11:481][766]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.180328
[2025.07.01-13.46.11:492][767]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.168999
[2025.07.01-13.46.11:503][768]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.158486
[2025.07.01-13.46.11:514][769]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.147492
[2025.07.01-13.46.11:525][770]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.136636
[2025.07.01-13.46.11:537][771]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.124022
[2025.07.01-13.46.11:550][772]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.111432
[2025.07.01-13.46.11:562][773]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.099276
[2025.07.01-13.46.11:573][774]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.087627
[2025.07.01-13.46.11:585][775]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.075503
[2025.07.01-13.46.11:597][776]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.064603
[2025.07.01-13.46.11:608][777]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.05242
[2025.07.01-13.46.11:617][778]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.042236
[2025.07.01-13.46.11:627][779]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.033476
[2025.07.01-13.46.11:635][780]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.024223
[2025.07.01-13.46.11:644][781]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.015584
[2025.07.01-13.46.11:653][782]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0.006994
[2025.07.01-13.46.11:662][783]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -0.002187
[2025.07.01-13.46.11:671][784]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -0.011184
[2025.07.01-13.46.11:680][785]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -0.020142
[2025.07.01-13.46.11:707][786]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.46.11:708][786]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.46.11:708][786]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.46.11:708][786]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.46.11:709][786]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.46.11:719][786]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.46.11:752][786]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.46.11:752][786]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.07.01-13.46.11:753][786]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=4
[2025.07.01-13.46.11:755][786]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=2
[2025.07.01-13.46.11:764][786]LogUObjectHash: Compacting FUObjectHashTables data took   1.49ms
[2025.07.01-13.46.11:814][787]LogPlayLevel: Display: Destroying online subsystem :Context_10
[2025.07.01-13.46.57:007][ 70]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18247.972656
[2025.07.01-13.46.57:279][102]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.46.57:280][102]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18248.234375, Update Interval: 307.386688
[2025.07.01-13.47.29:113][869]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.07.01-13.47.29:752][937]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.47.29:799][937]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.47.29:801][937]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.47.29:801][937]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.47.29:801][937]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.47.29:807][937]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.47.29:807][937]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget0D129E464DFEB7A837CA47A9E4CBB907.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.47.29:815][937]LogFileHelpers: InternalPromptForCheckoutAndSave took 62.202 ms (total: 526.353 ms)
[2025.07.01-13.47.29:865][937]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.47.29:865][937]LogContentValidation: Enabled validators:
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.47.29:865][937]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.47.29:865][937]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.47.31:379][ 83]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.47.31:388][ 83]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-13.47.31:388][ 83]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.47.31:394][ 83]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005852s)
[2025.07.01-13.47.31:394][ 83]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005894s)
[2025.07.01-13.47.31:426][ 83]LogUObjectHash: Compacting FUObjectHashTables data took   1.43ms
[2025.07.01-13.47.31:429][ 83]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.47.31:431][ 83]LogPlayLevel: PIE: World Init took: (0.002056s)
[2025.07.01-13.47.31:432][ 83]LogAudio: Display: Creating Audio Device:                 Id: 6, Scope: Unique, Realtime: True
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.47.31:432][ 83]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.47.31:432][ 83]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.47.31:432][ 83]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.47.31:432][ 83]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.47.31:432][ 83]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.47.31:434][ 83]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.47.31:528][ 83]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.47.31:528][ 83]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.47.31:528][ 83]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.47.31:528][ 83]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.47.31:529][ 83]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=6
[2025.07.01-13.47.31:529][ 83]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=6
[2025.07.01-13.47.31:531][ 83]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=6
[2025.07.01-13.47.31:531][ 83]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=6
[2025.07.01-13.47.31:531][ 83]LogInit: FAudioDevice initialized with ID 6.
[2025.07.01-13.47.31:531][ 83]LogAudio: Display: Audio Device (ID: 6) registered with world 'Main'.
[2025.07.01-13.47.31:531][ 83]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 6
[2025.07.01-13.47.31:535][ 83]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.47.31:537][ 83]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.47.31
[2025.07.01-13.47.31:538][ 83]LogWorld: Bringing up level for play took: 0.002370
[2025.07.01-13.47.31:541][ 83]LogOnline: OSS: Created online subsystem instance for: :Context_11
[2025.07.01-13.47.31:544][ 83]PIE: Server logged in
[2025.07.01-13.47.31:547][ 83]PIE: Play in editor total start time 0.159 seconds.
[2025.07.01-13.47.31:554][ 83]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:567][ 84]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:690][ 85]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:713][ 86]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:722][ 87]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:737][ 88]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:751][ 89]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:764][ 90]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:775][ 91]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:786][ 92]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:798][ 93]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:808][ 94]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:819][ 95]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:832][ 96]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:842][ 97]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:851][ 98]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:862][ 99]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:871][100]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:882][101]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:892][102]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:901][103]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:911][104]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:920][105]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:931][106]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:942][107]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:951][108]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:962][109]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:971][110]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:981][111]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.31:991][112]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:000][113]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:011][114]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:020][115]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:032][116]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:044][117]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:053][118]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:064][119]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:075][120]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:084][121]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:094][122]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:104][123]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:114][124]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:125][125]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:135][126]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:145][127]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:158][128]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:167][129]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:178][130]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:187][131]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:196][132]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:207][133]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:218][134]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:229][135]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:241][136]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:252][137]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:263][138]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:273][139]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:283][140]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:294][141]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:303][142]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:314][143]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:325][144]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:334][145]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:345][146]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:354][147]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:365][148]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:377][149]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:386][150]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:396][151]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:407][152]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:417][153]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:429][154]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 3
[2025.07.01-13.47.32:438][155]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:474][156]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:485][157]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:496][158]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:508][159]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:519][160]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:530][161]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:541][162]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:551][163]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:564][164]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:575][165]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:585][166]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:596][167]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:606][168]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:618][169]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:631][170]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:643][171]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:653][172]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:665][173]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:675][174]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:686][175]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:696][176]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:708][177]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:719][178]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:731][179]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:742][180]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:753][181]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:765][182]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:776][183]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:786][184]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:798][185]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:809][186]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:819][187]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:830][188]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:841][189]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:852][190]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:864][191]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:875][192]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:886][193]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:897][194]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:908][195]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:918][196]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:930][197]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:941][198]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:952][199]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:964][200]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:974][201]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:985][202]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.32:996][203]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:007][204]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:018][205]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:029][206]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:039][207]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:051][208]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:064][209]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:076][210]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:086][211]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:097][212]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:107][213]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:118][214]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:129][215]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:140][216]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:153][217]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:165][218]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:177][219]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:188][220]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:200][221]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:211][222]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:222][223]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:234][224]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:245][225]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:256][226]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:267][227]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:279][228]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:289][229]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:301][230]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:311][231]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:322][232]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:332][233]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:344][234]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:354][235]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:366][236]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:376][237]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:386][238]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:398][239]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:409][240]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:420][241]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.47.33:431][242]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:441][243]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:451][244]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:463][245]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:475][246]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:485][247]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:496][248]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:507][249]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:517][250]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:528][251]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:539][252]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:552][253]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:563][254]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:575][255]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:586][256]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:598][257]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:609][258]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:621][259]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:633][260]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:644][261]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:654][262]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:667][263]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:678][264]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:689][265]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:700][266]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:711][267]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:721][268]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:732][269]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:743][270]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:754][271]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:764][272]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:776][273]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:787][274]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:799][275]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:810][276]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:821][277]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:831][278]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:843][279]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:853][280]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:864][281]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:875][282]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:886][283]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:897][284]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:908][285]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:919][286]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:930][287]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:941][288]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:951][289]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:963][290]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:974][291]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:984][292]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.33:995][293]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:006][294]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:017][295]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:028][296]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:039][297]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:049][298]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:061][299]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:071][300]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:082][301]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:093][302]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:104][303]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:115][304]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:126][305]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:137][306]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:148][307]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:159][308]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:170][309]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:182][310]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:192][311]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:202][312]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:214][313]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:225][314]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:235][315]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:246][316]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:257][317]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:268][318]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:279][319]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:290][320]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:301][321]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:312][322]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:323][323]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:334][324]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:344][325]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:355][326]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:366][327]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:377][328]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:389][329]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:401][330]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:412][331]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:425][332]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.47.34:436][333]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:449][334]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:460][335]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:470][336]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:481][337]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:493][338]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:503][339]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:514][340]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:525][341]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:536][342]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:547][343]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:559][344]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:569][345]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:580][346]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:590][347]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:601][348]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:612][349]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:624][350]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:635][351]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:646][352]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:657][353]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:669][354]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:681][355]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:692][356]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:704][357]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:716][358]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:729][359]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:740][360]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:751][361]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:763][362]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:775][363]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:785][364]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:806][365]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:819][366]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:831][367]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:843][368]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:854][369]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:867][370]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:879][371]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:894][372]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:908][373]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:919][374]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:931][375]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:944][376]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:956][377]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:968][378]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:980][379]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.34:995][380]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:008][381]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:020][382]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:032][383]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:044][384]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:055][385]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:067][386]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:079][387]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:091][388]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:103][389]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:115][390]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:126][391]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:138][392]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:150][393]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:161][394]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:172][395]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:184][396]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:195][397]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:207][398]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:218][399]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:230][400]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:241][401]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:252][402]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:264][403]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:277][404]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:289][405]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:301][406]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:313][407]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:324][408]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:336][409]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:347][410]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:359][411]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:370][412]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:382][413]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:393][414]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:405][415]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:416][416]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:428][417]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 0
[2025.07.01-13.47.35:440][418]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:451][419]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:466][420]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:478][421]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:490][422]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:501][423]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:513][424]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:525][425]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:536][426]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:549][427]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:562][428]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:577][429]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:590][430]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:602][431]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:615][432]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:629][433]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:639][434]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:650][435]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:659][436]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:669][437]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:679][438]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:690][439]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] -1
[2025.07.01-13.47.35:737][440]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.47.35:737][440]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.47.35:737][440]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.47.35:738][440]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.47.35:739][440]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.47.35:750][440]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.47.35:785][440]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.47.35:786][440]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 6
[2025.07.01-13.47.35:786][440]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6, StreamState=4
[2025.07.01-13.47.35:788][440]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6, StreamState=2
[2025.07.01-13.47.35:800][440]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-13.47.36:053][441]LogPlayLevel: Display: Destroying online subsystem :Context_11
[2025.07.01-13.50.37:607][700]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.01-13.50.37:988][736]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.50.38:051][736]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.50.38:053][736]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.50.38:053][736]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.50.38:053][736]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.50.38:061][736]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.50.38:072][736]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetDC9B8B734FA109DDE077E98DD1708957.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.50.38:081][736]LogFileHelpers: InternalPromptForCheckoutAndSave took 92.645 ms (total: 618.999 ms)
[2025.07.01-13.50.38:133][736]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.50.38:133][736]LogContentValidation: Enabled validators:
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.50.38:133][736]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.50.38:134][736]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.50.39:399][846]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.50.39:407][846]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-13.50.39:407][846]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.50.39:413][846]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005742s)
[2025.07.01-13.50.39:413][846]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005782s)
[2025.07.01-13.50.39:446][846]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.07.01-13.50.39:449][846]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.50.39:451][846]LogPlayLevel: PIE: World Init took: (0.001954s)
[2025.07.01-13.50.39:451][846]LogAudio: Display: Creating Audio Device:                 Id: 7, Scope: Unique, Realtime: True
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.50.39:451][846]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.50.39:452][846]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.50.39:452][846]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.50.39:452][846]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.50.39:452][846]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.50.39:452][846]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.50.39:454][846]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.50.39:544][846]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.50.39:545][846]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.50.39:545][846]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.50.39:545][846]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.50.39:545][846]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=7
[2025.07.01-13.50.39:545][846]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=7
[2025.07.01-13.50.39:547][846]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=7
[2025.07.01-13.50.39:547][846]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=7
[2025.07.01-13.50.39:547][846]LogInit: FAudioDevice initialized with ID 7.
[2025.07.01-13.50.39:547][846]LogAudio: Display: Audio Device (ID: 7) registered with world 'Main'.
[2025.07.01-13.50.39:547][846]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 7
[2025.07.01-13.50.39:551][846]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.50.39:553][846]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.50.39
[2025.07.01-13.50.39:553][846]LogWorld: Bringing up level for play took: 0.001738
[2025.07.01-13.50.39:556][846]LogOnline: OSS: Created online subsystem instance for: :Context_12
[2025.07.01-13.50.39:559][846]PIE: Server logged in
[2025.07.01-13.50.39:562][846]PIE: Play in editor total start time 0.155 seconds.
[2025.07.01-13.50.39:572][846]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:589][847]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:697][848]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:720][849]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:731][850]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:742][851]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:754][852]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:765][853]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:777][854]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:787][855]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:799][856]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:809][857]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:820][858]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:830][859]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:840][860]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:850][861]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:860][862]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:870][863]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:881][864]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:891][865]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:901][866]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:911][867]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:921][868]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:932][869]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:942][870]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:952][871]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:963][872]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:973][873]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:982][874]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.39:993][875]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:003][876]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:013][877]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:025][878]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:036][879]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:047][880]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:057][881]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:067][882]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:078][883]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:088][884]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:097][885]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:108][886]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:118][887]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:128][888]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:138][889]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:149][890]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:159][891]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:169][892]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:180][893]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:190][894]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:200][895]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:211][896]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:221][897]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:231][898]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:242][899]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:252][900]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:262][901]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:272][902]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:282][903]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:293][904]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:303][905]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:313][906]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:323][907]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:333][908]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:343][909]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:353][910]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:363][911]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:373][912]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:384][913]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:394][914]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:404][915]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:415][916]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:425][917]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:435][918]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:445][919]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Get Ready!
[2025.07.01-13.50.40:454][920]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:490][921]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:501][922]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:513][923]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:524][924]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:534][925]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:547][926]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:559][927]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:570][928]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:580][929]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:593][930]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:605][931]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:618][932]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:631][933]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:642][934]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:654][935]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:665][936]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:676][937]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:688][938]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:700][939]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:712][940]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:723][941]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:734][942]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:747][943]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:758][944]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:769][945]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:781][946]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:792][947]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:806][948]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:818][949]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:830][950]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:840][951]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:852][952]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:864][953]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:875][954]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:886][955]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:898][956]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:910][957]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:920][958]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:933][959]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:944][960]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:954][961]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:965][962]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:977][963]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:988][964]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.40:999][965]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:010][966]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:022][967]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:035][968]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:046][969]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:056][970]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:068][971]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:079][972]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:089][973]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:100][974]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:111][975]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:122][976]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:134][977]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:145][978]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:156][979]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:168][980]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:179][981]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:191][982]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:202][983]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:214][984]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:225][985]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:236][986]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:249][987]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:261][988]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:272][989]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:283][990]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:295][991]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:306][992]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:317][993]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:329][994]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:340][995]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:352][996]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:363][997]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:375][998]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:386][999]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:397][  0]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:409][  1]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:420][  2]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:432][  3]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:443][  4]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 2
[2025.07.01-13.50.41:453][  5]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:465][  6]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:476][  7]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:488][  8]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:499][  9]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:510][ 10]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:521][ 11]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:533][ 12]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:544][ 13]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:556][ 14]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:568][ 15]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:580][ 16]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:591][ 17]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:603][ 18]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:615][ 19]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:628][ 20]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:640][ 21]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:651][ 22]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:662][ 23]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:674][ 24]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:685][ 25]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:696][ 26]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:707][ 27]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:719][ 28]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:730][ 29]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:743][ 30]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:756][ 31]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:767][ 32]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:780][ 33]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:791][ 34]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:803][ 35]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:814][ 36]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:825][ 37]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:836][ 38]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:849][ 39]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:861][ 40]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:871][ 41]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:883][ 42]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:894][ 43]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:905][ 44]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:917][ 45]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:928][ 46]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:940][ 47]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:953][ 48]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:964][ 49]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:977][ 50]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:987][ 51]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.41:999][ 52]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:011][ 53]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:023][ 54]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:034][ 55]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:048][ 56]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:059][ 57]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:070][ 58]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:082][ 59]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:094][ 60]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:106][ 61]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:117][ 62]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:129][ 63]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:142][ 64]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:153][ 65]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:165][ 66]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:178][ 67]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:190][ 68]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:201][ 69]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:213][ 70]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:225][ 71]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:237][ 72]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:248][ 73]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:260][ 74]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:271][ 75]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:283][ 76]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:294][ 77]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:304][ 78]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:315][ 79]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:326][ 80]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:336][ 81]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:348][ 82]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:359][ 83]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:370][ 84]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:381][ 85]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:392][ 86]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:405][ 87]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:416][ 88]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:428][ 89]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:438][ 90]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] 1
[2025.07.01-13.50.42:450][ 91]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:461][ 92]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:473][ 93]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:484][ 94]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:495][ 95]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:506][ 96]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:516][ 97]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:528][ 98]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:538][ 99]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:550][100]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:561][101]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:572][102]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:583][103]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:594][104]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:606][105]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:618][106]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:628][107]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:639][108]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:650][109]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:662][110]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:673][111]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:684][112]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:697][113]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:709][114]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:720][115]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:731][116]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:743][117]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:754][118]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:766][119]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:776][120]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:787][121]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:799][122]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:810][123]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:821][124]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:832][125]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:844][126]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:856][127]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:866][128]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:877][129]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:889][130]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:901][131]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:913][132]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:923][133]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:934][134]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:945][135]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:956][136]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:967][137]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:979][138]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.42:989][139]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:000][140]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:012][141]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:025][142]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:037][143]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:048][144]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:060][145]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:071][146]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:083][147]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:094][148]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:104][149]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:115][150]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:127][151]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:138][152]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:151][153]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:162][154]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:174][155]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:185][156]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:197][157]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:208][158]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:220][159]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:232][160]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:242][161]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:253][162]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:265][163]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:277][164]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:288][165]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:300][166]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:311][167]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:321][168]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:333][169]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:344][170]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:355][171]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:367][172]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:378][173]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:389][174]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:400][175]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:412][176]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:423][177]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:434][178]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.50.43:446][179]LogBlueprintUserMessages: [WBP_StartGameWidget_C_0] Go!
[2025.07.01-13.52.31:506][437]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.52.31:507][437]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.52.31:507][437]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.52.31:507][437]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.52.31:508][437]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.52.31:518][437]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.52.31:551][437]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-13.52.31:552][437]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 7
[2025.07.01-13.52.31:552][437]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7, StreamState=4
[2025.07.01-13.52.31:554][437]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7, StreamState=2
[2025.07.01-13.52.31:566][437]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.52.31:569][437]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-13.52.31:597][438]LogPlayLevel: Display: Destroying online subsystem :Context_12
[2025.07.01-13.52.32:880][586]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-13.52.33:807][682]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.52.33:866][682]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.52.33:868][682]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.52.33:868][682]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.52.33:868][682]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.52.33:876][682]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.52.33:877][682]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget07FBD8ED47DCAFB2517B24A88C7C9274.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.52.33:884][682]LogFileHelpers: InternalPromptForCheckoutAndSave took 76.938 ms (total: 695.937 ms)
[2025.07.01-13.52.33:933][682]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.52.33:933][682]LogContentValidation: Enabled validators:
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.52.33:933][682]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.52.33:933][682]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.52.39:348][232]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.52.39:464][232]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.52.39:466][232]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.52.39:466][232]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.52.39:466][232]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.52.39:473][232]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.52.39:473][232]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetD12B3AE14D28B487AEEC3389B03C11C4.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.52.39:482][232]LogFileHelpers: InternalPromptForCheckoutAndSave took 134.112 ms (total: 830.050 ms)
[2025.07.01-13.52.39:517][232]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.52.39:517][232]LogContentValidation: Enabled validators:
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.52.39:517][232]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.52.39:518][232]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.52.57:847][185]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18608.343750
[2025.07.01-13.52.58:633][277]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.52.58:633][277]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18609.085938, Update Interval: 320.015869
[2025.07.01-13.53.00:300][459]LogUObjectHash: Compacting FUObjectHashTables data took   1.41ms
[2025.07.01-13.53.01:456][581]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.53.01:501][581]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.53.01:503][581]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.53.01:503][581]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.53.01:503][581]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.53.01:510][581]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.53.01:511][581]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget9B9DFC044E07BD54CF9A1EA308F26FBD.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.53.01:530][581]LogFileHelpers: InternalPromptForCheckoutAndSave took 73.704 ms (total: 903.754 ms)
[2025.07.01-13.53.01:583][581]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.53.01:583][581]LogContentValidation: Enabled validators:
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.53.01:583][581]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.53.01:583][581]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.56.04:734][333]LogUObjectHash: Compacting FUObjectHashTables data took   1.72ms
[2025.07.01-13.56.04:744][333]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.072
[2025.07.01-13.56.04:745][333]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.56.04:748][333]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.56.04:748][333]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.56.04:757][333]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto7
[2025.07.01-13.56.04:757][333]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget_Auto79513E972484F6907871356A525E30A57.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto7.uasset'
[2025.07.01-13.56.04:758][333]LogFileHelpers: Auto-saving content packages took 0.013
[2025.07.01-13.56.09:039][806]LogUObjectHash: Compacting FUObjectHashTables data took   1.38ms
[2025.07.01-13.56.09:846][892]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.56.09:902][892]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.56.09:903][892]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.56.09:903][892]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.56.09:903][892]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.56.09:915][892]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.56.09:916][892]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetF1C541C64A7A10DBE76A12BC49AF09F0.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.56.09:925][892]LogFileHelpers: InternalPromptForCheckoutAndSave took 78.246 ms (total: 982.001 ms)
[2025.07.01-13.56.09:985][892]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.56.09:985][892]LogContentValidation: Enabled validators:
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.56.09:985][892]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.56.09:986][892]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.56.12:765][174]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.56.12:773][174]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-13.56.12:773][174]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.56.12:779][174]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006072s)
[2025.07.01-13.56.12:780][174]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006121s)
[2025.07.01-13.56.12:813][174]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.07.01-13.56.12:818][174]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.56.12:820][174]LogPlayLevel: PIE: World Init took: (0.002167s)
[2025.07.01-13.56.12:821][174]LogAudio: Display: Creating Audio Device:                 Id: 8, Scope: Unique, Realtime: True
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.56.12:821][174]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.56.12:821][174]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.56.12:821][174]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.56.12:821][174]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.56.12:821][174]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.56.12:823][174]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.56.12:917][174]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.56.12:917][174]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.56.12:917][174]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.56.12:917][174]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.56.12:917][174]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=8
[2025.07.01-13.56.12:917][174]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=8
[2025.07.01-13.56.12:919][174]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=8
[2025.07.01-13.56.12:919][174]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=8
[2025.07.01-13.56.12:919][174]LogInit: FAudioDevice initialized with ID 8.
[2025.07.01-13.56.12:919][174]LogAudio: Display: Audio Device (ID: 8) registered with world 'Main'.
[2025.07.01-13.56.12:919][174]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 8
[2025.07.01-13.56.12:924][174]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.56.12:925][174]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.56.12
[2025.07.01-13.56.12:926][174]LogWorld: Bringing up level for play took: 0.001984
[2025.07.01-13.56.12:928][174]LogOnline: OSS: Created online subsystem instance for: :Context_13
[2025.07.01-13.56.12:932][174]PIE: Server logged in
[2025.07.01-13.56.12:935][174]PIE: Play in editor total start time 0.161 seconds.
[2025.07.01-13.56.16:486][475]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.56.16:487][475]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.56.16:487][475]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.56.16:487][475]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.56.16:488][475]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.56.16:498][475]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.56.16:529][475]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-13.56.16:530][475]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 8
[2025.07.01-13.56.16:530][475]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8, StreamState=4
[2025.07.01-13.56.16:532][475]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8, StreamState=2
[2025.07.01-13.56.16:539][475]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.56.16:542][475]LogUObjectHash: Compacting FUObjectHashTables data took   1.61ms
[2025.07.01-13.56.16:591][476]LogPlayLevel: Display: Destroying online subsystem :Context_13
[2025.07.01-13.57.06:448][775]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.07.01-13.57.06:954][820]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.57.07:002][820]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.57.07:003][820]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.57.07:003][820]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.57.07:004][820]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.57.07:013][820]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.57.07:024][820]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetE5CC79AF4CFC12F6FD6DCFB5A9101B6D.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.57.07:033][820]LogFileHelpers: InternalPromptForCheckoutAndSave took 78.783 ms (total: 1.06 sec)
[2025.07.01-13.57.07:085][820]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.57.07:085][820]LogContentValidation: Enabled validators:
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.57.07:085][820]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.57.07:086][820]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.57.27:586][168]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.57.27:596][168]LogPlayLevel: [PlayLevel] Compiling WBP_StartGameWidget before play...
[2025.07.01-13.57.27:678][168]LogUObjectHash: Compacting FUObjectHashTables data took   1.44ms
[2025.07.01-13.57.27:685][168]LogPlayLevel: PlayLevel: Blueprint regeneration took 89 ms (1 blueprints)
[2025.07.01-13.57.27:685][168]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.57.27:691][168]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006047s)
[2025.07.01-13.57.27:691][168]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006092s)
[2025.07.01-13.57.27:721][168]LogUObjectHash: Compacting FUObjectHashTables data took   1.44ms
[2025.07.01-13.57.27:722][168]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.57.27:725][168]LogPlayLevel: PIE: World Init took: (0.002205s)
[2025.07.01-13.57.27:726][168]LogAudio: Display: Creating Audio Device:                 Id: 9, Scope: Unique, Realtime: True
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.57.27:726][168]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.57.27:726][168]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.57.27:726][168]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.57.27:726][168]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.57.27:726][168]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.57.27:728][168]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.57.27:812][168]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.57.27:812][168]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.57.27:812][168]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.57.27:813][168]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.57.27:813][168]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=9
[2025.07.01-13.57.27:813][168]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=9
[2025.07.01-13.57.27:815][168]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=9
[2025.07.01-13.57.27:815][168]LogInit: FAudioDevice initialized with ID 9.
[2025.07.01-13.57.27:815][168]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=9
[2025.07.01-13.57.27:815][168]LogAudio: Display: Audio Device (ID: 9) registered with world 'Main'.
[2025.07.01-13.57.27:815][168]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 9
[2025.07.01-13.57.27:867][168]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.57.27:867][168]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.57.27:869][168]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.57.27
[2025.07.01-13.57.27:870][168]LogWorld: Bringing up level for play took: 0.001952
[2025.07.01-13.57.27:872][168]LogOnline: OSS: Created online subsystem instance for: :Context_14
[2025.07.01-13.57.27:877][168]PIE: Server logged in
[2025.07.01-13.57.27:878][168]PIE: Play in editor total start time 0.283 seconds.
[2025.07.01-13.57.27:996][169]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
[2025.07.01-13.57.27:996][169]LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
[2025.07.01-13.57.33:696][675]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.57.33:696][675]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.57.33:696][675]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-13.57.33:702][675]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.57.33:702][675]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.57.33:703][675]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.57.33:714][675]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.57.33:748][675]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 9
[2025.07.01-13.57.33:748][675]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9, StreamState=4
[2025.07.01-13.57.33:750][675]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9, StreamState=2
[2025.07.01-13.57.33:759][675]LogUObjectHash: Compacting FUObjectHashTables data took   1.56ms
[2025.07.01-13.57.33:787][676]LogPlayLevel: Display: Destroying online subsystem :Context_14
[2025.07.01-13.57.42:206][673]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.07.01-13.57.42:792][726]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.57.42:850][726]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.57.42:852][726]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.57.42:852][726]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.57.42:852][726]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.57.42:860][726]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.57.42:860][726]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetE1D5F0D84B33EF8CDA0CA5A1F3B400FF.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.57.42:868][726]LogFileHelpers: InternalPromptForCheckoutAndSave took 76.317 ms (total: 1.13 sec)
[2025.07.01-13.57.42:917][726]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.57.42:917][726]LogContentValidation: Enabled validators:
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.57.42:917][726]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.57.42:918][726]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.57.44:175][846]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.57.44:183][846]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-13.57.44:183][846]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.57.44:189][846]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005635s)
[2025.07.01-13.57.44:189][846]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005668s)
[2025.07.01-13.57.44:221][846]LogUObjectHash: Compacting FUObjectHashTables data took   1.52ms
[2025.07.01-13.57.44:226][846]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.57.44:228][846]LogPlayLevel: PIE: World Init took: (0.002227s)
[2025.07.01-13.57.44:229][846]LogAudio: Display: Creating Audio Device:                 Id: 10, Scope: Unique, Realtime: True
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.57.44:229][846]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.57.44:229][846]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.57.44:229][846]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.57.44:229][846]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.57.44:229][846]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.57.44:232][846]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.57.44:319][846]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.57.44:319][846]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.57.44:319][846]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.57.44:319][846]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.57.44:320][846]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=10
[2025.07.01-13.57.44:320][846]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=10
[2025.07.01-13.57.44:322][846]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=10
[2025.07.01-13.57.44:322][846]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=10
[2025.07.01-13.57.44:322][846]LogInit: FAudioDevice initialized with ID 10.
[2025.07.01-13.57.44:322][846]LogAudio: Display: Audio Device (ID: 10) registered with world 'Main'.
[2025.07.01-13.57.44:322][846]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 10
[2025.07.01-13.57.44:327][846]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.57.44:328][846]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.57.44
[2025.07.01-13.57.44:329][846]LogWorld: Bringing up level for play took: 0.001777
[2025.07.01-13.57.44:331][846]LogOnline: OSS: Created online subsystem instance for: :Context_15
[2025.07.01-13.57.44:334][846]PIE: Server logged in
[2025.07.01-13.57.44:337][846]PIE: Play in editor total start time 0.154 seconds.
[2025.07.01-13.57.49:806][335]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.57.49:806][335]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.57.49:806][335]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.57.49:806][335]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.57.49:808][335]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.57.49:818][335]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.57.49:848][335]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.57.49:849][335]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 10
[2025.07.01-13.57.49:849][335]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10, StreamState=4
[2025.07.01-13.57.49:851][335]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10, StreamState=2
[2025.07.01-13.57.49:863][335]LogUObjectHash: Compacting FUObjectHashTables data took   1.51ms
[2025.07.01-13.57.49:914][336]LogPlayLevel: Display: Destroying online subsystem :Context_15
[2025.07.01-13.58.10:729][140]LogUObjectHash: Compacting FUObjectHashTables data took   1.41ms
[2025.07.01-13.58.11:338][202]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.58.11:384][202]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.58.11:386][202]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.58.11:386][202]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.58.11:386][202]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.58.11:394][202]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.58.11:395][202]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget440D488C42EEEEA240604485F2EB1283.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.58.11:402][202]LogFileHelpers: InternalPromptForCheckoutAndSave took 63.576 ms (total: 1.20 sec)
[2025.07.01-13.58.11:451][202]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.58.11:451][202]LogContentValidation: Enabled validators:
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.58.11:451][202]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.58.11:451][202]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.58.12:838][338]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-13.58.12:847][338]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-13.58.12:847][338]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.58.12:852][338]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005546s)
[2025.07.01-13.58.12:852][338]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005579s)
[2025.07.01-13.58.12:885][338]LogUObjectHash: Compacting FUObjectHashTables data took   1.37ms
[2025.07.01-13.58.12:889][338]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-13.58.12:891][338]LogPlayLevel: PIE: World Init took: (0.002131s)
[2025.07.01-13.58.12:892][338]LogAudio: Display: Creating Audio Device:                 Id: 11, Scope: Unique, Realtime: True
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-13.58.12:892][338]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-13.58.12:892][338]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-13.58.12:892][338]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-13.58.12:892][338]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-13.58.12:892][338]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-13.58.12:894][338]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-13.58.12:980][338]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-13.58.12:980][338]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-13.58.12:980][338]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-13.58.12:980][338]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-13.58.12:980][338]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=11
[2025.07.01-13.58.12:980][338]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=11
[2025.07.01-13.58.12:983][338]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=11
[2025.07.01-13.58.12:983][338]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=11
[2025.07.01-13.58.12:983][338]LogInit: FAudioDevice initialized with ID 11.
[2025.07.01-13.58.12:983][338]LogAudio: Display: Audio Device (ID: 11) registered with world 'Main'.
[2025.07.01-13.58.12:983][338]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 11
[2025.07.01-13.58.12:987][338]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-13.58.12:989][338]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-21.58.12
[2025.07.01-13.58.12:989][338]LogWorld: Bringing up level for play took: 0.001735
[2025.07.01-13.58.12:992][338]LogOnline: OSS: Created online subsystem instance for: :Context_16
[2025.07.01-13.58.12:995][338]PIE: Server logged in
[2025.07.01-13.58.12:997][338]PIE: Play in editor total start time 0.151 seconds.
[2025.07.01-13.58.20:296][990]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.58.20:296][990]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-13.58.20:296][990]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-13.58.20:296][990]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.58.20:297][990]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-13.58.20:307][990]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-13.58.20:340][990]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-13.58.20:340][990]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 11
[2025.07.01-13.58.20:340][990]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11, StreamState=4
[2025.07.01-13.58.20:342][990]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11, StreamState=2
[2025.07.01-13.58.20:349][990]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-13.58.20:352][990]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.07.01-13.58.20:399][991]LogPlayLevel: Display: Destroying online subsystem :Context_16
[2025.07.01-13.58.21:725][135]LogEditorTransaction: Undo Break Pin Links
[2025.07.01-13.58.23:580][296]LogUObjectHash: Compacting FUObjectHashTables data took   1.43ms
[2025.07.01-13.58.24:155][353]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-13.58.24:220][353]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-13.58.24:223][353]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-13.58.24:223][353]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-13.58.24:223][353]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-13.58.24:231][353]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-13.58.24:232][353]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget2925A47C4DE314FF10430AB81536E1EF.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-13.58.24:244][353]LogFileHelpers: InternalPromptForCheckoutAndSave took 88.644 ms (total: 1.28 sec)
[2025.07.01-13.58.24:302][353]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-13.58.24:302][353]LogContentValidation: Enabled validators:
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-13.58.24:302][353]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-13.58.24:303][353]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-13.59.13:454][342]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18982.673828
[2025.07.01-13.59.14:454][345]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-13.59.14:454][345]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18983.341797, Update Interval: 348.692902
[2025.07.01-14.03.22:108][ 88]LogWindowsTextInputMethodSystem: Activated input method: Chinese (Traditional, Taiwan) - (Keyboard).
[2025.07.01-14.03.22:108][ 88]LogWindowsTextInputMethodSystem: Activated input method: Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.07.01-14.03.37:662][491]LogUObjectHash: Compacting FUObjectHashTables data took   1.59ms
[2025.07.01-14.03.38:086][492]LogUObjectHash: Compacting FUObjectHashTables data took   1.41ms
[2025.07.01-14.03.40:649][540]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.03.40:657][540]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.03.40:658][540]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.03.40:664][540]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006276s)
[2025.07.01-14.03.40:664][540]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006320s)
[2025.07.01-14.03.40:697][540]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-14.03.40:701][540]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.03.40:703][540]LogPlayLevel: PIE: World Init took: (0.002144s)
[2025.07.01-14.03.40:704][540]LogAudio: Display: Creating Audio Device:                 Id: 12, Scope: Unique, Realtime: True
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.03.40:704][540]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.03.40:704][540]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.03.40:704][540]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.03.40:704][540]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.03.40:704][540]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.03.40:706][540]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.03.40:795][540]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.03.40:795][540]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.03.40:796][540]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.03.40:796][540]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.03.40:796][540]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=12
[2025.07.01-14.03.40:796][540]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=12
[2025.07.01-14.03.40:798][540]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=12
[2025.07.01-14.03.40:798][540]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=12
[2025.07.01-14.03.40:798][540]LogInit: FAudioDevice initialized with ID 12.
[2025.07.01-14.03.40:798][540]LogAudio: Display: Audio Device (ID: 12) registered with world 'Main'.
[2025.07.01-14.03.40:798][540]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 12
[2025.07.01-14.03.40:854][540]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.03.40:854][540]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.03.40:856][540]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.03.40
[2025.07.01-14.03.40:857][540]LogWorld: Bringing up level for play took: 0.002101
[2025.07.01-14.03.40:859][540]LogOnline: OSS: Created online subsystem instance for: :Context_17
[2025.07.01-14.03.40:866][540]PIE: Server logged in
[2025.07.01-14.03.40:867][540]PIE: Play in editor total start time 0.21 seconds.
[2025.07.01-14.03.43:706][707]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.03.43:706][707]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.03.43:706][707]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-14.03.43:709][707]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.03.43:709][707]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.03.43:710][707]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.03.43:721][707]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.03.43:755][707]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 12
[2025.07.01-14.03.43:755][707]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=12, StreamState=4
[2025.07.01-14.03.43:757][707]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=12, StreamState=2
[2025.07.01-14.03.43:766][707]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.07.01-14.03.43:798][708]LogPlayLevel: Display: Destroying online subsystem :Context_17
[2025.07.01-14.04.08:131][925]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.04.08:140][925]LogPlayLevel: [PlayLevel] Compiling WBP_StartGameWidget before play...
[2025.07.01-14.04.08:326][925]LogUObjectHash: Compacting FUObjectHashTables data took   1.44ms
[2025.07.01-14.04.08:328][925]LogPlayLevel: PlayLevel: Blueprint regeneration took 188 ms (1 blueprints)
[2025.07.01-14.04.08:328][925]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.04.08:334][925]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006007s)
[2025.07.01-14.04.08:334][925]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006051s)
[2025.07.01-14.04.08:371][925]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-14.04.08:373][925]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.04.08:375][925]LogPlayLevel: PIE: World Init took: (0.002160s)
[2025.07.01-14.04.08:376][925]LogAudio: Display: Creating Audio Device:                 Id: 13, Scope: Unique, Realtime: True
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.04.08:376][925]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.04.08:376][925]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.04.08:376][925]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.04.08:376][925]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.04.08:376][925]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.04.08:378][925]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.04.08:467][925]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.04.08:467][925]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.04.08:467][925]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.04.08:468][925]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.04.08:468][925]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=13
[2025.07.01-14.04.08:468][925]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=13
[2025.07.01-14.04.08:471][925]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=13
[2025.07.01-14.04.08:471][925]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=13
[2025.07.01-14.04.08:471][925]LogInit: FAudioDevice initialized with ID 13.
[2025.07.01-14.04.08:471][925]LogAudio: Display: Audio Device (ID: 13) registered with world 'Main'.
[2025.07.01-14.04.08:471][925]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 13
[2025.07.01-14.04.08:521][925]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.04.08:521][925]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.04.08:523][925]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.04.08
[2025.07.01-14.04.08:523][925]LogWorld: Bringing up level for play took: 0.002009
[2025.07.01-14.04.08:526][925]LogOnline: OSS: Created online subsystem instance for: :Context_18
[2025.07.01-14.04.08:533][925]PIE: Server logged in
[2025.07.01-14.04.08:534][925]PIE: Play in editor total start time 0.395 seconds.
[2025.07.01-14.04.14:375][335]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.04.14:375][335]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.04.14:375][335]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-14.04.14:378][335]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.04.14:378][335]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.04.14:379][335]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.04.14:389][335]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.04.14:422][335]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 13
[2025.07.01-14.04.14:422][335]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=13, StreamState=4
[2025.07.01-14.04.14:425][335]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=13, StreamState=2
[2025.07.01-14.04.14:434][335]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-14.04.14:469][336]LogPlayLevel: Display: Destroying online subsystem :Context_18
[2025.07.01-14.04.21:154][ 24]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.04.21:163][ 24]LogPlayLevel: [PlayLevel] Compiling WBP_StartGameWidget before play...
[2025.07.01-14.04.21:240][ 24]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-14.04.21:245][ 24]LogPlayLevel: PlayLevel: Blueprint regeneration took 83 ms (1 blueprints)
[2025.07.01-14.04.21:245][ 24]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.04.21:251][ 24]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005720s)
[2025.07.01-14.04.21:251][ 24]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005762s)
[2025.07.01-14.04.21:282][ 24]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.07.01-14.04.21:284][ 24]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.04.21:286][ 24]LogPlayLevel: PIE: World Init took: (0.002236s)
[2025.07.01-14.04.21:287][ 24]LogAudio: Display: Creating Audio Device:                 Id: 14, Scope: Unique, Realtime: True
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.04.21:287][ 24]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.04.21:287][ 24]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.04.21:287][ 24]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.04.21:287][ 24]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.04.21:287][ 24]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.04.21:289][ 24]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.04.21:376][ 24]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.04.21:376][ 24]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.04.21:376][ 24]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.04.21:376][ 24]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.04.21:377][ 24]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=14
[2025.07.01-14.04.21:377][ 24]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=14
[2025.07.01-14.04.21:379][ 24]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=14
[2025.07.01-14.04.21:379][ 24]LogInit: FAudioDevice initialized with ID 14.
[2025.07.01-14.04.21:379][ 24]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=14
[2025.07.01-14.04.21:380][ 24]LogAudio: Display: Audio Device (ID: 14) registered with world 'Main'.
[2025.07.01-14.04.21:380][ 24]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 14
[2025.07.01-14.04.21:419][ 24]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.04.21:419][ 24]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.04.21:420][ 24]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.04.21
[2025.07.01-14.04.21:421][ 24]LogWorld: Bringing up level for play took: 0.002094
[2025.07.01-14.04.21:424][ 24]LogOnline: OSS: Created online subsystem instance for: :Context_19
[2025.07.01-14.04.21:428][ 24]PIE: Server logged in
[2025.07.01-14.04.21:429][ 24]PIE: Play in editor total start time 0.268 seconds.
[2025.07.01-14.04.26:475][493]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.04.26:475][493]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.04.26:475][493]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-14.04.26:479][493]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.04.26:479][493]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.04.26:480][493]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.04.26:490][493]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.04.26:523][493]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 14
[2025.07.01-14.04.26:523][493]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=14, StreamState=4
[2025.07.01-14.04.26:525][493]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=14, StreamState=2
[2025.07.01-14.04.26:535][493]LogUObjectHash: Compacting FUObjectHashTables data took   1.43ms
[2025.07.01-14.04.26:561][494]LogPlayLevel: Display: Destroying online subsystem :Context_19
[2025.07.01-14.04.58:900][844]LogSlate: Window 'Color Picker' being destroyed
[2025.07.01-14.05.26:502][640]LogSlate: Window 'Color Picker' being destroyed
[2025.07.01-14.05.36:897][591]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.05.36:907][591]LogPlayLevel: [PlayLevel] Compiling WBP_StartGameWidget before play...
[2025.07.01-14.05.37:088][591]LogUObjectHash: Compacting FUObjectHashTables data took   1.38ms
[2025.07.01-14.05.37:091][591]LogPlayLevel: PlayLevel: Blueprint regeneration took 184 ms (1 blueprints)
[2025.07.01-14.05.37:091][591]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.05.37:097][591]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005840s)
[2025.07.01-14.05.37:097][591]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005880s)
[2025.07.01-14.05.37:127][591]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-14.05.37:128][591]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.05.37:130][591]LogPlayLevel: PIE: World Init took: (0.002454s)
[2025.07.01-14.05.37:131][591]LogAudio: Display: Creating Audio Device:                 Id: 15, Scope: Unique, Realtime: True
[2025.07.01-14.05.37:131][591]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.05.37:131][591]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.05.37:131][591]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.05.37:132][591]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.05.37:132][591]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.05.37:132][591]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.05.37:132][591]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.05.37:132][591]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.05.37:132][591]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.05.37:132][591]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.05.37:132][591]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.05.37:134][591]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.05.37:220][591]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.05.37:220][591]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.05.37:220][591]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.05.37:220][591]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.05.37:221][591]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=15
[2025.07.01-14.05.37:221][591]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=15
[2025.07.01-14.05.37:223][591]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=15
[2025.07.01-14.05.37:223][591]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=15
[2025.07.01-14.05.37:223][591]LogInit: FAudioDevice initialized with ID 15.
[2025.07.01-14.05.37:223][591]LogAudio: Display: Audio Device (ID: 15) registered with world 'Main'.
[2025.07.01-14.05.37:223][591]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 15
[2025.07.01-14.05.37:268][591]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.05.37:268][591]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.05.37:270][591]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.05.37
[2025.07.01-14.05.37:271][591]LogWorld: Bringing up level for play took: 0.001895
[2025.07.01-14.05.37:273][591]LogOnline: OSS: Created online subsystem instance for: :Context_20
[2025.07.01-14.05.37:280][591]PIE: Server logged in
[2025.07.01-14.05.37:282][591]PIE: Play in editor total start time 0.375 seconds.
[2025.07.01-14.05.37:447][591]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 19367.748047
[2025.07.01-14.05.37:790][610]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.05.37:790][610]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19368.076172, Update Interval: 336.422607
[2025.07.01-14.05.42:745][969]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.05.42:745][969]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.05.42:745][969]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-14.05.42:749][969]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.05.42:749][969]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.05.42:750][969]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.05.42:760][969]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.05.42:790][969]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 15
[2025.07.01-14.05.42:790][969]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=15, StreamState=4
[2025.07.01-14.05.42:793][969]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=15, StreamState=2
[2025.07.01-14.05.42:804][969]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-14.05.42:840][970]LogPlayLevel: Display: Destroying online subsystem :Context_20
[2025.07.01-14.06.48:496][249]LogUObjectHash: Compacting FUObjectHashTables data took   1.74ms
[2025.07.01-14.06.48:498][249]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.035
[2025.07.01-14.06.48:498][249]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-14.06.48:498][249]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-14.06.48:502][249]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto8
[2025.07.01-14.06.48:502][249]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto8A0F29AF041A2185EA9E884A42A125117.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto8.uasset'
[2025.07.01-14.06.48:526][249]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-14.06.48:531][249]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-14.06.48:531][249]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-14.06.48:540][249]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto8
[2025.07.01-14.06.48:540][249]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidget_Auto89DC5B234410FBC72D5AFC38DB1FF1A6F.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Widget/WBP_StartGameWidget_Auto8.uasset'
[2025.07.01-14.06.48:540][249]LogFileHelpers: Auto-saving content packages took 0.043
[2025.07.01-14.11.57:156][274]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 19747.093750
[2025.07.01-14.11.58:157][277]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.11.58:157][277]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19747.761719, Update Interval: 353.752258
[2025.07.01-14.14.36:781][905]LogUObjectHash: Compacting FUObjectHashTables data took   1.97ms
[2025.07.01-14.14.37:375][963]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-14.14.37:426][963]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_StartGameWidget] ([2] browsable assets)...
[2025.07.01-14.14.37:428][963]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_StartGameWidget.WBP_StartGameWidget]
[2025.07.01-14.14.37:428][963]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_StartGameWidget]
[2025.07.01-14.14.37:428][963]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_StartGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset" SILENT=true
[2025.07.01-14.14.37:437][963]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_StartGameWidget
[2025.07.01-14.14.37:437][963]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_StartGameWidgetD7BE794F46F852378B75BC81F96AC116.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_StartGameWidget.uasset'
[2025.07.01-14.14.37:447][963]LogFileHelpers: InternalPromptForCheckoutAndSave took 71.581 ms (total: 1.36 sec)
[2025.07.01-14.14.37:508][963]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-14.14.37:509][963]LogContentValidation: Enabled validators:
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-14.14.37:509][963]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-14.14.37:509][963]AssetCheck: /Game/Blueprints/Widget/WBP_StartGameWidget Validating asset
[2025.07.01-14.14.38:060][ 22]LogUObjectHash: Compacting FUObjectHashTables data took   1.55ms
[2025.07.01-14.14.38:068][ 22]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-14.14.38:126][ 22]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-14.14.38:126][ 22]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-14.14.38:126][ 22]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-14.14.38:131][ 22]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-14.14.38:132][ 22]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameModeF623FF144B7A7E8878830B95E198C337.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-14.14.38:141][ 22]LogFileHelpers: InternalPromptForCheckoutAndSave took 72.910 ms (total: 1.43 sec)
[2025.07.01-14.14.38:191][ 22]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-14.14.38:191][ 22]LogContentValidation: Enabled validators:
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-14.14.38:192][ 22]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-14.14.38:192][ 22]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-14.14.44:120][644]Running Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.07.01-14.14.44:225][644]Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.07.01-14.14.44:225][644]Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.07.01-14.14.44:729][644]Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log_GPF.txt
[2025.07.01-14.14.44:931][644]
[2025.07.01-14.14.44:931][644]Generating VisualStudioCode project files:
[2025.07.01-14.14.44:931][644]Discovering modules, targets and source code for project...
[2025.07.01-14.14.44:931][644]Adding projects for all targets...
[2025.07.01-14.14.45:335][644]Available x64 toolchains (1):
[2025.07.01-14.14.45:335][644] * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.07.01-14.14.45:335][644]    (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.07.01-14.14.45:335][644]Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.07.01-14.14.45:436][644]Adding projects for all targets took 0.45s
[2025.07.01-14.14.45:436][644]Adding projects for all game projects took 0.00s
[2025.07.01-14.14.45:738][644]Adding projects for all modules took 0.34s
[2025.07.01-14.14.45:839][644]Compiling Rules Assemblies
[2025.07.01-14.14.45:840][644]Compiling Rules Assemblies 100%
[2025.07.01-14.14.45:840][644]Creating Build Targets
[2025.07.01-14.14.47:959][644]Creating Build Targets 100%
[2025.07.01-14.14.47:959][644]Creating Build Targets took 2.05s
[2025.07.01-14.14.47:959][644]Binding IntelliSense data...
[2025.07.01-14.14.53:041][644]Binding IntelliSense data... 100%
[2025.07.01-14.14.53:042][644]Binding IntelliSense data took 5.07s
[2025.07.01-14.14.53:042][644]Writing project files...
[2025.07.01-14.14.53:748][644]Writing project files... 100%
[2025.07.01-14.14.53:748][644]Writing project files took 0.78s
[2025.07.01-14.14.53:748][644]
[2025.07.01-14.14.53:748][644]Generating QueryTargets data for editor...
[2025.07.01-14.14.53:748][644]Compiling Rules Assemblies
[2025.07.01-14.14.53:748][644]Compiling Rules Assemblies 100%
[2025.07.01-14.14.53:748][644]Writing Query Target Info
[2025.07.01-14.14.53:849][644]Writing Query Target Info 100%
[2025.07.01-14.14.53:849][644]Generating QueryTargets data for editor took 0.05s
[2025.07.01-14.14.53:849][644]
[2025.07.01-14.14.53:849][644]Result: Succeeded
[2025.07.01-14.14.53:849][644]Total execution time: 9.53 seconds
[2025.07.01-14.18.24:852][277]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20135.105469
[2025.07.01-14.18.25:853][280]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.18.25:853][280]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20135.771484, Update Interval: 312.588898
[2025.07.01-14.24.10:514][314]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20481.072266
[2025.07.01-14.24.11:514][317]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.24.11:514][317]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20481.740234, Update Interval: 305.716736
[2025.07.01-14.29.57:546][355]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20828.345703
[2025.07.01-14.29.58:547][358]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.29.58:547][358]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20829.013672, Update Interval: 315.791504
[2025.07.01-14.35.52:579][420]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21183.648438
[2025.07.01-14.35.54:245][425]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.35.54:245][425]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21184.984375, Update Interval: 330.264587
[2025.07.01-14.42.23:957][594]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21575.423828
[2025.07.01-14.42.24:958][597]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.42.24:958][597]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21576.091797, Update Interval: 342.950531
[2025.07.01-14.44.44:845][ 17]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.01-14.46.41:656][367]LogHotReload: New module detected: UnrealEditor-ToonTank-0004.dll
[2025.07.01-14.46.42:331][369]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-14.46.42:419][369]LogUObjectHash: Compacting FUObjectHashTables data took   0.93ms
[2025.07.01-14.46.42:457][369]LogClass: UPackage /Script/ToonTank Reload.
[2025.07.01-14.46.42:457][369]LogClass: UClass ToonTanksGameMode Reload.
[2025.07.01-14.46.42:475][369]LogClass: Could not find existing class ToonTanksGameMode in package /Script/ToonTank for reload, assuming new or modified class
[2025.07.01-14.46.42:508][369]Re-instancing ToonTanksGameMode after reload.
[2025.07.01-14.46.42:704][369]LogUObjectHash: Compacting FUObjectHashTables data took   2.40ms
[2025.07.01-14.46.42:763][369]Display: HotReload took  0.4s.
[2025.07.01-14.46.42:764][369]Display: Reload/Re-instancing Complete: 1 package changed, 1 class changed, 6 classes unchanged, 2 functions remapped
[2025.07.01-14.48.44:722][734]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21956.566406
[2025.07.01-14.48.45:724][737]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.48.45:724][737]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21957.234375, Update Interval: 314.374207
[2025.07.01-14.49.09:702][849]LogHotReload: New module detected: UnrealEditor-ToonTank-0005.dll
[2025.07.01-14.49.10:280][892]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-14.49.10:465][892]LogUObjectHash: Compacting FUObjectHashTables data took   2.09ms
[2025.07.01-14.49.10:724][892]LogUObjectHash: Compacting FUObjectHashTables data took   2.25ms
[2025.07.01-14.49.10:777][892]Display: HotReload took  0.5s.
[2025.07.01-14.49.10:777][892]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.01-14.49.14:896][204]LogSlate: Window 'Details' being destroyed
[2025.07.01-14.49.50:555][968]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.07.01-14.49.51:041][  9]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-14.49.51:095][  9]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-14.49.51:095][  9]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-14.49.51:095][  9]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-14.49.51:101][  9]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-14.49.51:102][  9]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode01BBCABE4C0EF266C8DA75B7F8F88066.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-14.49.51:112][  9]LogFileHelpers: InternalPromptForCheckoutAndSave took 71.353 ms (total: 1.50 sec)
[2025.07.01-14.49.51:181][  9]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-14.49.51:181][  9]LogContentValidation: Enabled validators:
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-14.49.51:181][  9]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-14.49.51:181][  9]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-14.50.02:828][278]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.50.02:843][278]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.50.02:843][278]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.50.02:850][278]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006853s)
[2025.07.01-14.50.02:850][278]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006901s)
[2025.07.01-14.50.02:903][278]LogUObjectHash: Compacting FUObjectHashTables data took   1.48ms
[2025.07.01-14.50.02:906][278]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.50.02:909][278]LogPlayLevel: PIE: World Init took: (0.002691s)
[2025.07.01-14.50.02:910][278]LogAudio: Display: Creating Audio Device:                 Id: 16, Scope: Unique, Realtime: True
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.50.02:910][278]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.50.02:911][278]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.50.02:911][278]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.50.02:911][278]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.50.02:911][278]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.50.02:913][278]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.50.03:027][278]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.50.03:027][278]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.50.03:027][278]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.50.03:027][278]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.50.03:028][278]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=16
[2025.07.01-14.50.03:028][278]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=16
[2025.07.01-14.50.03:030][278]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=16
[2025.07.01-14.50.03:031][278]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=16
[2025.07.01-14.50.03:031][278]LogInit: FAudioDevice initialized with ID 16.
[2025.07.01-14.50.03:031][278]LogAudio: Display: Audio Device (ID: 16) registered with world 'Main'.
[2025.07.01-14.50.03:031][278]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 16
[2025.07.01-14.50.03:095][278]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.50.03:095][278]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.50.03:098][278]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.50.03
[2025.07.01-14.50.03:099][278]LogWorld: Bringing up level for play took: 0.003411
[2025.07.01-14.50.03:102][278]LogOnline: OSS: Created online subsystem instance for: :Context_21
[2025.07.01-14.50.03:108][278]PIE: Server logged in
[2025.07.01-14.50.03:109][278]PIE: Play in editor total start time 0.266 seconds.
[2025.07.01-14.50.08:195][632]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-14.50.10:967][841]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-14.50.11:293][864]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.07.01-14.50.11:556][887]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.07.01-14.50.12:235][948]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-14.50.12:396][963]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.12:421][965]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.13:238][ 41]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.13:410][ 56]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.13:434][ 58]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.14:240][129]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.14:400][143]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.14:433][146]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.15:124][180]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.15:456][181]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.15:456][181]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.16:125][183]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.16:457][184]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.16:457][184]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.17:160][186]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.17:456][187]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.17:456][187]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.18:125][189]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.18:457][190]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.18:457][190]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.19:696][251]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.19:872][265]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.19:886][266]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.20:762][335]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.20:918][347]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.20:942][349]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.21:762][410]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.21:930][423]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.21:953][425]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.22:813][499]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.22:985][514]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.23:009][516]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.25:030][702]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.25:196][715]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.25:224][717]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.26:042][772]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.26:200][782]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.26:227][784]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.26:449][798]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.50.26:449][798]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.50.26:449][798]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-14.50.26:453][798]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.50.26:454][798]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.50.26:455][798]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.50.26:471][798]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.50.26:535][798]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 16
[2025.07.01-14.50.26:536][798]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=16, StreamState=4
[2025.07.01-14.50.26:539][798]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=16, StreamState=2
[2025.07.01-14.50.26:560][798]LogUObjectHash: Compacting FUObjectHashTables data took   2.14ms
[2025.07.01-14.50.26:610][799]LogPlayLevel: Display: Destroying online subsystem :Context_21
[2025.07.01-14.50.37:652][316]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.50.37:673][316]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.50.37:673][316]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.50.37:681][316]LogPlayLevel: PIE: StaticDuplicateObject took: (0.007366s)
[2025.07.01-14.50.37:681][316]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.007420s)
[2025.07.01-14.50.37:731][316]LogUObjectHash: Compacting FUObjectHashTables data took   2.08ms
[2025.07.01-14.50.37:735][316]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.50.37:738][316]LogPlayLevel: PIE: World Init took: (0.003095s)
[2025.07.01-14.50.37:740][316]LogAudio: Display: Creating Audio Device:                 Id: 17, Scope: Unique, Realtime: True
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.50.37:740][316]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.50.37:741][316]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.50.37:741][316]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.50.37:741][316]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.50.37:741][316]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.50.37:743][316]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.50.37:860][316]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.50.37:860][316]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.50.37:860][316]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.50.37:860][316]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.50.37:861][316]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=17
[2025.07.01-14.50.37:861][316]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=17
[2025.07.01-14.50.37:864][316]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=17
[2025.07.01-14.50.37:864][316]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=17
[2025.07.01-14.50.37:864][316]LogInit: FAudioDevice initialized with ID 17.
[2025.07.01-14.50.37:864][316]LogAudio: Display: Audio Device (ID: 17) registered with world 'Main'.
[2025.07.01-14.50.37:864][316]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 17
[2025.07.01-14.50.37:870][316]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.50.37:872][316]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.50.37
[2025.07.01-14.50.37:872][316]LogWorld: Bringing up level for play took: 0.002187
[2025.07.01-14.50.37:875][316]LogOnline: OSS: Created online subsystem instance for: :Context_22
[2025.07.01-14.50.37:880][316]PIE: Server logged in
[2025.07.01-14.50.37:885][316]PIE: Play in editor total start time 0.212 seconds.
[2025.07.01-14.50.40:887][509]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.50.40:887][509]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.50.40:887][509]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.50.40:888][509]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.50.40:890][509]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.50.40:905][509]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.50.40:957][509]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.50.40:957][509]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 17
[2025.07.01-14.50.40:958][509]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=17, StreamState=4
[2025.07.01-14.50.40:961][509]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=17, StreamState=2
[2025.07.01-14.50.40:973][509]LogUObjectHash: Compacting FUObjectHashTables data took   1.60ms
[2025.07.01-14.50.41:359][510]LogPlayLevel: Display: Destroying online subsystem :Context_22
[2025.07.01-14.50.42:620][588]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_41
[2025.07.01-14.50.43:552][638]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.50.43:565][638]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.50.43:565][638]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.50.43:573][638]LogPlayLevel: PIE: StaticDuplicateObject took: (0.008494s)
[2025.07.01-14.50.43:573][638]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.008553s)
[2025.07.01-14.50.43:623][638]LogUObjectHash: Compacting FUObjectHashTables data took   1.58ms
[2025.07.01-14.50.43:629][638]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.50.43:633][638]LogPlayLevel: PIE: World Init took: (0.004400s)
[2025.07.01-14.50.43:635][638]LogAudio: Display: Creating Audio Device:                 Id: 18, Scope: Unique, Realtime: True
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.50.43:635][638]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.50.43:635][638]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.50.43:635][638]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.50.43:635][638]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.50.43:635][638]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.50.43:638][638]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.50.43:743][638]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.50.43:743][638]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.50.43:743][638]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.50.43:743][638]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.50.43:744][638]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=18
[2025.07.01-14.50.43:744][638]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=18
[2025.07.01-14.50.43:747][638]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=18
[2025.07.01-14.50.43:747][638]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=18
[2025.07.01-14.50.43:747][638]LogInit: FAudioDevice initialized with ID 18.
[2025.07.01-14.50.43:747][638]LogAudio: Display: Audio Device (ID: 18) registered with world 'Main'.
[2025.07.01-14.50.43:748][638]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 18
[2025.07.01-14.50.43:752][638]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.50.43:754][638]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.50.43
[2025.07.01-14.50.43:755][638]LogWorld: Bringing up level for play took: 0.002419
[2025.07.01-14.50.43:758][638]LogOnline: OSS: Created online subsystem instance for: :Context_24
[2025.07.01-14.50.43:762][638]PIE: Server logged in
[2025.07.01-14.50.43:765][638]PIE: Play in editor total start time 0.201 seconds.
[2025.07.01-14.50.56:337][568]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-14.50.56:721][597]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-14.50.56:819][605]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-14.50.56:819][605]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-14.50.57:023][620]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.57:023][620]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.57:804][680]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.57:816][681]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.58:018][697]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.58:018][697]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.58:812][760]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.58:826][761]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.59:028][777]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.59:028][777]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.59:801][837]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.50.59:813][838]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.00:011][853]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.00:025][854]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.00:804][916]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.00:819][917]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.01:019][932]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.01:019][932]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.01:800][992]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.01:816][993]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.02:010][  8]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.02:011][  8]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.02:804][ 70]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.02:817][ 71]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.03:020][ 87]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.03:020][ 87]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.03:633][132]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.51.03:634][132]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.51.03:634][132]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.51.03:634][132]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.51.03:636][132]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.51.03:648][132]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.51.03:690][132]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.51.03:690][132]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 18
[2025.07.01-14.51.03:690][132]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=18, StreamState=4
[2025.07.01-14.51.03:693][132]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=18, StreamState=2
[2025.07.01-14.51.03:707][132]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.07.01-14.51.03:858][133]LogPlayLevel: Display: Destroying online subsystem :Context_24
[2025.07.01-14.51.51:045][608]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.51.51:053][608]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.51.51:088][608]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.51.51:093][608]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005776s)
[2025.07.01-14.51.51:093][608]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005828s)
[2025.07.01-14.51.51:128][608]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.07.01-14.51.51:130][608]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.51.51:132][608]LogPlayLevel: PIE: World Init took: (0.002059s)
[2025.07.01-14.51.51:133][608]LogAudio: Display: Creating Audio Device:                 Id: 19, Scope: Unique, Realtime: True
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.51.51:133][608]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.51.51:133][608]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.51.51:133][608]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.51.51:133][608]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.51.51:133][608]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.51.51:136][608]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.51.51:224][608]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.51.51:225][608]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.51.51:225][608]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.51.51:225][608]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.51.51:225][608]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=19
[2025.07.01-14.51.51:225][608]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=19
[2025.07.01-14.51.51:228][608]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=19
[2025.07.01-14.51.51:228][608]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=19
[2025.07.01-14.51.51:228][608]LogInit: FAudioDevice initialized with ID 19.
[2025.07.01-14.51.51:228][608]LogAudio: Display: Audio Device (ID: 19) registered with world 'Main'.
[2025.07.01-14.51.51:228][608]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 19
[2025.07.01-14.51.51:232][608]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.51.51:234][608]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.51.51
[2025.07.01-14.51.51:235][608]LogWorld: Bringing up level for play took: 0.002146
[2025.07.01-14.51.51:237][608]LogOnline: OSS: Created online subsystem instance for: :Context_25
[2025.07.01-14.51.51:241][608]PIE: Server logged in
[2025.07.01-14.51.51:243][608]PIE: Play in editor total start time 0.19 seconds.
[2025.07.01-14.51.56:136][ 67]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-14.51.56:216][ 75]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-14.51.56:257][ 79]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-14.51.57:137][167]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-14.51.57:218][175]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.57:250][178]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.58:133][265]LogTemp: Warning: Tower Destroyed
[2025.07.01-14.51.58:216][273]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.58:247][276]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.59:247][375]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.51.59:423][392]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.00:245][472]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.00:423][489]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.01:254][570]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.01:431][587]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.02:253][667]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.02:429][684]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.52.03:137][752]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.52.03:137][752]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.52.03:137][752]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.52.03:138][752]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.52.03:139][752]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.52.03:151][752]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.52.03:184][752]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.52.03:184][752]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 19
[2025.07.01-14.52.03:184][752]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=19, StreamState=4
[2025.07.01-14.52.03:186][752]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=19, StreamState=2
[2025.07.01-14.52.03:196][752]LogUObjectHash: Compacting FUObjectHashTables data took   1.53ms
[2025.07.01-14.52.03:319][753]LogPlayLevel: Display: Destroying online subsystem :Context_25
[2025.07.01-14.52.55:484][151]LogHotReload: New module detected: UnrealEditor-ToonTank-0006.dll
[2025.07.01-14.52.55:992][187]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-14.52.56:097][187]LogUObjectHash: Compacting FUObjectHashTables data took   1.52ms
[2025.07.01-14.52.56:303][187]LogUObjectHash: Compacting FUObjectHashTables data took   1.53ms
[2025.07.01-14.52.56:351][187]Display: HotReload took  0.4s.
[2025.07.01-14.52.56:351][187]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.01-14.52.57:256][189]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.52.57:265][189]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.52.57:265][189]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.52.57:271][189]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005460s)
[2025.07.01-14.52.57:271][189]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005491s)
[2025.07.01-14.52.57:397][189]LogUObjectHash: Compacting FUObjectHashTables data took   2.58ms
[2025.07.01-14.52.57:404][189]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.52.57:407][189]LogPlayLevel: PIE: World Init took: (0.002276s)
[2025.07.01-14.52.57:408][189]LogAudio: Display: Creating Audio Device:                 Id: 20, Scope: Unique, Realtime: True
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.52.57:408][189]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.52.57:408][189]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.52.57:408][189]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.52.57:408][189]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.52.57:408][189]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.52.57:410][189]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.52.57:500][189]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.52.57:500][189]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.52.57:500][189]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.52.57:500][189]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.52.57:500][189]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=20
[2025.07.01-14.52.57:501][189]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=20
[2025.07.01-14.52.57:503][189]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=20
[2025.07.01-14.52.57:503][189]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=20
[2025.07.01-14.52.57:503][189]LogInit: FAudioDevice initialized with ID 20.
[2025.07.01-14.52.57:503][189]LogAudio: Display: Audio Device (ID: 20) registered with world 'Main'.
[2025.07.01-14.52.57:503][189]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 20
[2025.07.01-14.52.57:508][189]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.52.57:509][189]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.52.57
[2025.07.01-14.52.57:510][189]LogWorld: Bringing up level for play took: 0.001935
[2025.07.01-14.52.57:512][189]LogOnline: OSS: Created online subsystem instance for: :Context_26
[2025.07.01-14.52.57:516][189]PIE: Server logged in
[2025.07.01-14.52.57:518][189]PIE: Play in editor total start time 0.254 seconds.
[2025.07.01-14.53.02:483][646]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-14.53.02:483][646]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-14.53.03:483][744]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.03:484][744]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.04:478][843]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.04:478][843]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.05:480][941]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.05:481][941]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.06:484][ 39]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.06:485][ 39]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.07:477][137]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.07:478][137]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.08:486][230]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.08:486][230]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] false
[2025.07.01-14.53.09:489][324]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.53.09:489][324]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.53.09:489][324]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.53.09:489][324]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.53.09:491][324]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.53.09:502][324]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.53.09:553][324]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-14.53.09:553][324]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 20
[2025.07.01-14.53.09:554][324]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=20, StreamState=4
[2025.07.01-14.53.09:556][324]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=20, StreamState=2
[2025.07.01-14.53.09:563][324]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.53.09:585][324]LogUObjectHash: Compacting FUObjectHashTables data took   2.00ms
[2025.07.01-14.53.09:743][325]LogPlayLevel: Display: Destroying online subsystem :Context_26
[2025.07.01-14.53.11:513][453]Cmd: SELECT NONE
[2025.07.01-14.54.33:917][820]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 22305.386719
[2025.07.01-14.54.34:918][823]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-14.54.34:918][823]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 22306.056641, Update Interval: 353.955505
[2025.07.01-14.56.25:331][246]LogHotReload: New module detected: UnrealEditor-ToonTank-0007.dll
[2025.07.01-14.56.25:871][288]LogHotReload: Starting Hot-Reload from IDE
[2025.07.01-14.56.25:974][288]LogUObjectHash: Compacting FUObjectHashTables data took   1.92ms
[2025.07.01-14.56.26:173][288]LogUObjectHash: Compacting FUObjectHashTables data took   2.05ms
[2025.07.01-14.56.26:226][288]Display: HotReload took  0.4s.
[2025.07.01-14.56.26:226][288]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.01-14.56.27:553][331]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-14.56.27:562][331]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-14.56.27:595][331]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.56.27:601][331]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005660s)
[2025.07.01-14.56.27:601][331]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005712s)
[2025.07.01-14.56.27:716][331]LogUObjectHash: Compacting FUObjectHashTables data took   1.71ms
[2025.07.01-14.56.27:727][331]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-14.56.27:729][331]LogPlayLevel: PIE: World Init took: (0.002598s)
[2025.07.01-14.56.27:730][331]LogAudio: Display: Creating Audio Device:                 Id: 21, Scope: Unique, Realtime: True
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-14.56.27:730][331]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-14.56.27:730][331]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-14.56.27:730][331]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-14.56.27:730][331]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-14.56.27:730][331]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-14.56.27:733][331]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-14.56.27:822][331]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-14.56.27:822][331]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-14.56.27:822][331]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-14.56.27:822][331]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-14.56.27:823][331]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=21
[2025.07.01-14.56.27:823][331]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=21
[2025.07.01-14.56.27:825][331]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=21
[2025.07.01-14.56.27:825][331]LogInit: FAudioDevice initialized with ID 21.
[2025.07.01-14.56.27:825][331]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=21
[2025.07.01-14.56.27:825][331]LogAudio: Display: Audio Device (ID: 21) registered with world 'Main'.
[2025.07.01-14.56.27:825][331]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 21
[2025.07.01-14.56.27:830][331]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-14.56.27:831][331]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-22.56.27
[2025.07.01-14.56.27:832][331]LogWorld: Bringing up level for play took: 0.001810
[2025.07.01-14.56.27:835][331]LogOnline: OSS: Created online subsystem instance for: :Context_27
[2025.07.01-14.56.27:838][331]PIE: Server logged in
[2025.07.01-14.56.27:841][331]PIE: Play in editor total start time 0.28 seconds.
[2025.07.01-14.56.33:205][810]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-14.56.34:018][889]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-14.56.34:524][934]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-14.56.34:665][947]LogTemp: Warning: Tower Destroyed
[2025.07.01-14.56.38:726][307]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.07.01-14.56.39:005][330]LogTemp: Warning: BP_PawnTurret_C_5 Health: 0.000000
[2025.07.01-14.56.39:175][344]LogTemp: Warning: Tower Destroyed
[2025.07.01-14.56.41:097][516]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.07.01-14.56.42:047][602]LogTemp: Warning: BP_PawnTurret_C_6 Health: 0.000000
[2025.07.01-14.56.42:187][614]LogTemp: Warning: Tower Destroyed
[2025.07.01-14.56.45:506][888]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.07.01-14.56.46:424][962]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.07.01-14.56.47:630][ 67]LogBlueprintUserMessages: [BP_ToonTanksGameMode_C_0] true
[2025.07.01-14.56.47:630][ 67]LogTemp: Warning: Tower Destroyed
[2025.07.01-14.56.49:492][231]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.56.49:492][231]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-14.56.49:492][231]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.56.49:493][231]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.56.49:494][231]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-14.56.49:504][231]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.56.49:534][231]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.01-14.56.49:535][231]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 21
[2025.07.01-14.56.49:535][231]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=21, StreamState=4
[2025.07.01-14.56.49:537][231]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=21, StreamState=2
[2025.07.01-14.56.49:543][231]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-14.56.49:546][231]LogUObjectHash: Compacting FUObjectHashTables data took   1.62ms
[2025.07.01-14.56.49:624][232]LogPlayLevel: Display: Destroying online subsystem :Context_27
[2025.07.01-14.58.13:016][529]LogUObjectHash: Compacting FUObjectHashTables data took   1.39ms
[2025.07.01-14.58.19:506][179]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.58.19:506][179]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.58.19:545][179]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/PlayerController/BP_ToonTanksPlayerController.BP_ToonTanksPlayerController
[2025.07.01-14.58.19:546][179]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_42
[2025.07.01-14.58.20:072][179]LogUObjectHash: Compacting FUObjectHashTables data took   1.68ms
[2025.07.01-14.58.21:167][283]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.58.21:167][283]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.58.21:269][283]LogUObjectHash: Compacting FUObjectHashTables data took   1.34ms
[2025.07.01-14.58.21:818][337]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.58.21:818][337]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.58.21:829][337]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.58.21:829][337]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.58.21:897][337]LogUObjectHash: Compacting FUObjectHashTables data took   1.35ms
[2025.07.01-14.58.22:218][370]LogActorComponent: UnregisterComponent: (/Engine/Transient.EditorFloorComp) Not registered. Aborting.
[2025.07.01-14.58.22:218][370]LogWorld: UWorld::CleanupWorld for World_42, bSessionEnded=true, bCleanupResources=true
[2025.07.01-14.58.22:218][370]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-14.58.22:290][370]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.07.01-14.58.46:354][785]LogContentBrowser: Deferred new asset file creation: WBP_StartGameWidget1
[2025.07.01-14.58.46:365][785]LogContentBrowser: Creating deferred item: WBP_StartGameWidget1
[2025.07.01-14.58.46:382][786]LogContentBrowser: Renaming the item being created (Deferred Item: WBP_StartGameWidget1).
[2025.07.01-14.58.52:493][407]LogContentBrowser: Attempting asset rename: WBP_StartGameWidget1 -> WBP_EndGameWidget
[2025.07.01-14.58.52:557][407]LogUObjectHash: Compacting FUObjectHashTables data took   1.69ms
[2025.07.01-14.58.52:573][407]LogContentBrowser: End creating deferred item WBP_StartGameWidget1
[2025.07.01-14.58.53:288][479]LogAssetEditorSubsystem: Opening Asset editor for WidgetBlueprint /Game/Blueprints/Widget/WBP_EndGameWidget.WBP_EndGameWidget
[2025.07.01-14.58.53:288][479]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_44
[2025.07.01-14.58.53:295][479]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_45
[2025.07.01-14.58.58:764][824]LogSlate: Window 'WBP_EndGameWidget' being destroyed
[2025.07.01-14.59.23:084][550]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.07.01-14.59.23:737][612]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-14.59.23:799][612]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Widget/WBP_EndGameWidget] ([2] browsable assets)...
[2025.07.01-14.59.23:801][612]OBJ SavePackage:     Rendered thumbnail for [WidgetBlueprint /Game/Blueprints/Widget/WBP_EndGameWidget.WBP_EndGameWidget]
[2025.07.01-14.59.23:801][612]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Widget/WBP_EndGameWidget]
[2025.07.01-14.59.23:801][612]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Widget/WBP_EndGameWidget" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_EndGameWidget.uasset" SILENT=true
[2025.07.01-14.59.23:806][612]LogSavePackage: Moving output files for package: /Game/Blueprints/Widget/WBP_EndGameWidget
[2025.07.01-14.59.23:806][612]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/WBP_EndGameWidget1F4FF00547DD4CF28BEC399241AE6C74.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Widget/WBP_EndGameWidget.uasset'
[2025.07.01-14.59.23:818][612]LogFileHelpers: InternalPromptForCheckoutAndSave took 81.049 ms (total: 1.58 sec)
[2025.07.01-14.59.23:882][612]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-14.59.23:882][612]LogContentValidation: Enabled validators:
[2025.07.01-14.59.23:882][612]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-14.59.23:882][612]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-14.59.23:882][612]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-14.59.23:883][612]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-14.59.23:883][612]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-14.59.23:883][612]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-14.59.23:883][612]AssetCheck: /Game/Blueprints/Widget/WBP_EndGameWidget Validating asset
[2025.07.01-14.59.23:898][612]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.07.01-14.59.44:312][916]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode
[2025.07.01-14.59.44:313][916]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_48
[2025.07.01-15.00.27:880][788]LogEditorTransaction: Undo Change Class Pin Value
[2025.07.01-15.00.30:045][983]LogBlueprintDebug: Display: Encountered a blueprint breakpoint in /Game/Blueprints/Widget/WBP_EndGameWidget.WBP_EndGameWidget without an associated node. The blueprint breakpoint has been removed
[2025.07.01-15.00.30:045][983]LogBlueprintDebug: Display: Encountered a blueprint breakpoint in /Game/Blueprints/Widget/WBP_EndGameWidget.WBP_EndGameWidget without an associated node. The blueprint breakpoint has been removed
[2025.07.01-15.00.31:664][173]LogEditorTransaction: Redo Change Class Pin Value
[2025.07.01-15.00.38:500][918]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.07.01-15.00.38:929][961]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.00.38:992][961]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.00.38:992][961]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.00.38:992][961]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.00.38:996][961]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.00.38:997][961]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameModeF7845292401AC2A51BFF69A56BBF5C99.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.00.39:016][961]LogFileHelpers: InternalPromptForCheckoutAndSave took 87.507 ms (total: 1.67 sec)
[2025.07.01-15.00.39:075][961]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.00.39:075][961]LogContentValidation: Enabled validators:
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.00.39:075][961]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.00.39:076][961]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.01.26:778][420]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 22718.031250
[2025.07.01-15.01.27:098][455]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-15.01.27:098][455]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 22718.318359, Update Interval: 346.186096
[2025.07.01-15.01.50:265][135]LogBlueprint: Error: [AssetLog] Y:\UE Project\ToonTank\Content\Blueprints\GameMode\BP_ToonTanksGameMode.uasset: [Compiler] This blueprint (self) is not a WBP_EndGameWidget_C, therefore ' Target ' must have a connection.
[2025.07.01-15.01.50:266][135]LogBlueprint: Error: [AssetLog] Y:\UE Project\ToonTank\Content\Blueprints\GameMode\BP_ToonTanksGameMode.uasset: [Compiler] Variable node  Get DIsplayText  uses an invalid target.  It may depend on a node that is not connected to the execution chain, and got purged.
[2025.07.01-15.01.50:269][135]LogStreaming: Display: FlushAsyncLoading(533): 1 QueuedPackages, 0 AsyncPackages
[2025.07.01-15.01.50:324][135]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-15.01.50:773][178]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.01.50:838][178]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.01.50:838][178]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.01.50:838][178]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.01.50:843][178]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.01.50:844][178]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode037D464F4B102FE5E37B34AC9DB8EA4E.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.01.50:855][178]LogFileHelpers: InternalPromptForCheckoutAndSave took 81.631 ms (total: 1.75 sec)
[2025.07.01-15.01.50:903][178]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.01.50:903][178]LogContentValidation: Enabled validators:
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.01.50:903][178]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.01.50:903][178]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.01.57:794][661]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.01-15.01.58:374][721]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.01.58:436][721]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.01.58:436][721]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.01.58:436][721]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.01.58:441][721]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.01.58:441][721]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode8CD3461D49AB1C4F8ABF30A299BB1D20.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.01.58:450][721]LogFileHelpers: InternalPromptForCheckoutAndSave took 75.225 ms (total: 1.83 sec)
[2025.07.01-15.01.58:502][721]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.01.58:502][721]LogContentValidation: Enabled validators:
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.01.58:502][721]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.01.58:502][721]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.02.00:061][896]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-15.02.00:070][896]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-15.02.00:070][896]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.02.00:076][896]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005832s)
[2025.07.01-15.02.00:076][896]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005873s)
[2025.07.01-15.02.00:109][896]LogUObjectHash: Compacting FUObjectHashTables data took   1.72ms
[2025.07.01-15.02.00:113][896]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-15.02.00:115][896]LogPlayLevel: PIE: World Init took: (0.002196s)
[2025.07.01-15.02.00:116][896]LogAudio: Display: Creating Audio Device:                 Id: 22, Scope: Unique, Realtime: True
[2025.07.01-15.02.00:116][896]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-15.02.00:116][896]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-15.02.00:116][896]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-15.02.00:116][896]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-15.02.00:116][896]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-15.02.00:117][896]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-15.02.00:117][896]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-15.02.00:117][896]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-15.02.00:117][896]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-15.02.00:117][896]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-15.02.00:117][896]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-15.02.00:119][896]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-15.02.00:211][896]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-15.02.00:212][896]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-15.02.00:212][896]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-15.02.00:212][896]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-15.02.00:212][896]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=22
[2025.07.01-15.02.00:213][896]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=22
[2025.07.01-15.02.00:215][896]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=22
[2025.07.01-15.02.00:215][896]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=22
[2025.07.01-15.02.00:215][896]LogInit: FAudioDevice initialized with ID 22.
[2025.07.01-15.02.00:215][896]LogAudio: Display: Audio Device (ID: 22) registered with world 'Main'.
[2025.07.01-15.02.00:215][896]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 22
[2025.07.01-15.02.00:251][896]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.02.00:251][896]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-15.02.00:253][896]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-23.02.00
[2025.07.01-15.02.00:253][896]LogWorld: Bringing up level for play took: 0.001925
[2025.07.01-15.02.00:256][896]LogOnline: OSS: Created online subsystem instance for: :Context_32
[2025.07.01-15.02.00:261][896]PIE: Server logged in
[2025.07.01-15.02.00:262][896]PIE: Play in editor total start time 0.192 seconds.
[2025.07.01-15.02.05:331][331]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-15.02.05:353][333]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-15.02.07:294][509]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.02.07:294][509]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.02.07:295][509]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-15.02.07:302][509]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.02.07:302][509]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.02.07:303][509]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-15.02.07:314][509]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.02.07:351][509]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 22
[2025.07.01-15.02.07:351][509]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=22, StreamState=4
[2025.07.01-15.02.07:353][509]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=22, StreamState=2
[2025.07.01-15.02.07:362][509]LogUObjectHash: Compacting FUObjectHashTables data took   1.52ms
[2025.07.01-15.02.07:389][510]LogPlayLevel: Display: Destroying online subsystem :Context_32
[2025.07.01-15.02.46:657][702]LogUObjectHash: Compacting FUObjectHashTables data took   1.69ms
[2025.07.01-15.02.46:660][702]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.043
[2025.07.01-15.02.46:660][702]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.02.46:660][702]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.02.46:666][702]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto9
[2025.07.01-15.02.46:666][702]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode_Auto9ADC6184A4340683E705A4BA9C139A0AA.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/GameMode/BP_ToonTanksGameMode_Auto9.uasset'
[2025.07.01-15.02.46:667][702]LogFileHelpers: Auto-saving content packages took 0.007
[2025.07.01-15.05.39:254][948]LogEditorTransaction: Undo Create Pin Link
[2025.07.01-15.06.22:918][843]LogUObjectHash: Compacting FUObjectHashTables data took   1.60ms
[2025.07.01-15.06.23:900][945]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.06.23:954][945]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.06.23:954][945]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.06.23:954][945]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.06.23:961][945]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.06.23:961][945]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode80FEBAF54D97D24C4DEE5ABD83410E5E.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.06.23:972][945]LogFileHelpers: InternalPromptForCheckoutAndSave took 71.771 ms (total: 1.90 sec)
[2025.07.01-15.06.24:033][945]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.06.24:033][945]LogContentValidation: Enabled validators:
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.06.24:033][945]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.06.24:034][945]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.07.32:480][112]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.07.32:543][112]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.07.32:543][112]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.07.32:543][112]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.07.32:549][112]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.07.32:550][112]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode87D3CD144C34871389C36F9EAD3365CA.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.07.32:559][112]LogFileHelpers: InternalPromptForCheckoutAndSave took 79.003 ms (total: 1.98 sec)
[2025.07.01-15.07.32:594][112]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.07.32:594][112]LogContentValidation: Enabled validators:
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.07.32:594][112]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.07.32:594][112]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.07.40:737][ 55]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.07.01-15.07.41:484][128]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.07.41:541][128]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.07.41:541][128]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.07.41:541][128]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.07.41:547][128]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.07.41:548][128]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode88000FF242007CCBAC6269BA2C0D316F.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.07.41:556][128]LogFileHelpers: InternalPromptForCheckoutAndSave took 73.044 ms (total: 2.05 sec)
[2025.07.01-15.07.41:591][128]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.07.41:592][128]LogContentValidation: Enabled validators:
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.07.41:592][128]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.07.41:592][128]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.07.43:253][315]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-15.07.43:261][315]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-15.07.43:261][315]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.07.43:267][315]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006321s)
[2025.07.01-15.07.43:267][315]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006366s)
[2025.07.01-15.07.43:301][315]LogUObjectHash: Compacting FUObjectHashTables data took   1.44ms
[2025.07.01-15.07.43:305][315]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-15.07.43:307][315]LogPlayLevel: PIE: World Init took: (0.002167s)
[2025.07.01-15.07.43:308][315]LogAudio: Display: Creating Audio Device:                 Id: 23, Scope: Unique, Realtime: True
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-15.07.43:308][315]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-15.07.43:308][315]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-15.07.43:308][315]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-15.07.43:308][315]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-15.07.43:308][315]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-15.07.43:310][315]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-15.07.43:401][315]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-15.07.43:402][315]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-15.07.43:402][315]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-15.07.43:402][315]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-15.07.43:403][315]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=23
[2025.07.01-15.07.43:403][315]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=23
[2025.07.01-15.07.43:405][315]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=23
[2025.07.01-15.07.43:405][315]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=23
[2025.07.01-15.07.43:405][315]LogInit: FAudioDevice initialized with ID 23.
[2025.07.01-15.07.43:405][315]LogAudio: Display: Audio Device (ID: 23) registered with world 'Main'.
[2025.07.01-15.07.43:405][315]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 23
[2025.07.01-15.07.43:458][315]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.07.43:458][315]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-15.07.43:460][315]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-23.07.43
[2025.07.01-15.07.43:461][315]LogWorld: Bringing up level for play took: 0.001957
[2025.07.01-15.07.43:463][315]LogOnline: OSS: Created online subsystem instance for: :Context_33
[2025.07.01-15.07.43:469][315]PIE: Server logged in
[2025.07.01-15.07.43:470][315]PIE: Play in editor total start time 0.209 seconds.
[2025.07.01-15.07.48:470][767]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-15.07.48:494][769]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-15.07.55:535][426]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.07.55:535][426]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.07.55:536][426]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-15.07.55:541][426]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.07.55:542][426]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.07.55:543][426]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-15.07.55:555][426]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.07.55:590][426]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 23
[2025.07.01-15.07.55:590][426]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=23, StreamState=4
[2025.07.01-15.07.55:592][426]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=23, StreamState=2
[2025.07.01-15.07.55:602][426]LogUObjectHash: Compacting FUObjectHashTables data took   1.61ms
[2025.07.01-15.07.55:658][427]LogPlayLevel: Display: Destroying online subsystem :Context_33
[2025.07.01-15.07.58:322][737]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 23107.992188
[2025.07.01-15.07.58:596][770]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-15.07.58:597][770]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 23108.255859, Update Interval: 318.935516
[2025.07.01-15.09.29:843][326]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.01-15.09.29:901][326]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.07.01-15.09.29:901][326]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.07.01-15.09.29:901][326]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.07.01-15.09.29:908][326]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.07.01-15.09.29:909][326]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode249083CA49B5B26964840A80BA1A6DFD.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.07.01-15.09.29:917][326]LogFileHelpers: InternalPromptForCheckoutAndSave took 73.786 ms (total: 2.12 sec)
[2025.07.01-15.09.29:969][326]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.01-15.09.29:969][326]LogContentValidation: Enabled validators:
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.01-15.09.29:969][326]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.01-15.09.29:969][326]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.07.01-15.09.31:573][502]LogUObjectHash: Compacting FUObjectHashTables data took   1.70ms
[2025.07.01-15.09.33:030][658]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-15.09.33:038][658]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-15.09.33:039][658]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.09.33:045][658]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006380s)
[2025.07.01-15.09.33:045][658]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006421s)
[2025.07.01-15.09.33:077][658]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-15.09.33:081][658]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-15.09.33:083][658]LogPlayLevel: PIE: World Init took: (0.002083s)
[2025.07.01-15.09.33:084][658]LogAudio: Display: Creating Audio Device:                 Id: 24, Scope: Unique, Realtime: True
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-15.09.33:084][658]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-15.09.33:084][658]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-15.09.33:084][658]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-15.09.33:084][658]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-15.09.33:084][658]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-15.09.33:086][658]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-15.09.33:174][658]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-15.09.33:174][658]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-15.09.33:174][658]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-15.09.33:174][658]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-15.09.33:175][658]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=24
[2025.07.01-15.09.33:175][658]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=24
[2025.07.01-15.09.33:177][658]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=24
[2025.07.01-15.09.33:177][658]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=24
[2025.07.01-15.09.33:177][658]LogInit: FAudioDevice initialized with ID 24.
[2025.07.01-15.09.33:177][658]LogAudio: Display: Audio Device (ID: 24) registered with world 'Main'.
[2025.07.01-15.09.33:178][658]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 24
[2025.07.01-15.09.33:232][658]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.09.33:232][658]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-15.09.33:234][658]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-23.09.33
[2025.07.01-15.09.33:234][658]LogWorld: Bringing up level for play took: 0.001957
[2025.07.01-15.09.33:237][658]LogOnline: OSS: Created online subsystem instance for: :Context_34
[2025.07.01-15.09.33:244][658]PIE: Server logged in
[2025.07.01-15.09.33:245][658]PIE: Play in editor total start time 0.207 seconds.
[2025.07.01-15.09.41:231][281]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-15.09.41:692][320]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-15.09.41:878][336]LogTemp: Warning: Tower Destroyed
[2025.07.01-15.09.43:523][479]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.07.01-15.09.44:274][549]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-15.09.45:277][644]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-15.09.48:076][909]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.09.48:076][909]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.09.48:076][909]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.01-15.09.48:079][909]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.09.48:079][909]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.09.48:080][909]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-15.09.48:091][909]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.09.48:124][909]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 24
[2025.07.01-15.09.48:124][909]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=24, StreamState=4
[2025.07.01-15.09.48:126][909]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=24, StreamState=2
[2025.07.01-15.09.48:135][909]LogUObjectHash: Compacting FUObjectHashTables data took   1.44ms
[2025.07.01-15.09.48:164][910]LogPlayLevel: Display: Destroying online subsystem :Context_34
[2025.07.01-15.09.50:148][100]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-15.09.50:156][100]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-15.09.50:194][100]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.09.50:200][100]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005663s)
[2025.07.01-15.09.50:200][100]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005712s)
[2025.07.01-15.09.50:233][100]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.07.01-15.09.50:236][100]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-15.09.50:240][100]LogPlayLevel: PIE: World Init took: (0.003381s)
[2025.07.01-15.09.50:241][100]LogAudio: Display: Creating Audio Device:                 Id: 25, Scope: Unique, Realtime: True
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-15.09.50:241][100]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-15.09.50:241][100]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-15.09.50:241][100]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-15.09.50:241][100]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-15.09.50:241][100]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-15.09.50:243][100]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-15.09.50:331][100]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-15.09.50:331][100]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-15.09.50:331][100]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-15.09.50:331][100]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-15.09.50:332][100]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=25
[2025.07.01-15.09.50:332][100]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=25
[2025.07.01-15.09.50:334][100]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=25
[2025.07.01-15.09.50:334][100]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=25
[2025.07.01-15.09.50:334][100]LogInit: FAudioDevice initialized with ID 25.
[2025.07.01-15.09.50:334][100]LogAudio: Display: Audio Device (ID: 25) registered with world 'Main'.
[2025.07.01-15.09.50:334][100]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 25
[2025.07.01-15.09.50:340][100]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-15.09.50:341][100]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-23.09.50
[2025.07.01-15.09.50:342][100]LogWorld: Bringing up level for play took: 0.001781
[2025.07.01-15.09.50:344][100]LogOnline: OSS: Created online subsystem instance for: :Context_35
[2025.07.01-15.09.50:348][100]PIE: Server logged in
[2025.07.01-15.09.50:350][100]PIE: Play in editor total start time 0.195 seconds.
[2025.07.01-15.09.56:154][635]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.07.01-15.09.56:263][645]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.07.01-15.09.58:242][828]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.07.01-15.09.58:320][835]LogTemp: Warning: Tower Destroyed
[2025.07.01-15.09.58:621][862]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.07.01-15.09.58:812][880]LogTemp: Warning: Tower Destroyed
[2025.07.01-15.10.01:548][110]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-15.10.04:121][310]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.07.01-15.10.04:513][340]LogTemp: Warning: BP_PawnTurret_C_6 Health: 0.000000
[2025.07.01-15.10.04:881][369]LogTemp: Warning: Tower Destroyed
[2025.07.01-15.10.06:577][505]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-15.10.09:597][749]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.07.01-15.10.09:809][766]LogTemp: Warning: BP_PawnTurret_C_5 Health: 0.000000
[2025.07.01-15.10.10:074][787]LogTemp: Warning: Tower Destroyed
[2025.07.01-15.10.13:793][ 89]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.10.13:793][ 89]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.10.13:793][ 89]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.10.13:794][ 89]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.10.13:795][ 89]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-15.10.13:806][ 89]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.10.13:839][ 89]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.10.13:839][ 89]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 25
[2025.07.01-15.10.13:839][ 89]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=25, StreamState=4
[2025.07.01-15.10.13:841][ 89]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=25, StreamState=2
[2025.07.01-15.10.13:851][ 89]LogUObjectHash: Compacting FUObjectHashTables data took   1.47ms
[2025.07.01-15.10.13:900][ 90]LogPlayLevel: Display: Destroying online subsystem :Context_35
[2025.07.01-15.10.15:459][208]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.01-15.10.15:467][208]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.01-15.10.15:492][208]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.10.15:497][208]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005811s)
[2025.07.01-15.10.15:498][208]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005865s)
[2025.07.01-15.10.15:531][208]LogUObjectHash: Compacting FUObjectHashTables data took   1.43ms
[2025.07.01-15.10.15:534][208]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.01-15.10.15:537][208]LogPlayLevel: PIE: World Init took: (0.002305s)
[2025.07.01-15.10.15:538][208]LogAudio: Display: Creating Audio Device:                 Id: 26, Scope: Unique, Realtime: True
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.01-15.10.15:538][208]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.01-15.10.15:538][208]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.01-15.10.15:538][208]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.01-15.10.15:538][208]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.01-15.10.15:538][208]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.01-15.10.15:540][208]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.01-15.10.15:627][208]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.01-15.10.15:627][208]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.01-15.10.15:628][208]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.01-15.10.15:628][208]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.01-15.10.15:628][208]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=26
[2025.07.01-15.10.15:628][208]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=26
[2025.07.01-15.10.15:630][208]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=26
[2025.07.01-15.10.15:630][208]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=26
[2025.07.01-15.10.15:630][208]LogInit: FAudioDevice initialized with ID 26.
[2025.07.01-15.10.15:630][208]LogAudio: Display: Audio Device (ID: 26) registered with world 'Main'.
[2025.07.01-15.10.15:630][208]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 26
[2025.07.01-15.10.15:635][208]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.01-15.10.15:637][208]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.01-23.10.15
[2025.07.01-15.10.15:637][208]LogWorld: Bringing up level for play took: 0.001901
[2025.07.01-15.10.15:640][208]LogOnline: OSS: Created online subsystem instance for: :Context_36
[2025.07.01-15.10.15:643][208]PIE: Server logged in
[2025.07.01-15.10.15:645][208]PIE: Play in editor total start time 0.179 seconds.
[2025.07.01-15.10.20:593][673]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.07.01-15.10.20:604][674]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.07.01-15.10.22:222][826]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.10.22:222][826]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.01-15.10.22:222][826]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.10.22:223][826]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.10.22:224][826]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.01-15.10.22:235][826]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.10.22:269][826]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.01-15.10.22:270][826]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 26
[2025.07.01-15.10.22:270][826]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=26, StreamState=4
[2025.07.01-15.10.22:272][826]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=26, StreamState=2
[2025.07.01-15.10.22:280][826]LogUObjectHash: Compacting FUObjectHashTables data took   1.54ms
[2025.07.01-15.10.22:420][827]LogPlayLevel: Display: Destroying online subsystem :Context_36
[2025.07.01-15.13.55:941][561]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 23465.531250
[2025.07.01-15.13.56:941][564]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.01-15.13.56:941][564]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 23466.197266, Update Interval: 350.553284
[2025.07.01-15.16.57:388][660]LogUObjectHash: Compacting FUObjectHashTables data took   1.76ms
[2025.07.01-15.16.57:448][660]LogStall: Shutdown...
[2025.07.01-15.16.57:448][660]LogStall: Shutdown complete.
[2025.07.01-15.16.57:480][660]LogWorld: UWorld::CleanupWorld for World_45, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.57:480][660]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.57:492][660]LogWorld: UWorld::CleanupWorld for World_44, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.57:492][660]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.57:540][660]LogWorld: UWorld::CleanupWorld for World_48, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.57:540][660]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.57:645][660]LogSlate: Window 'ToonTank - Unreal Editor' being destroyed
[2025.07.01-15.16.57:968][660]LogUObjectHash: Compacting FUObjectHashTables data took   1.35ms
[2025.07.01-15.16.58:016][660]Cmd: QUIT_EDITOR
[2025.07.01-15.16.58:017][661]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.07.01-15.16.58:022][661]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.07.01-15.16.58:022][661]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.07.01-15.16.58:022][661]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.07.01-15.16.58:023][661]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.58:023][661]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.58:036][661]LogStylusInput: Shutting down StylusInput subsystem.
[2025.07.01-15.16.58:036][661]LogTedsSettings: UTedsSettingsEditorSubsystem::Deinitialize
[2025.07.01-15.16.58:037][661]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.07.01-15.16.58:048][661]LogWorld: UWorld::CleanupWorld for World_41, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.58:048][661]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.58:055][661]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.07.01-15.16.58:055][661]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.01-15.16.58:060][661]LogRuntimeTelemetry: Recording EnginePreExit events
[2025.07.01-15.16.58:061][661]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.07.01-15.16.58:064][661]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::EndSession
[2025.07.01-15.16.58:470][661]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.07.01-15.16.58:470][661]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.07.01-15.16.58:470][661]LogAudio: Display: Audio Device unregistered from world 'Main'.
[2025.07.01-15.16.58:470][661]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.07.01-15.16.58:470][661]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=4
[2025.07.01-15.16.58:472][661]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=2
[2025.07.01-15.16.58:502][661]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.07.01-15.16.58:502][661]LogAudio: Display: Audio Device Manager Shutdown
[2025.07.01-15.16.58:503][661]LogWindowsTextInputMethodSystem: Activated input method: Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.07.01-15.16.58:505][661]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.07.01-15.16.58:505][661]LogExit: Preparing to exit.
[2025.07.01-15.16.58:547][661]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:553][661]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.07.01-15.16.58:865][661]LogEditorDataStorage: Deinitializing
[2025.07.01-15.16.59:007][661]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.07.01-15.16.59:021][661]LogExit: Editor shut down
[2025.07.01-15.16.59:023][661]LogExit: Transaction tracking system shut down
[2025.07.01-15.16.59:192][661]LogExit: Object subsystem successfully closed.
[2025.07.01-15.16.59:202][661]LogShaderCompilers: Display: Shaders left to compile 0
[2025.07.01-15.16.59:287][661]LogShaderCompilers: Display: Shaders left to compile 0
[2025.07.01-15.16.59:338][661]LogMemoryProfiler: Shutdown
[2025.07.01-15.16.59:338][661]LogNetworkingProfiler: Shutdown
[2025.07.01-15.16.59:338][661]LoadingProfiler: Shutdown
[2025.07.01-15.16.59:338][661]LogTimingProfiler: Shutdown
[2025.07.01-15.16.59:339][661]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.07.01-15.16.59:339][661]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.07.01-15.16.59:608][661]LogChaosDD: Chaos Debug Draw Shutdown
[2025.07.01-15.16.59:622][661]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.07.01-15.16.59:622][661]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B3D0D2B5B-4C85-F6DD-B93F-FC88CA3FD520%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.0-43139311%2B%2B%2BUE5%2BRelease-5.6&UserID=736956f4414f060eb3ef6884c4fdf35b%7C2cea6243ea6c4d59a9b9ae3518a528b0%7C2f4bda52-3074-479d-ba0f-dfdc8867a3cc&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.07.01-15.17.01:674][661]LogEOSShared: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.07.01-15.17.01:682][661]LogNFORDenoise: NFORDenoise function shutting down
[2025.07.01-15.17.01:682][661]RenderDocPlugin: plugin has been unloaded.
[2025.07.01-15.17.01:682][661]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.07.01-15.17.01:686][661]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.07.01-15.17.01:686][661]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.07.01-15.17.01:686][661]LogPakFile: Destroying PakPlatformFile
[2025.07.01-15.17.01:980][661]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.07.01-15.17.02:007][661]LogExit: Exiting.
[2025.07.01-15.17.02:026][661]Log file closed, 07/01/25 23:17:02
