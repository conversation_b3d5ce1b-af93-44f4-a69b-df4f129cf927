<HotReloadState xmlns="http://schemas.datacontract.org/2004/07/UnrealBuildTool" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><NextSuffix>14</NextSuffix><OriginalFileToHotReloadFile xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfFileReferenceFileReferencePMg3qwHH><a:Key xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll</b:_x003C_FullName_x003E_k__BackingField></a:Key><a:Value xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0013.dll</b:_x003C_FullName_x003E_k__BackingField></a:Value></a:KeyValueOfFileReferenceFileReferencePMg3qwHH><a:KeyValueOfFileReferenceFileReferencePMg3qwHH><a:Key xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.pdb</b:_x003C_FullName_x003E_k__BackingField></a:Key><a:Value xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0013.pdb</b:_x003C_FullName_x003E_k__BackingField></a:Value></a:KeyValueOfFileReferenceFileReferencePMg3qwHH><a:KeyValueOfFileReferenceFileReferencePMg3qwHH><a:Key xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank.lib</b:_x003C_FullName_x003E_k__BackingField></a:Key><a:Value xmlns:b="http://schemas.datacontract.org/2004/07/EpicGames.Core"><b:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0013.lib</b:_x003C_FullName_x003E_k__BackingField></a:Value></a:KeyValueOfFileReferenceFileReferencePMg3qwHH></OriginalFileToHotReloadFile><TemporaryFiles xmlns:a="http://schemas.datacontract.org/2004/07/EpicGames.Core"><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\ToonTankEditor.target</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor.modules</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-6062.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-6062.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-6062.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0002.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0002.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0002.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0003.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0003.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0003.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0004.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0004.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0004.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0005.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0005.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0005.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0006.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0006.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0006.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0007.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0007.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0007.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0008.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0008.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0008.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0009.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0009.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0009.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0010.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0010.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0010.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-9439.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-9439.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-9439.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0012.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0012.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0012.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0013.dll</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank-0013.pdb</a:_x003C_FullName_x003E_k__BackingField></a:FileReference><a:FileReference><a:_x003C_FullName_x003E_k__BackingField>Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToonTank\UnrealEditor-ToonTank-0013.lib</a:_x003C_FullName_x003E_k__BackingField></a:FileReference></TemporaryFiles></HotReloadState>