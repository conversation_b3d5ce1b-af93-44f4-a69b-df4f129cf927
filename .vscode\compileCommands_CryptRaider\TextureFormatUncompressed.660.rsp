/FI "Y:\UE_5.6\Engine\Intermediate\Build\Win64\x64\UnrealEditor\Development\TextureFormatUncompressed\Definitions.TextureFormatUncompressed.h" 
/FI "Y:\UE Project\CryptRaider\Intermediate\Build\Win64\x64\CryptRaiderEditor\Development\Core\SharedPCH.Core.Cpp20.h" 
/I "Y:\UE_5.6\Engine\Source" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureFormatUncompressed\Private" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Core\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Core\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TraceLog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AutoRTFM\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ImageCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuild\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuild\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureBuild\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DerivedDataCache\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DerivedDataCache\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\DerivedDataCache\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\DerivedDataCache\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureCompressor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureCompressor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureCompressor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureFormat\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormatUncompressed\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormatUncompressed\VNI" 
/I "Y:\UE Project\CryptRaider\Intermediate\Build\Win64\x64\CryptRaiderEditor\Development\Core" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\GuidelinesSupportLibrary\GSL-1144\include" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\AtomicQueue" 
