﻿Log file open, 07/02/25 19:53:43
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=17564)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: ToonTank
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.5965] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 7 3700X 8-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "Y:\UE Project\ToonTank\ToonTank.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="736956f4414f060eb3ef6884c4fdf35b"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.825659
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-EF1402B74D779B04263CCFB64BA56414
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../UE Project/ToonTank/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogAssetRegistry: Display: PlatformFileJournal is not available on volume 'Y:' of project directory 'Y:/UE Project/ToonTank/', so AssetDiscovery cache will not be read or written. Unavailability reason:
	NTFS Journal is not active for volume 'Y:'. Launch cmd.exe as admin and run command `fsutil usn createJournal Y: m=<SizeInBytes>`. Recommended <SizeInBytes> is 0x40000000 (1GB).
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogAssetRegistry: Display: Asset registry cache read as 66.7 MiB from ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "Y:/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.07.02-11.53.44:219][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.07.02-11.53.44:219][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.07.02-11.53.44:219][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.07.02-11.53.44:219][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.07.02-11.53.44:219][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.07.02-11.53.44:220][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.07.02-11.53.44:220][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.07.02-11.53.44:220][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.07.02-11.53.44:220][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.07.02-11.53.44:220][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.07.02-11.53.44:224][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.07.02-11.53.44:224][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.07.02-11.53.44:224][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.07.02-11.53.44:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.07.02-11.53.44:225][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.07.02-11.53.44:225][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.07.02-11.53.44:229][  0]LogRHI: Using Default RHI: D3D12
[2025.07.02-11.53.44:229][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.02-11.53.44:229][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.02-11.53.44:242][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.07.02-11.53.44:242][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.02-11.53.44:457][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2060 (VendorId: 10de, DeviceId: 1f08, SubSysId: 37551462, Revision: 00a1
[2025.07.02-11.53.44:457][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.07.02-11.53.44:457][  0]LogD3D12RHI:   Adapter has 5954MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 2 output[s], UMA:false
[2025.07.02-11.53.44:457][  0]LogD3D12RHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.07.02-11.53.44:457][  0]LogD3D12RHI:      Driver Date: 6-1-2024
[2025.07.02-11.53.44:466][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.07.02-11.53.44:466][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.07.02-11.53.44:466][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 0 output[s], UMA:true
[2025.07.02-11.53.44:466][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.07.02-11.53.44:466][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.07.02-11.53.44:466][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.02-11.53.44:466][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.07.02-11.53.44:466][  0]LogHAL: Display: Platform has ~ 64 GB [68619431936 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.07.02-11.53.44:467][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.07.02-11.53.44:467][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.07.02-11.53.44:467][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.07.02-11.53.44:467][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.07.02-11.53.44:468][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.02-11.53.44:469][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.07.02-11.53.44:469][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.07.02-11.53.44:469][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.07.02-11.53.44:469][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.07.02-11.53.44:469][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.07.02-11.53.44:469][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.07.02-11.53.44:469][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.07.02-11.53.44:469][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Y:/UE Project/ToonTank/Saved/Config/WindowsEditor/Editor.ini]
[2025.07.02-11.53.44:469][  0]LogInit: Computer: ADMINISTRATOR
[2025.07.02-11.53.44:469][  0]LogInit: User: maxwe
[2025.07.02-11.53.44:469][  0]LogInit: CPU Page size=4096, Cores=8
[2025.07.02-11.53.44:469][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.07.02-11.53.44:469][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.07.02-11.53.44:469][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.07.02-11.53.44:469][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.07.02-11.53.44:469][  0]LogMemory: Process Physical Memory: 636.98 MB used, 654.78 MB peak
[2025.07.02-11.53.44:469][  0]LogMemory: Process Virtual Memory: 652.32 MB used, 652.32 MB peak
[2025.07.02-11.53.44:469][  0]LogMemory: Physical Memory: 16358.43 MB used,  49082.16 MB free, 65440.59 MB total
[2025.07.02-11.53.44:469][  0]LogMemory: Virtual Memory: 17484.51 MB used,  57684.08 MB free, 75168.59 MB total
[2025.07.02-11.53.44:469][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.07.02-11.53.44:477][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.07.02-11.53.44:487][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.07.02-11.53.44:487][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.07.02-11.53.44:488][  0]LogInit: Using OS detected language (en-GB).
[2025.07.02-11.53.44:488][  0]LogInit: Using OS detected locale (en-HK).
[2025.07.02-11.53.44:496][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.07.02-11.53.44:496][  0]LogInit: Setting process to per monitor DPI aware
[2025.07.02-11.53.44:919][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.07.02-11.53.44:919][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.07.02-11.53.44:919][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.07.02-11.53.44:920][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.07.02-11.53.44:920][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.07.02-11.53.44:920][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.07.02-11.53.44:920][  0]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.07.02-11.53.44:929][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.07.02-11.53.44:937][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.07.02-11.53.44:950][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.07.02-11.53.44:950][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.07.02-11.53.45:212][  0]LogRHI: Using Default RHI: D3D12
[2025.07.02-11.53.45:212][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.07.02-11.53.45:212][  0]LogRHI: Loading RHI module D3D12RHI
[2025.07.02-11.53.45:212][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.07.02-11.53.45:212][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.07.02-11.53.45:212][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.07.02-11.53.45:212][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.07.02-11.53.45:213][  0]LogWindows: Attached monitors:
[2025.07.02-11.53.45:213][  0]LogWindows:     resolution: 1920x1080, work area: (1920, 0) -> (3840, 1040), device: '\\.\DISPLAY1'
[2025.07.02-11.53.45:213][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1040), device: '\\.\DISPLAY2' [PRIMARY]
[2025.07.02-11.53.45:213][  0]LogWindows: Found 2 attached monitors.
[2025.07.02-11.53.45:213][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.07.02-11.53.45:213][  0]LogRHI: RHI Adapter Info:
[2025.07.02-11.53.45:213][  0]LogRHI:             Name: NVIDIA GeForce RTX 2060
[2025.07.02-11.53.45:213][  0]LogRHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.07.02-11.53.45:213][  0]LogRHI:      Driver Date: 6-1-2024
[2025.07.02-11.53.45:213][  0]LogD3D12RHI:     GPU DeviceId: 0x1f08 (for the marketing name, search the web for "GPU Device Id")
[2025.07.02-11.53.45:213][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.07.02-11.53.45:322][  0]LogNvidiaAftermath: Aftermath initialized
[2025.07.02-11.53.45:323][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.07.02-11.53.45:377][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.07.02-11.53.45:377][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Bindless resources are supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Raster order views are supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.07.02-11.53.45:377][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.07.02-11.53.45:462][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000026EB8F822C0)
[2025.07.02-11.53.45:463][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000026EB8F82580)
[2025.07.02-11.53.45:463][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000026EB8F82840)
[2025.07.02-11.53.45:463][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.07.02-11.53.45:463][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.07.02-11.53.45:744][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.07.02-11.53.45:744][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.07.02-11.53.45:744][  0]LogRHI: Texture pool is 3267 MB (70% of 4667 MB)
[2025.07.02-11.53.45:744][  0]LogD3D12RHI: Async texture creation enabled
[2025.07.02-11.53.45:744][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.07.02-11.53.45:765][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.07.02-11.53.45:772][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.07.02-11.53.45:772][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.07.02-11.53.45:772][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.07.02-11.53.45:772][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.07.02-11.53.45:772][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.07.02-11.53.45:789][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all'
[2025.07.02-11.53.45:789][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all" ]
[2025.07.02-11.53.45:831][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.07.02-11.53.45:831][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.07.02-11.53.45:831][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.07.02-11.53.45:831][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.07.02-11.53.45:831][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.07.02-11.53.45:831][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.07.02-11.53.45:831][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.07.02-11.53.45:831][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.07.02-11.53.45:837][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.07.02-11.53.45:842][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.07.02-11.53.45:890][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.07.02-11.53.45:917][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.07.02-11.53.45:917][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.07.02-11.53.45:940][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.07.02-11.53.45:940][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.07.02-11.53.45:940][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.07.02-11.53.45:940][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.07.02-11.53.45:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.07.02-11.53.45:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.07.02-11.53.45:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.07.02-11.53.45:970][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.07.02-11.53.45:992][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.07.02-11.53.45:993][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.07.02-11.53.46:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.07.02-11.53.46:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.07.02-11.53.46:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.07.02-11.53.46:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.07.02-11.53.46:018][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.07.02-11.53.46:160][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   VVM_1_0
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.07.02-11.53.46:208][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.07.02-11.53.46:208][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.07.02-11.53.46:212][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.07.02-11.53.46:212][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.07.02-11.53.46:212][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.07.02-11.53.46:212][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.02-11.53.46:212][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.07.02-11.53.46:447][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.07.02-11.53.46:447][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.07.02-11.53.46:447][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.07.02-11.53.46:449][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.07.02-11.53.46:450][  0]LogZenServiceInstance: InTree version at 'Y:/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.02-11.53.46:450][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.07.02-11.53.46:451][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.07.02-11.53.46:452][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 26196  --child-id Zen_26196_Startup'
[2025.07.02-11.53.46:561][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.07.02-11.53.46:561][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.112 seconds
[2025.07.02-11.53.46:568][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.07.02-11.53.46:578][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.07.02-11.53.46:578][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.10ms. RandomReadSpeed=282.43MBs, RandomWriteSpeed=219.32MBs. Assigned SpeedClass 'Local'
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.07.02-11.53.46:579][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.07.02-11.53.46:581][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../UE Project/ToonTank/Intermediate/Shaders/WorkingDirectory/26196/).
[2025.07.02-11.53.46:582][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/E284F9B0461619A7A35B94B13000E7FC/'.
[2025.07.02-11.53.46:590][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.07.02-11.53.46:591][  0]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.07.02-11.53.46:600][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.07.02-11.53.46:600][  0]LogShaderCompilers: Display: Using XGE Controller for shader compilation
[2025.07.02-11.53.46:600][  0]LogShaderCompilers: Display: Using 8 local workers for shader compilation
[2025.07.02-11.53.46:602][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../UE Project/ToonTank/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.07.02-11.53.46:602][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.07.02-11.53.47:724][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.07.02-11.53.49:636][  0]LogSlate: Using FreeType 2.10.0
[2025.07.02-11.53.49:638][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.07.02-11.53.49:644][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.02-11.53.49:644][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.02-11.53.49:644][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.02-11.53.49:644][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.02-11.53.49:722][  0]LogAssetRegistry: FAssetRegistry took 0.0022 seconds to start up
[2025.07.02-11.53.49:726][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.07.02-11.53.49:781][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin.
[2025.07.02-11.53.50:084][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.07.02-11.53.50:084][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.07.02-11.53.50:130][  0]LogDeviceProfileManager: Active device profile: [0000026EE8E0B680][0000026EDA0D5000 66] WindowsEditor
[2025.07.02-11.53.50:130][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.07.02-11.53.50:146][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-11.53.50:155][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.07.02-11.53.50:155][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.07.02-11.53.50:155][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.07.02-11.53.50:175][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.07.02-11.53.50:179][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR'
[2025.07.02-11.53.50:179][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR" -nocompile -nocompileuat ]
[2025.07.02-11.53.50:220][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:222][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.07.02-11.53.50:222][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:223][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:224][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.07.02-11.53.50:224][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:225][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:229][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.07.02-11.53.50:229][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.07.02-11.53.50:232][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.07.02-11.53.50:233][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:236][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:236][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:238][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.07.02-11.53.50:238][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.07.02-11.53.50:240][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.07.02-11.53.50:240][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:282][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.07.02-11.53.50:282][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:303][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.07.02-11.53.50:303][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:318][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.07.02-11.53.50:318][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.07.02-11.53.50:683][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.07.02-11.53.50:683][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.07.02-11.53.50:683][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.07.02-11.53.50:683][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.07.02-11.53.50:683][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.07.02-11.53.51:314][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.07.02-11.53.51:410][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.07.02-11.53.51:485][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.07.02-11.53.51:485][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.07.02-11.53.51:485][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.07.02-11.53.51:485][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.07.02-11.53.51:549][  0]LogSlate: Border
[2025.07.02-11.53.51:549][  0]LogSlate: BreadcrumbButton
[2025.07.02-11.53.51:549][  0]LogSlate: Brushes.Title
[2025.07.02-11.53.51:549][  0]LogSlate: ColorPicker.ColorThemes
[2025.07.02-11.53.51:549][  0]LogSlate: Default
[2025.07.02-11.53.51:549][  0]LogSlate: Icons.Save
[2025.07.02-11.53.51:549][  0]LogSlate: Icons.Toolbar.Settings
[2025.07.02-11.53.51:549][  0]LogSlate: ListView
[2025.07.02-11.53.51:549][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.07.02-11.53.51:549][  0]LogSlate: SoftwareCursor_Grab
[2025.07.02-11.53.51:549][  0]LogSlate: TableView.DarkRow
[2025.07.02-11.53.51:549][  0]LogSlate: TableView.Row
[2025.07.02-11.53.51:549][  0]LogSlate: TreeView
[2025.07.02-11.53.51:814][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.07.02-11.53.51:822][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 8.533 ms
[2025.07.02-11.53.51:915][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.07.02-11.53.51:915][  0]LogInit: XR: MultiViewport is Disabled
[2025.07.02-11.53.51:915][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.07.02-11.53.51:934][  0]LogTurnkeySupport: Turnkey Device: Win64@Administrator: (Name=Administrator, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.07.02-11.53.51:964][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.07.02-11.53.52:843][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.07.02-11.53.52:858][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.07.02-11.53.52:858][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.07.02-11.53.52:858][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:51919'.
[2025.07.02-11.53.52:864][  0]LogUdpMessaging: Display: Added local interface '192.168.0.177' to multicast group '230.0.0.1:6666'
[2025.07.02-11.53.52:867][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.07.02-11.53.53:292][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.07.02-11.53.53:293][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.07.02-11.53.53:326][  0]LogMetaSound: MetaSound Engine Initialized
[2025.07.02-11.53.53:892][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 46287708B74B4414800000000000A400 | Instance: DA74016B40E34632DCD39FA5487F147F (ADMINISTRATOR-26196).
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.07.02-11.53.54:101][  0]LogNNERuntimeORT:   NPU:       no
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: Interface availability:
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT:   GPU: yes
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT:   RDG: yes
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT:   NPU: no
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.07.02-11.53.54:102][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.07.02-11.53.54:274][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.07.02-11.53.54:274][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.07.02-11.53.54:290][  0]LogTimingProfiler: Initialize
[2025.07.02-11.53.54:292][  0]LogTimingProfiler: OnSessionChanged
[2025.07.02-11.53.54:292][  0]LoadingProfiler: Initialize
[2025.07.02-11.53.54:293][  0]LoadingProfiler: OnSessionChanged
[2025.07.02-11.53.54:293][  0]LogNetworkingProfiler: Initialize
[2025.07.02-11.53.54:294][  0]LogNetworkingProfiler: OnSessionChanged
[2025.07.02-11.53.54:295][  0]LogMemoryProfiler: Initialize
[2025.07.02-11.53.54:295][  0]LogMemoryProfiler: OnSessionChanged
[2025.07.02-11.53.55:057][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.02-11.53.55:057][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.07.02-11.53.55:084][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.07.02-11.53.55:653][  0]SourceControl: Revision control is disabled
[2025.07.02-11.53.55:675][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.02-11.53.55:675][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.02-11.53.55:675][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.02-11.53.55:675][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.02-11.53.55:704][  0]SourceControl: Revision control is disabled
[2025.07.02-11.53.56:209][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.07.02-11.53.56:233][  0]LogCollectionManager: Loaded 0 collections in 0.001914 seconds
[2025.07.02-11.53.56:237][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Saved/Collections/' took 0.00s
[2025.07.02-11.53.56:240][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Developers/maxwe/Collections/' took 0.00s
[2025.07.02-11.53.56:242][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Collections/' took 0.00s
[2025.07.02-11.53.56:381][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.07.02-11.53.56:381][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.07.02-11.53.56:381][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.07.02-11.53.56:381][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.07.02-11.53.56:404][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.07.02-11.53.56:404][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.07.02-11.53.56:404][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.07.02-11.53.56:404][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.07.02-11.53.56:422][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-07-02T11:53:56.422Z using C
[2025.07.02-11.53.56:422][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=ToonTank, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.07.02-11.53.56:424][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.07.02-11.53.56:425][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.07.02-11.53.56:436][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.07.02-11.53.56:439][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.07.02-11.53.56:439][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.07.02-11.53.56:439][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000477
[2025.07.02-11.53.56:523][  0]LogUObjectArray: 42374 objects as part of root set at end of initial load.
[2025.07.02-11.53.56:523][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.07.02-11.53.56:681][  0]LogEngine: Initializing Engine...
[2025.07.02-11.53.56:837][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.07.02-11.53.56:838][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.07.02-11.53.57:131][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.07.02-11.53.57:173][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.07.02-11.53.57:205][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.07.02-11.53.57:253][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.07.02-11.53.57:253][  0]LogInit: Texture streaming: Enabled
[2025.07.02-11.53.57:286][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.07.02-11.53.57:295][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.07.02-11.53.57:311][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.07.02-11.53.57:312][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.07.02-11.53.57:313][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.07.02-11.53.57:313][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.02-11.53.57:314][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.02-11.53.57:314][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.02-11.53.57:314][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.02-11.53.57:314][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.02-11.53.57:314][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.02-11.53.57:342][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.02-11.53.57:529][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.02-11.53.57:530][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.02-11.53.57:533][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.02-11.53.57:533][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.02-11.53.57:535][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.07.02-11.53.57:535][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.07.02-11.53.57:538][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.07.02-11.53.57:538][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.07.02-11.53.57:538][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.07.02-11.53.57:538][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.07.02-11.53.57:538][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.07.02-11.53.57:557][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.07.02-11.53.57:566][  0]LogInit: Undo buffer set to 256 MB
[2025.07.02-11.53.57:566][  0]LogInit: Transaction tracking system initialized
[2025.07.02-11.53.57:586][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.07.02-11.53.57:743][  0]LocalizationService: Localization service is disabled
[2025.07.02-11.53.58:134][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/' took 0.01s
[2025.07.02-11.53.58:141][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.07.02-11.53.58:245][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.07.02-11.53.58:245][  0]LogPython: Using Python 3.11.8
[2025.07.02-11.53.58:331][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.07.02-11.53.59:326][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.07.02-11.53.59:407][  0]LogEditorDataStorage: Initializing
[2025.07.02-11.53.59:416][  0]LogEditorDataStorage: Initialized
[2025.07.02-11.53.59:447][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.07.02-11.53.59:555][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.07.02-11.53.59:598][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.07.02-11.53.59:605][  0]SourceControl: Revision control is disabled
[2025.07.02-11.53.59:605][  0]LogUnrealEdMisc: Loading editor; pre map load, took 17.482
[2025.07.02-11.53.59:610][  0]Cmd: MAP LOAD FILE="../../../../UE Project/ToonTank/Content/Maps/Main.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.07.02-11.53.59:615][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.07.02-11.53.59:616][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.02-11.53.59:689][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.02-11.53.59:699][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.63ms
[2025.07.02-11.53.59:934][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Main'.
[2025.07.02-11.53.59:934][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.02-11.53.59:969][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.07.02-11.54.00:010][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.95ms
[2025.07.02-11.54.00:015][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.07.02-11.54.00:015][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.299ms to complete.
[2025.07.02-11.54.00:029][  0]LogUnrealEdMisc: Total Editor Startup Time, took 17.906
[2025.07.02-11.54.00:259][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.07.02-11.54.00:501][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-11.54.00:635][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-11.54.00:770][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-11.54.00:908][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-11.54.01:015][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:016][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.07.02-11.54.01:017][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:017][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.07.02-11.54.01:018][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:019][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:019][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:020][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:022][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:022][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.07.02-11.54.01:023][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:024][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:025][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:026][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.07.02-11.54.01:027][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:027][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:028][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:029][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:030][  0]LogPakFile: Initializing PakPlatformFile
[2025.07.02-11.54.01:030][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.07.02-11.54.01:165][  0]LogAssetRegistry: Display: Asset registry cache written as 66.7 MiB to ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin
[2025.07.02-11.54.01:241][  0]LogSlate: Took 0.000538 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.07.02-11.54.01:532][  0]LogSlate: Took 0.000752 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.07.02-11.54.01:539][  0]LogSlate: Took 0.000634 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.07.02-11.54.01:543][  0]LogSlate: Took 0.000638 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.07.02-11.54.01:656][  0]LogSlate: Took 0.000618 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.07.02-11.54.02:064][  0]LogStall: Startup...
[2025.07.02-11.54.02:069][  0]LogStall: Startup complete.
[2025.07.02-11.54.02:107][  0]LogLoad: (Engine Initialization) Total time: 19.98 seconds
[2025.07.02-11.54.02:588][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.07.02-11.54.02:588][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.07.02-11.54.02:682][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.02-11.54.02:689][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.07.02-11.54.02:765][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.07.02-11.54.02:777][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 88.424 ms
[2025.07.02-11.54.03:153][  1]LogAssetRegistry: AssetRegistryGather time 0.1936s: AssetDataDiscovery 0.0461s, AssetDataGather 0.0657s, StoreResults 0.0818s. Wall time 13.4320s.
	NumCachedDirectories 0. NumUncachedDirectories 1474. NumCachedFiles 7413. NumUncachedFiles 3.
	BackgroundTickInterruptions 0.
[2025.07.02-11.54.03:182][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.07.02-11.54.03:200][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.07.02-11.54.03:201][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.07.02-11.54.03:693][  5]LogSourceControl: Uncontrolled asset discovery finished in 0.49186 seconds (Found 7392 uncontrolled assets)
[2025.07.02-11.54.04:160][  6]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 7.388043
[2025.07.02-11.54.04:162][  6]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.07.02-11.54.04:164][  6]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7.721388
[2025.07.02-11.54.04:807][ 27]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.07.02-11.54.05:080][ 50]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 8.629444
[2025.07.02-11.54.05:083][ 50]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.07.02-11.54.05:083][ 50]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8.629444, Update Interval: 300.900909
[2025.07.02-11.54.13:278][535]Running Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.07.02-11.54.13:386][535]Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.07.02-11.54.13:386][535]Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.07.02-11.54.14:092][535]Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log_GPF.txt
[2025.07.02-11.54.14:294][535]
[2025.07.02-11.54.14:294][535]Generating VisualStudioCode project files:
[2025.07.02-11.54.14:294][535]Discovering modules, targets and source code for project...
[2025.07.02-11.54.14:396][535]Adding projects for all targets...
[2025.07.02-11.54.14:900][535]Available x64 toolchains (1):
[2025.07.02-11.54.14:900][535] * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.07.02-11.54.14:900][535]    (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.07.02-11.54.14:900][535]Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.07.02-11.54.14:900][535]Adding projects for all targets took 0.54s
[2025.07.02-11.54.14:900][535]Adding projects for all game projects took 0.00s
[2025.07.02-11.54.15:404][535]Adding projects for all modules took 0.42s
[2025.07.02-11.54.15:505][535]Compiling Rules Assemblies
[2025.07.02-11.54.15:506][535]Compiling Rules Assemblies 100%
[2025.07.02-11.54.15:506][535]Creating Build Targets
[2025.07.02-11.54.18:450][535]Creating Build Targets 100%
[2025.07.02-11.54.18:450][535]Creating Build Targets took 2.95s
[2025.07.02-11.54.18:450][535]Binding IntelliSense data...
[2025.07.02-11.54.24:920][535]Binding IntelliSense data... 100%
[2025.07.02-11.54.24:920][535]Binding IntelliSense data took 6.38s
[2025.07.02-11.54.24:920][535]Writing project files...
[2025.07.02-11.54.25:728][535]Writing project files... 100%
[2025.07.02-11.54.25:728][535]Writing project files took 0.86s
[2025.07.02-11.54.25:728][535]
[2025.07.02-11.54.25:728][535]Generating QueryTargets data for editor...
[2025.07.02-11.54.25:728][535]Compiling Rules Assemblies
[2025.07.02-11.54.25:728][535]Compiling Rules Assemblies 100%
[2025.07.02-11.54.25:728][535]Writing Query Target Info
[2025.07.02-11.54.25:728][535]Writing Query Target Info 100%
[2025.07.02-11.54.25:728][535]Generating QueryTargets data for editor took 0.02s
[2025.07.02-11.54.25:728][535]
[2025.07.02-11.54.25:728][535]Result: Succeeded
[2025.07.02-11.54.25:728][535]Total execution time: 12.20 seconds
[2025.07.02-11.54.57:522][603]LogStreaming: Display: FlushAsyncLoading(330): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-11.54.57:535][603]LogAssetEditorSubsystem: Opening Asset editor for ParticleSystem /Game/Assets/Effects/P_HitEffect.P_HitEffect
[2025.07.02-11.54.57:540][603]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.07.02-11.54.57:553][603]LogStaticMesh: Display: Building static mesh AnimTreeEd_PreviewFloor (Required Memory Estimate: 3.43098 MB)...
[2025.07.02-11.54.57:569][603]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Engine/EditorMeshes/AnimTreeEd_PreviewFloor) ...
[2025.07.02-11.54.57:573][603]LogStaticMesh: Built static mesh [0.02s] /Engine/EditorMeshes/AnimTreeEd_PreviewFloor.AnimTreeEd_PreviewFloor
[2025.07.02-11.54.58:367][603]LogSlate: Took 0.000184 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.07.02-11.54.58:448][603]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.07.02-11.54.58:779][606]LogMeshUtilities: Finished distance field build in 1.2s - 126x126x49 sparse distance field, 1.1Mb total, 0.0Mb always loaded, 83% occupied, 0 triangles, AnimTreeEd_PreviewFloor
[2025.07.02-11.55.01:415][750]LogSlate: Window 'P_HitEffect' being destroyed
[2025.07.02-11.55.46:579][673]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: ================================================
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Total job queries 258, among them cache hits 36 (13.95%), DDC hits 161 (62.40%), Duplicates 57 (22.09%)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Tracking 165 distinct input hashes that result in 87 distinct outputs (52.73%)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: RAM used: 665.50 KiB of 3.20 GiB budget. Usage: 0.02%
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Shaders Compiled: 4
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Jobs assigned 4, completed 4 (100%)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Time job spent in pending queue: average 0.06 s, longest 0.10 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Job execution time: average 1.29 s, max 1.65 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Job life time (pending + execution): average 1.35 s, max 1.75
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Shader code size: total 26.352 KiB, numShaders 4, average 6.588 KiB, min 4.34 KiB, max 7.715 KiB
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 1.75 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.03%
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Jobs were issued in 4 batches (only local compilation was used), average 1.00 jobs/batch
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Average processing rate: 2.28 jobs/sec
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Total thread time: 2.638 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Total thread preprocess time: 7.666 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Percentage time preprocessing: 290.55%
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Effective parallelization: 1.51 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.188169
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    2 times, average 0.76 sec, max 0.90 sec, min 0.62 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                                 FLumenCardPS (compiled    1 times, average 0.66 sec, max 0.66 sec, min 0.66 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialVS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 57.62% of total time (compiled    2 times, average 0.76 sec, max 0.90 sec, min 0.62 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                                 FLumenCardPS - 25.16% of total time (compiled    1 times, average 0.66 sec, max 0.66 sec, min 0.66 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 17.22% of total time (compiled    1 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialVS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialPS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: === Material stats ===
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Materials Cooked:        0
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Materials Translated:    139
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Material Total Translate Time: 0.22 s
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Material Translation Only: 0.06 s (29%)
[2025.07.02-11.57.02:626][522]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.07.02-11.57.02:627][522]LogShaderCompilers: Display: Material Cache Hits: 20 (14%)
[2025.07.02-11.57.02:627][522]LogShaderCompilers: Display: ================================================
[2025.07.02-11.58.04:109][439]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.07.02-11.58.05:131][498]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Actor/BP_Projectile.BP_Projectile
[2025.07.02-11.58.05:131][498]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.07.02-11.58.05:177][498]LogStreaming: Display: FlushAsyncLoading(343): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-11.58.14:364][975]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.07.02-11.58.14:364][975]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.02-11.58.14:389][975]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Actor/BP_Projectile.BP_Projectile
[2025.07.02-11.58.14:389][975]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.07.02-11.58.14:447][975]LogStreaming: Display: FlushAsyncLoading(537): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-11.58.16:154][975]LogUObjectHash: Compacting FUObjectHashTables data took   1.27ms
[2025.07.02-11.58.16:160][975]LogSlate: Took 0.000131 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.07.02-11.58.19:103][258]LogHotReload: New module detected: UnrealEditor-ToonTank-0008.dll
[2025.07.02-11.58.19:622][307]LogHotReload: Starting Hot-Reload from IDE
[2025.07.02-11.58.19:708][307]LogUObjectHash: Compacting FUObjectHashTables data took   1.16ms
[2025.07.02-11.58.19:742][307]LogClass: UPackage /Script/ToonTank Reload.
[2025.07.02-11.58.19:742][307]LogClass: UClass Projectile Reload.
[2025.07.02-11.58.19:750][307]LogClass: Could not find existing class Projectile in package /Script/ToonTank for reload, assuming new or modified class
[2025.07.02-11.58.19:750][307]LogClass: Function OnHit is new or belongs to a modified class.
[2025.07.02-11.58.19:783][307]Re-instancing Projectile after reload.
[2025.07.02-11.58.19:954][307]LogUObjectHash: Compacting FUObjectHashTables data took   1.14ms
[2025.07.02-11.58.20:002][307]LogStreaming: Display: FlushAsyncLoading(542): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-11.58.20:005][307]Display: HotReload took  0.4s.
[2025.07.02-11.58.20:005][307]Display: Reload/Re-instancing Complete: 1 package changed, 1 class changed, 6 classes unchanged, 1 function remapped
[2025.07.02-11.58.41:175][ 50]LogUObjectHash: Compacting FUObjectHashTables data took   1.75ms
[2025.07.02-11.58.41:825][111]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.07.02-11.58.41:888][111]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/Actor/BP_Projectile] ([2] browsable assets)...
[2025.07.02-11.58.41:938][111]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Actor/BP_Projectile.BP_Projectile]
[2025.07.02-11.58.41:939][111]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Actor/BP_Projectile]
[2025.07.02-11.58.41:939][111]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Actor/BP_Projectile" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Actor/BP_Projectile.uasset" SILENT=true
[2025.07.02-11.58.41:950][111]LogSavePackage: Moving output files for package: /Game/Blueprints/Actor/BP_Projectile
[2025.07.02-11.58.41:950][111]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_Projectile6799D985459D24C8CF644DADC9DF4203.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Actor/BP_Projectile.uasset'
[2025.07.02-11.58.41:959][111]LogFileHelpers: InternalPromptForCheckoutAndSave took 133.528 ms
[2025.07.02-11.58.42:021][111]LogContentValidation: Display: Starting to validate 1 assets
[2025.07.02-11.58.42:021][111]LogContentValidation: Enabled validators:
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.07.02-11.58.42:021][111]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.07.02-11.58.42:022][111]AssetCheck: /Game/Blueprints/Actor/BP_Projectile Validating asset
[2025.07.02-11.59.05:974][930]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 309.539459
[2025.07.02-11.59.06:236][961]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-11.59.06:236][961]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 309.791565, Update Interval: 301.093170
[2025.07.02-12.04.09:914][ 40]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 613.483215
[2025.07.02-12.04.10:914][ 43]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.04.10:914][ 43]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 614.150757, Update Interval: 356.747955
[2025.07.02-12.10.11:990][126]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 975.563599
[2025.07.02-12.10.12:991][129]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.10.12:991][129]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 976.230530, Update Interval: 340.057373
[2025.07.02-12.11.34:007][372]LogHotReload: New module detected: UnrealEditor-ToonTank-0009.dll
[2025.07.02-12.11.34:681][374]LogHotReload: Starting Hot-Reload from IDE
[2025.07.02-12.11.34:774][374]LogUObjectHash: Compacting FUObjectHashTables data took   2.72ms
[2025.07.02-12.11.34:962][374]LogUObjectHash: Compacting FUObjectHashTables data took   1.37ms
[2025.07.02-12.11.35:016][374]Display: HotReload took  0.3s.
[2025.07.02-12.11.35:016][374]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.02-12.16.02:294][175]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1325.870239
[2025.07.02-12.16.03:297][178]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.16.03:297][178]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1326.537842, Update Interval: 305.839417
[2025.07.02-12.21.31:038][161]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1654.618896
[2025.07.02-12.21.32:039][164]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.21.32:039][164]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1655.286499, Update Interval: 327.550903
[2025.07.02-12.27.45:777][285]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2029.360107
[2025.07.02-12.27.46:777][288]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.27.46:777][288]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2030.027588, Update Interval: 302.678925
[2025.07.02-12.33.48:522][373]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2392.104980
[2025.07.02-12.33.49:524][376]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.33.49:524][376]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2392.771973, Update Interval: 311.109344
[2025.07.02-12.39.34:261][410]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2737.843994
[2025.07.02-12.39.35:261][413]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.39.35:261][413]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2738.511230, Update Interval: 328.913239
[2025.07.02-12.42.42:628][975]LogHotReload: New module detected: UnrealEditor-ToonTank-0010.dll
[2025.07.02-12.42.43:301][977]LogHotReload: Starting Hot-Reload from IDE
[2025.07.02-12.42.43:478][977]LogUObjectHash: Compacting FUObjectHashTables data took   2.12ms
[2025.07.02-12.42.43:745][977]LogUObjectHash: Compacting FUObjectHashTables data took   1.60ms
[2025.07.02-12.42.43:789][977]Display: HotReload took  0.5s.
[2025.07.02-12.42.43:789][977]Display: Reload/Re-instancing Complete: 1 package changed, 7 classes unchanged, 2 functions remapped
[2025.07.02-12.42.47:338][280]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.07.02-12.42.47:350][280]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.07.02-12.42.47:358][280]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.07.02-12.42.47:359][280]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.07.02-12.42.47:359][280]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.07.02-12.42.47:367][280]LogPlayLevel: PIE: StaticDuplicateObject took: (0.007370s)
[2025.07.02-12.42.47:367][280]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.007711s)
[2025.07.02-12.42.47:465][280]LogUObjectHash: Compacting FUObjectHashTables data took   1.97ms
[2025.07.02-12.42.47:471][280]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.07.02-12.42.47:474][280]LogPlayLevel: PIE: World Init took: (0.002657s)
[2025.07.02-12.42.47:475][280]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.07.02-12.42.47:475][280]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.07.02-12.42.47:476][280]LogAudio: Display: AudioDevice MaxSources: 32
[2025.07.02-12.42.47:476][280]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.07.02-12.42.47:476][280]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.07.02-12.42.47:476][280]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.07.02-12.42.47:478][280]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.07.02-12.42.47:564][280]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.07.02-12.42.47:564][280]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.07.02-12.42.47:564][280]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.07.02-12.42.47:564][280]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.07.02-12.42.47:565][280]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.07.02-12.42.47:565][280]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.07.02-12.42.47:568][280]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.07.02-12.42.47:568][280]LogInit: FAudioDevice initialized with ID 2.
[2025.07.02-12.42.47:568][280]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.07.02-12.42.47:568][280]LogAudio: Display: Audio Device (ID: 2) registered with world 'Main'.
[2025.07.02-12.42.47:568][280]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.07.02-12.42.47:625][280]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-12.42.47:626][280]LogStreaming: Display: FlushAsyncLoading(543): 1 QueuedPackages, 0 AsyncPackages
[2025.07.02-12.42.47:666][280]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.07.02-12.42.47:668][280]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.07.02-20.42.47
[2025.07.02-12.42.47:671][280]LogWorld: Bringing up level for play took: 0.004091
[2025.07.02-12.42.47:673][280]LogOnline: OSS: Created online subsystem instance for: :Context_6
[2025.07.02-12.42.47:707][280]LogStaticMesh: Display: Waiting on static mesh StaticMesh /Game/Assets/Meshes/SM_TankBase.SM_TankBase being ready before playing
[2025.07.02-12.42.47:707][280]LogStaticMesh: Display: Waiting on static mesh StaticMesh /Game/Assets/Meshes/SM_TankTurret.SM_TankTurret being ready before playing
[2025.07.02-12.42.47:718][280]PIE: Server logged in
[2025.07.02-12.42.47:719][280]PIE: Play in editor total start time 0.373 seconds.
[2025.07.02-12.42.47:905][281]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
[2025.07.02-12.42.47:905][281]LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
[2025.07.02-12.43.00:249][304]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.07.02-12.43.00:249][304]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.07.02-12.43.00:249][304]LogSlate: Window 'ToonTank Preview [NetMode: Standalone 0]  (64-bit/PC D3D SM6)' being destroyed
[2025.07.02-12.43.00:256][304]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.07.02-12.43.00:256][304]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.02-12.43.00:258][304]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.07.02-12.43.00:269][304]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.07.02-12.43.00:301][304]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.07.02-12.43.00:302][304]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.07.02-12.43.00:302][304]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.07.02-12.43.00:304][304]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.07.02-12.43.00:350][304]LogUObjectHash: Compacting FUObjectHashTables data took   2.78ms
[2025.07.02-12.43.00:382][305]LogPlayLevel: Display: Destroying online subsystem :Context_6
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: ================================================
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Total job queries 262, among them cache hits 37 (14.12%), DDC hits 163 (62.21%), Duplicates 57 (21.76%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Tracking 168 distinct input hashes that result in 90 distinct outputs (53.57%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: RAM used: 686.72 KiB of 3.20 GiB budget. Usage: 0.02%
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Shaders Compiled: 5
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Jobs assigned 5, completed 5 (100%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Average time worker was idle: 2871.57 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Time job spent in pending queue: average 0.07 s, longest 0.11 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Job execution time: average 1.15 s, max 1.65 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Job life time (pending + execution): average 1.22 s, max 1.75
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Shader code size: total 35.387 KiB, numShaders 5, average 7.077 KiB, min 4.34 KiB, max 9.035 KiB
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 2.48 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.03%
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Jobs were issued in 5 batches (only local compilation was used), average 1.00 jobs/batch
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Average processing rate: 2.02 jobs/sec
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Total thread time: 3.011 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Total thread preprocess time: 7.725 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Percentage time preprocessing: 256.54%
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Effective parallelization: 1.21 (times faster than compiling all shaders on one thread). Compare with number of workers: 8 - 0.151795
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    2 times, average 0.76 sec, max 0.90 sec, min 0.62 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                                 FLumenCardPS (compiled    1 times, average 0.66 sec, max 0.66 sec, min 0.66 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    1 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight (compiled    1 times, average 0.37 sec, max 0.37 sec, min 0.37 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 50.48% of total time (compiled    2 times, average 0.76 sec, max 0.90 sec, min 0.62 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                                 FLumenCardPS - 22.04% of total time (compiled    1 times, average 0.66 sec, max 0.66 sec, min 0.66 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 15.09% of total time (compiled    1 times, average 0.45 sec, max 0.45 sec, min 0.45 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 12.39% of total time (compiled    1 times, average 0.37 sec, max 0.37 sec, min 0.37 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display:                                 FLandscapePhysicalMaterialVS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: === Material stats ===
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Materials Cooked:        0
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Materials Translated:    141
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Material Total Translate Time: 0.22 s
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Material Translation Only: 0.06 s (29%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (1%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: Material Cache Hits: 21 (15%)
[2025.07.02-12.45.03:874][720]LogShaderCompilers: Display: ================================================
[2025.07.02-12.45.44:892][843]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3108.469727
[2025.07.02-12.45.45:893][846]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.07.02-12.45.45:893][846]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3109.137451, Update Interval: 312.226318
