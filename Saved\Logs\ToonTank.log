﻿Log file open, 06/26/25 19:36:02
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=32324)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: ToonTank
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.5965] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 7 3700X 8-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "Y:\UE Project\ToonTank\ToonTank.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="736956f4414f060eb3ef6884c4fdf35b"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.422707
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-A6BE33D6491F210DD621DFAAD0F4FDAC
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../UE Project/ToonTank/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogAssetRegistry: Display: PlatformFileJournal is not available on volume 'Y:' of project directory 'Y:/UE Project/ToonTank/', so AssetDiscovery cache will not be read or written. Unavailability reason:
	NTFS Journal is not active for volume 'Y:'. Launch cmd.exe as admin and run command `fsutil usn createJournal Y: m=<SizeInBytes>`. Recommended <SizeInBytes> is 0x40000000 (1GB).
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.07 seconds
LogAssetRegistry: Display: Asset registry cache read as 66.6 MiB from ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading IOS ini files took 0.07 seconds
LogConfig: Display: Loading Android ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.07 seconds
LogConfig: Display: Loading TVOS ini files took 0.08 seconds
LogConfig: Display: Loading Windows ini files took 0.08 seconds
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogConfig: Display: Loading VisionOS ini files took 0.08 seconds
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogConfig: Display: Loading Linux ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin CompositeCore
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSShared: Loaded "Y:/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.26-11.36.03:439][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.26-11.36.03:439][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.26-11.36.03:439][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.26-11.36.03:439][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.26-11.36.03:439][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.26-11.36.03:440][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.26-11.36.03:444][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.06.26-11.36.03:444][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.06.26-11.36.03:444][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.26-11.36.03:444][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.26-11.36.03:444][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.26-11.36.03:445][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.26-11.36.03:445][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.26-11.36.03:446][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.26-11.36.03:449][  0]LogRHI: Using Default RHI: D3D12
[2025.06.26-11.36.03:449][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.26-11.36.03:449][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.26-11.36.03:451][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.26-11.36.03:452][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.26-11.36.03:655][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2060 (VendorId: 10de, DeviceId: 1f08, SubSysId: 37551462, Revision: 00a1
[2025.06.26-11.36.03:655][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.26-11.36.03:655][  0]LogD3D12RHI:   Adapter has 5954MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 2 output[s], UMA:false
[2025.06.26-11.36.03:656][  0]LogD3D12RHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.26-11.36.03:656][  0]LogD3D12RHI:      Driver Date: 6-1-2024
[2025.06.26-11.36.03:663][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.26-11.36.03:663][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.26-11.36.03:663][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 0 output[s], UMA:true
[2025.06.26-11.36.03:663][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.26-11.36.03:663][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.26-11.36.03:663][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.26-11.36.03:663][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.26-11.36.03:663][  0]LogHAL: Display: Platform has ~ 64 GB [68619431936 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.26-11.36.03:664][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.26-11.36.03:664][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.26-11.36.03:664][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.26-11.36.03:664][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.26-11.36.03:664][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.26-11.36.03:664][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.26-11.36.03:664][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.26-11.36.03:664][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.26-11.36.03:664][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.26-11.36.03:664][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.26-11.36.03:664][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.26-11.36.03:664][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.26-11.36.03:664][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Y:/UE Project/ToonTank/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.26-11.36.03:664][  0]LogInit: Computer: ADMINISTRATOR
[2025.06.26-11.36.03:664][  0]LogInit: User: maxwe
[2025.06.26-11.36.03:664][  0]LogInit: CPU Page size=4096, Cores=8
[2025.06.26-11.36.03:664][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.26-11.36.03:664][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.26-11.36.03:664][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.06.26-11.36.03:664][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.26-11.36.03:664][  0]LogMemory: Process Physical Memory: 638.48 MB used, 656.23 MB peak
[2025.06.26-11.36.03:665][  0]LogMemory: Process Virtual Memory: 659.07 MB used, 659.07 MB peak
[2025.06.26-11.36.03:665][  0]LogMemory: Physical Memory: 21644.54 MB used,  43796.05 MB free, 65440.59 MB total
[2025.06.26-11.36.03:665][  0]LogMemory: Virtual Memory: 23554.71 MB used,  51613.88 MB free, 75168.59 MB total
[2025.06.26-11.36.03:665][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.26-11.36.03:667][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.26-11.36.03:669][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.26-11.36.03:669][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.26-11.36.03:670][  0]LogInit: Using OS detected language (en-GB).
[2025.06.26-11.36.03:670][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.26-11.36.03:674][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.26-11.36.03:675][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.06.26-11.36.04:239][  0]LogWindowsTextInputMethodSystem: Activated input method: Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.26-11.36.04:249][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.26-11.36.04:251][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.06.26-11.36.04:257][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.26-11.36.04:257][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.26-11.36.04:427][  0]LogRHI: Using Default RHI: D3D12
[2025.06.26-11.36.04:427][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.26-11.36.04:427][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.26-11.36.04:427][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.26-11.36.04:427][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.26-11.36.04:427][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.06.26-11.36.04:427][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.26-11.36.04:428][  0]LogWindows: Attached monitors:
[2025.06.26-11.36.04:428][  0]LogWindows:     resolution: 1920x1080, work area: (1920, 0) -> (3840, 1040), device: '\\.\DISPLAY1'
[2025.06.26-11.36.04:428][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1040), device: '\\.\DISPLAY2' [PRIMARY]
[2025.06.26-11.36.04:428][  0]LogWindows: Found 2 attached monitors.
[2025.06.26-11.36.04:428][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.26-11.36.04:428][  0]LogRHI: RHI Adapter Info:
[2025.06.26-11.36.04:428][  0]LogRHI:             Name: NVIDIA GeForce RTX 2060
[2025.06.26-11.36.04:428][  0]LogRHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.26-11.36.04:428][  0]LogRHI:      Driver Date: 6-1-2024
[2025.06.26-11.36.04:428][  0]LogD3D12RHI:     GPU DeviceId: 0x1f08 (for the marketing name, search the web for "GPU Device Id")
[2025.06.26-11.36.04:428][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.26-11.36.04:525][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.26-11.36.04:525][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.26-11.36.04:591][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.06.26-11.36.04:591][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Raster order views are supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.26-11.36.04:591][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.26-11.36.04:691][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000020A20F022C0)
[2025.06.26-11.36.04:691][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000020A20F02580)
[2025.06.26-11.36.04:692][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000020A20F02840)
[2025.06.26-11.36.04:692][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.26-11.36.04:692][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.26-11.36.04:901][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.06.26-11.36.04:901][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.06.26-11.36.04:901][  0]LogRHI: Texture pool is 3267 MB (70% of 4667 MB)
[2025.06.26-11.36.04:901][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.26-11.36.04:901][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.26-11.36.04:919][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.26-11.36.04:923][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.06.26-11.36.04:923][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.06.26-11.36.04:923][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.06.26-11.36.04:923][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.06.26-11.36.04:923][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.26-11.36.04:926][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all'
[2025.06.26-11.36.04:926][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all" ]
[2025.06.26-11.36.04:937][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.06.26-11.36.04:937][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.26-11.36.04:937][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.26-11.36.04:937][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.26-11.36.04:937][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.26-11.36.04:937][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.26-11.36.04:937][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.06.26-11.36.04:937][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.06.26-11.36.04:938][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.26-11.36.04:938][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.26-11.36.04:977][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.26-11.36.04:994][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.26-11.36.04:995][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.26-11.36.05:012][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.26-11.36.05:013][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.26-11.36.05:013][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.26-11.36.05:013][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.26-11.36.05:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.26-11.36.05:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.26-11.36.05:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.26-11.36.05:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.26-11.36.05:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.26-11.36.05:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.26-11.36.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.26-11.36.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.26-11.36.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.26-11.36.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.26-11.36.05:065][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.26-11.36.05:093][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.26-11.36.05:101][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.26-11.36.05:102][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.26-11.36.05:102][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.06.26-11.36.05:106][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.26-11.36.05:106][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.26-11.36.05:106][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.26-11.36.05:106][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.26-11.36.05:106][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.26-11.36.05:381][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.06.26-11.36.05:381][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.26-11.36.05:381][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.26-11.36.05:382][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.26-11.36.05:382][  0]LogZenServiceInstance: InTree version at 'Y:/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.26-11.36.05:382][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.26-11.36.05:383][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.26-11.36.05:383][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 264  --child-id Zen_264_Startup'
[2025.06.26-11.36.05:473][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.26-11.36.05:474][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.092 seconds
[2025.06.26-11.36.05:475][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.26-11.36.05:482][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.26-11.36.05:482][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=1022.42MBs, RandomWriteSpeed=191.35MBs. Assigned SpeedClass 'Local'
[2025.06.26-11.36.05:483][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.26-11.36.05:483][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.26-11.36.05:483][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.26-11.36.05:484][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.26-11.36.05:484][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.26-11.36.05:484][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.26-11.36.05:484][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.26-11.36.05:484][  0]LogShaderCompilers: Guid format shader working directory is 16 characters bigger than the processId version (../../../../UE Project/ToonTank/Intermediate/Shaders/WorkingDirectory/264/).
[2025.06.26-11.36.05:484][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/0479FD1F4D22428C277240B1B145D36F/'.
[2025.06.26-11.36.05:496][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.06.26-11.36.05:496][  0]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.06.26-11.36.05:507][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.06.26-11.36.05:507][  0]LogShaderCompilers: Display: Using XGE Controller for shader compilation
[2025.06.26-11.36.05:507][  0]LogShaderCompilers: Display: Using 8 local workers for shader compilation
[2025.06.26-11.36.05:508][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../UE Project/ToonTank/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.26-11.36.05:508][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.26-11.36.06:258][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.26-11.36.07:290][  0]LogSlate: Using FreeType 2.10.0
[2025.06.26-11.36.07:291][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.26-11.36.07:294][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.26-11.36.07:294][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.26-11.36.07:294][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.26-11.36.07:294][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.26-11.36.07:321][  0]LogAssetRegistry: FAssetRegistry took 0.0033 seconds to start up
[2025.06.26-11.36.07:323][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.26-11.36.07:350][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.002s loading caches ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.26-11.36.07:647][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.26-11.36.07:647][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.26-11.36.07:695][  0]LogDeviceProfileManager: Active device profile: [0000020A51DAA500][0000020A4EEA2800 66] WindowsEditor
[2025.06.26-11.36.07:695][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.26-11.36.07:698][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.26-11.36.07:700][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.26-11.36.07:700][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.26-11.36.07:700][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.26-11.36.07:711][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.26-11.36.07:712][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR'
[2025.06.26-11.36.07:712][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR" -nocompile -nocompileuat ]
[2025.06.26-11.36.07:742][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:743][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.06.26-11.36.07:743][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:743][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:744][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.06.26-11.36.07:744][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:744][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:745][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.06.26-11.36.07:745][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.06.26-11.36.07:747][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.06.26-11.36.07:747][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:748][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:748][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:749][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.06.26-11.36.07:749][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.06.26-11.36.07:751][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.06.26-11.36.07:751][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:787][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.06.26-11.36.07:787][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:806][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.06.26-11.36.07:806][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.07:821][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.06.26-11.36.07:821][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.26-11.36.08:030][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.26-11.36.08:030][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.26-11.36.08:030][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.26-11.36.08:030][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.26-11.36.08:030][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.26-11.36.08:549][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.06.26-11.36.08:598][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.26-11.36.08:598][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.26-11.36.08:598][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.06.26-11.36.08:598][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.06.26-11.36.08:602][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.26-11.36.08:602][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.26-11.36.08:603][  0]LogLiveCoding: Display: First instance in process group "UE_ToonTank_0x08ddcd9e", spawning console
[2025.06.26-11.36.08:607][  0]LogLiveCoding: Display: Waiting for server
[2025.06.26-11.36.08:624][  0]LogSlate: Border
[2025.06.26-11.36.08:624][  0]LogSlate: BreadcrumbButton
[2025.06.26-11.36.08:624][  0]LogSlate: Brushes.Title
[2025.06.26-11.36.08:624][  0]LogSlate: ColorPicker.ColorThemes
[2025.06.26-11.36.08:624][  0]LogSlate: Default
[2025.06.26-11.36.08:624][  0]LogSlate: Icons.Save
[2025.06.26-11.36.08:624][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.26-11.36.08:624][  0]LogSlate: ListView
[2025.06.26-11.36.08:624][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.26-11.36.08:624][  0]LogSlate: SoftwareCursor_Grab
[2025.06.26-11.36.08:624][  0]LogSlate: TableView.DarkRow
[2025.06.26-11.36.08:624][  0]LogSlate: TableView.Row
[2025.06.26-11.36.08:624][  0]LogSlate: TreeView
[2025.06.26-11.36.08:702][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.26-11.36.08:706][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 3.794 ms
[2025.06.26-11.36.08:726][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.26-11.36.08:726][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.26-11.36.08:726][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.26-11.36.08:762][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.26-11.36.08:773][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 223B6F08C276407F8000000000009000 | Instance: 260C212A4813A40C9D873EAACC6E08D1 (ADMINISTRATOR-264).
[2025.06.26-11.36.08:809][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   NPU:       no
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: Interface availability:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   GPU: yes
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   RDG: yes
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT:   NPU: no
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.26-11.36.08:865][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.26-11.36.09:003][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.26-11.36.09:004][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.26-11.36.09:038][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.26-11.36.09:454][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.26-11.36.09:492][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.26-11.36.09:496][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.26-11.36.09:496][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.26-11.36.09:496][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:54629'.
[2025.06.26-11.36.09:503][  0]LogUdpMessaging: Display: Added local interface '192.168.0.177' to multicast group '230.0.0.1:6666'
[2025.06.26-11.36.09:507][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.06.26-11.36.09:538][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.06.26-11.36.09:538][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.06.26-11.36.09:544][  0]LogTimingProfiler: Initialize
[2025.06.26-11.36.09:544][  0]LogTimingProfiler: OnSessionChanged
[2025.06.26-11.36.09:544][  0]LoadingProfiler: Initialize
[2025.06.26-11.36.09:545][  0]LoadingProfiler: OnSessionChanged
[2025.06.26-11.36.09:545][  0]LogNetworkingProfiler: Initialize
[2025.06.26-11.36.09:545][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.26-11.36.09:545][  0]LogMemoryProfiler: Initialize
[2025.06.26-11.36.09:545][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.26-11.36.09:610][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.26-11.36.09:614][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.26-11.36.09:624][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.26-11.36.09:624][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.26-11.36.09:768][  0]SourceControl: Revision control is disabled
[2025.06.26-11.36.09:778][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.26-11.36.09:778][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.26-11.36.09:778][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.26-11.36.09:778][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.26-11.36.09:784][  0]SourceControl: Revision control is disabled
[2025.06.26-11.36.10:019][  0]LogCollectionManager: Loaded 0 collections in 0.001355 seconds
[2025.06.26-11.36.10:021][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Saved/Collections/' took 0.00s
[2025.06.26-11.36.10:025][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Developers/maxwe/Collections/' took 0.00s
[2025.06.26-11.36.10:028][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Collections/' took 0.00s
[2025.06.26-11.36.10:055][  0]LogTurnkeySupport: Turnkey Device: Win64@Administrator: (Name=Administrator, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.06.26-11.36.10:060][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.26-11.36.10:060][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.26-11.36.10:060][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.26-11.36.10:060][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.26-11.36.10:082][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.26-11.36.10:083][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.26-11.36.10:083][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.26-11.36.10:083][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.26-11.36.10:104][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-06-26T11:36:10.104Z using C
[2025.06.26-11.36.10:104][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=ToonTank, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.26-11.36.10:104][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.26-11.36.10:105][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.26-11.36.10:111][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.26-11.36.10:112][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.26-11.36.10:112][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.26-11.36.10:112][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000071
[2025.06.26-11.36.10:170][  0]LogUObjectArray: 42354 objects as part of root set at end of initial load.
[2025.06.26-11.36.10:170][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.26-11.36.10:293][  0]LogEngine: Initializing Engine...
[2025.06.26-11.36.10:446][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.26-11.36.10:446][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.06.26-11.36.10:702][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.26-11.36.10:717][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.26-11.36.10:726][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.26-11.36.10:736][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.06.26-11.36.10:736][  0]LogInit: Texture streaming: Enabled
[2025.06.26-11.36.10:742][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.06.26-11.36.10:746][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.26-11.36.10:748][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.26-11.36.10:748][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.26-11.36.10:749][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.26-11.36.10:749][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.26-11.36.10:749][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.26-11.36.10:749][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.26-11.36.10:749][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.26-11.36.10:749][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.26-11.36.10:749][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.26-11.36.10:755][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.26-11.36.10:873][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.26-11.36.10:873][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.26-11.36.10:875][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.26-11.36.10:875][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.26-11.36.10:876][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.26-11.36.10:876][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.26-11.36.10:878][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.26-11.36.10:878][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.26-11.36.10:878][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.26-11.36.10:878][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.26-11.36.10:879][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.26-11.36.10:884][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.26-11.36.10:889][  0]LogInit: Undo buffer set to 256 MB
[2025.06.26-11.36.10:889][  0]LogInit: Transaction tracking system initialized
[2025.06.26-11.36.10:896][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.26-11.36.10:931][  0]LocalizationService: Localization service is disabled
[2025.06.26-11.36.11:078][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/' took 0.00s
[2025.06.26-11.36.11:118][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.06.26-11.36.11:119][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.06.26-11.36.11:119][  0]LogPython: Using Python 3.11.8
[2025.06.26-11.36.11:147][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.06.26-11.36.11:494][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.26-11.36.11:578][  0]LogEditorDataStorage: Initializing
[2025.06.26-11.36.11:581][  0]LogEditorDataStorage: Initialized
[2025.06.26-11.36.11:584][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.26-11.36.11:603][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.26-11.36.11:688][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.26-11.36.11:691][  0]SourceControl: Revision control is disabled
[2025.06.26-11.36.11:691][  0]LogUnrealEdMisc: Loading editor; pre map load, took 9.276
[2025.06.26-11.36.11:693][  0]Cmd: MAP LOAD FILE="../../../../UE Project/ToonTank/Content/Maps/Main.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.26-11.36.11:695][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.26-11.36.11:695][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-11.36.11:717][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.26-11.36.11:720][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.06.26-11.36.11:813][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Main'.
[2025.06.26-11.36.11:814][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.26-11.36.11:832][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.26-11.36.11:857][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.06.26-11.36.11:858][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.26-11.36.11:858][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.064ms to complete.
[2025.06.26-11.36.11:865][  0]LogUnrealEdMisc: Total Editor Startup Time, took 9.450
[2025.06.26-11.36.11:966][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.06.26-11.36.12:097][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.12:190][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.12:282][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.12:373][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.12:414][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:415][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.26-11.36.12:415][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:415][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.26-11.36.12:416][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:416][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:416][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:416][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:417][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:417][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.26-11.36.12:417][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:418][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:418][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:418][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.26-11.36.12:418][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:419][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:419][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:419][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:419][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.26-11.36.12:419][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.26-11.36.12:502][  0]LogSlate: Took 0.000141 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.26-11.36.12:649][  0]LogAssetRegistry: Display: Asset registry cache written as 66.6 MiB to ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin
[2025.06.26-11.36.12:722][  0]LogSlate: Took 0.000179 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.26-11.36.12:772][  0]LogSlate: Took 0.000229 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.26-11.36.13:258][  0]LogStall: Startup...
[2025.06.26-11.36.13:261][  0]LogStall: Startup complete.
[2025.06.26-11.36.13:284][  0]LogLoad: (Engine Initialization) Total time: 10.87 seconds
[2025.06.26-11.36.13:674][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.26-11.36.13:674][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.26-11.36.13:839][  0]LogSlate: Took 0.000168 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.26-11.36.13:894][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-11.36.13:897][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.26-11.36.13:931][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.26-11.36.13:933][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 36.991 ms
[2025.06.26-11.36.13:933][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: ToonTankEditor Win64 Development
[2025.06.26-11.36.14:310][  1]LogAssetRegistry: AssetRegistryGather time 0.1537s: AssetDataDiscovery 0.0274s, AssetDataGather 0.0374s, StoreResults 0.0889s. Wall time 6.9920s.
	NumCachedDirectories 0. NumUncachedDirectories 1470. NumCachedFiles 7411. NumUncachedFiles 0.
	BackgroundTickInterruptions 12.
[2025.06.26-11.36.14:336][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.06.26-11.36.14:351][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.26-11.36.14:351][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.06.26-11.36.14:511][  2]LogSlate: Took 0.000124 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.26-11.36.14:550][  3]LogSourceControl: Uncontrolled asset discovery finished in 0.198882 seconds (Found 7387 uncontrolled assets)
[2025.06.26-11.36.14:663][  4]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.26-11.36.14:914][ 10]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 4.787139
[2025.06.26-11.36.14:915][ 10]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.26-11.36.14:916][ 10]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4.802299
[2025.06.26-11.36.15:219][ 34]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.26-11.36.15:497][ 59]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 5.373925
[2025.06.26-11.36.15:499][ 59]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.26-11.36.15:499][ 59]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5.373925, Update Interval: 310.680878
[2025.06.26-11.36.32:033][226]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.26-11.36.32:040][226]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.26-11.36.32:045][226]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.26-11.36.32:046][226]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.26-11.36.32:046][226]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.26-11.36.32:051][226]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005180s)
[2025.06.26-11.36.32:051][226]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005260s)
[2025.06.26-11.36.32:141][226]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.06.26-11.36.32:143][226]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.26-11.36.32:145][226]LogPlayLevel: PIE: World Init took: (0.002158s)
[2025.06.26-11.36.32:146][226]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.26-11.36.32:146][226]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.26-11.36.32:146][226]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.26-11.36.32:146][226]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.26-11.36.32:146][226]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.26-11.36.32:146][226]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.26-11.36.32:148][226]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.26-11.36.32:245][226]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.26-11.36.32:245][226]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.26-11.36.32:245][226]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.26-11.36.32:245][226]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.26-11.36.32:246][226]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.26-11.36.32:246][226]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.26-11.36.32:249][226]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.26-11.36.32:249][226]LogInit: FAudioDevice initialized with ID 2.
[2025.06.26-11.36.32:249][226]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.26-11.36.32:249][226]LogAudio: Display: Audio Device (ID: 2) registered with world 'Main'.
[2025.06.26-11.36.32:249][226]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.26-11.36.32:255][226]LogLoad: Game class is 'GameModeBase'
[2025.06.26-11.36.32:258][226]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.26-19.36.32
[2025.06.26-11.36.32:259][226]LogWorld: Bringing up level for play took: 0.004008
[2025.06.26-11.36.32:262][226]LogOnline: OSS: Created online subsystem instance for: :Context_2
[2025.06.26-11.36.32:266][226]PIE: Server logged in
[2025.06.26-11.36.32:268][226]PIE: Play in editor total start time 0.228 seconds.
[2025.06.26-11.36.38:303][853]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.38:303][853]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.26-11.36.38:304][853]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.26-11.36.38:304][853]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-11.36.38:306][853]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.26-11.36.38:312][853]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-11.36.38:337][853]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-11.36.38:337][853]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.26-11.36.38:337][853]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.06.26-11.36.38:340][853]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.06.26-11.36.38:350][853]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.06.26-11.36.38:454][854]LogPlayLevel: Display: Destroying online subsystem :Context_2
[2025.06.26-11.36.38:882][886]Cmd: SELECT NONE
[2025.06.26-11.38.05:484][800]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.26-11.41.26:812][403]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 316.700775
[2025.06.26-11.41.27:814][406]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-11.41.27:814][406]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 317.368317, Update Interval: 321.297638
[2025.06.26-11.46.52:596][380]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 642.483582
[2025.06.26-11.46.53:597][383]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-11.46.53:597][383]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 643.150452, Update Interval: 300.067749
[2025.06.26-11.52.00:998][305]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 950.885620
[2025.06.26-11.52.01:999][308]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-11.52.01:999][308]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 951.555237, Update Interval: 338.821381
[2025.06.26-11.57.49:403][350]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1299.290405
[2025.06.26-11.57.50:404][353]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-11.57.50:404][353]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1299.957520, Update Interval: 333.666809
[2025.06.26-12.03.40:484][403]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1650.370850
[2025.06.26-12.03.41:484][406]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.03.41:484][406]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1651.039551, Update Interval: 321.618103
[2025.06.26-12.09.53:556][522]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2023.442749
[2025.06.26-12.09.54:555][525]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.09.54:555][525]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2024.109497, Update Interval: 338.684052
[2025.06.26-12.16.12:812][659]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2402.692871
[2025.06.26-12.16.13:813][662]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.16.13:813][662]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2403.359619, Update Interval: 330.101624
[2025.06.26-12.22.21:912][766]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2771.796143
[2025.06.26-12.22.22:913][769]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.22.22:913][769]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2772.465088, Update Interval: 308.547638
[2025.06.26-12.28.07:989][804]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3117.874268
[2025.06.26-12.28.08:989][807]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.28.08:989][807]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3118.541016, Update Interval: 332.824493
[2025.06.26-12.34.30:429][951]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3500.314209
[2025.06.26-12.34.31:429][954]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.34.31:429][954]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3500.981445, Update Interval: 329.665833
[2025.06.26-12.38.05:484][596]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.26-12.40.57:212][111]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3887.098877
[2025.06.26-12.40.58:213][114]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.40.58:213][114]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3887.765869, Update Interval: 352.356934
[2025.06.26-12.42.10:143][330]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Headset (soundcore Liberty 4 NC Hands-Free AG Audio), InstanceID=1
[2025.06.26-12.42.10:143][330]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{2d2c91eb-aaac-429e-a7dc-ba7eafbad167}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-12.42.10:233][330]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{2d2c91eb-aaac-429e-a7dc-ba7eafbad167}]
[2025.06.26-12.42.10:233][330]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.26-12.42.10:233][330]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.26-12.42.10:902][332]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=16000, DeviceID={0.0.0.00000000}.{2d2c91eb-aaac-429e-a7dc-ba7eafbad167}, Name=Headset (soundcore Liberty 4 NC Hands-Free AG Audio)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=599.15
[2025.06.26-12.42.10:902][332]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.26-12.42.10:902][332]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.26-12.42.10:904][332]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.26-12.46.55:825][279]LogWindowsTextInputMethodSystem: Activated input method: Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.26-12.46.57:310][418]Running Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.26-12.46.57:415][418]Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.06.26-12.46.57:415][418]Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.26-12.46.58:322][418]Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log_GPF.txt
[2025.06.26-12.46.58:523][418]
[2025.06.26-12.46.58:523][418]Generating VisualStudioCode project files:
[2025.06.26-12.46.58:523][418]Discovering modules, targets and source code for project...
[2025.06.26-12.46.58:623][418]Adding projects for all targets...
[2025.06.26-12.46.59:026][418]Available x64 toolchains (1):
[2025.06.26-12.46.59:026][418] * Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.06.26-12.46.59:026][418]    (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.06.26-12.46.59:026][418]Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.06.26-12.46.59:026][418]Adding projects for all targets took 0.45s
[2025.06.26-12.46.59:026][418]Adding projects for all game projects took 0.00s
[2025.06.26-12.46.59:430][418]Adding projects for all modules took 0.36s
[2025.06.26-12.46.59:531][418]Compiling Rules Assemblies
[2025.06.26-12.46.59:632][418]Compiling Rules Assemblies 100%
[2025.06.26-12.46.59:632][418]Creating Build Targets
[2025.06.26-12.47.01:660][418]Creating Build Targets 100%
[2025.06.26-12.47.01:660][418]Creating Build Targets took 2.08s
[2025.06.26-12.47.01:660][418]Binding IntelliSense data...
[2025.06.26-12.47.06:116][418]Binding IntelliSense data... 100%
[2025.06.26-12.47.06:116][418]Binding IntelliSense data took 4.48s
[2025.06.26-12.47.06:117][418]Writing project files...
[2025.06.26-12.47.06:925][418]Writing project files... 100%
[2025.06.26-12.47.06:925][418]Writing project files took 0.78s
[2025.06.26-12.47.06:925][418]
[2025.06.26-12.47.06:925][418]Generating QueryTargets data for editor...
[2025.06.26-12.47.06:925][418]Compiling Rules Assemblies
[2025.06.26-12.47.06:925][418]Compiling Rules Assemblies 100%
[2025.06.26-12.47.06:925][418]Writing Query Target Info
[2025.06.26-12.47.06:925][418]Writing Query Target Info 100%
[2025.06.26-12.47.06:925][418]Generating QueryTargets data for editor took 0.02s
[2025.06.26-12.47.06:925][418]
[2025.06.26-12.47.06:925][418]Result: Succeeded
[2025.06.26-12.47.06:925][418]Total execution time: 9.19 seconds
[2025.06.26-12.47.33:570][527]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4283.457031
[2025.06.26-12.47.34:567][529]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.47.34:567][529]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4284.122070, Update Interval: 349.315460
[2025.06.26-12.54.02:715][693]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4672.614258
[2025.06.26-12.54.03:716][696]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-12.54.03:716][696]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4673.281738, Update Interval: 311.285126
[2025.06.26-13.00.00:155][765]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5030.062012
[2025.06.26-13.00.01:157][768]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.00.01:157][768]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5030.729980, Update Interval: 304.583282
[2025.06.26-13.05.55:267][830]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5385.180664
[2025.06.26-13.05.56:270][833]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.05.56:270][833]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5385.848145, Update Interval: 307.697998
[2025.06.26-13.06.48:511][990]LogWindowsTextInputMethodSystem: Activated input method: English (United Kingdom) - (Keyboard).
[2025.06.26-13.06.48:511][990]LogWindowsTextInputMethodSystem: Activated input method: English (United Kingdom) - (Keyboard).
[2025.06.26-13.06.50:401][172]LogLiveCoding: Display: Starting Live Coding compile.
[2025.06.26-13.06.56:245][389]LogLiveCoding: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debug9ff05af4-8b26-4667-8c15-112608716b25 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\D30F.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_0.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_0.exp
Detoured run took 1.1s
[2025.06.26-13.06.56:763][389]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.06.26-13.06.56:900][390]Display: Reload/Re-instancing Complete: 1 package changed, 2 classes unchanged
[2025.06.26-13.06.56:900][390]LogLiveCoding: Display: Live coding succeeded
[2025.06.26-13.07.20:920][916]LogLiveCoding: Display: Starting Live Coding compile.
[2025.06.26-13.07.24:653][305]LogLiveCoding: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debugeb6c0707-1025-4e83-b8b5-db0f846cb383 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\409F.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_1.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_1.exp
Detoured run took 1.4s
[2025.06.26-13.07.24:905][315]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.06.26-13.07.24:964][316]Display: Reload/Re-instancing Complete: 1 package changed, 2 classes unchanged
[2025.06.26-13.07.24:964][316]LogLiveCoding: Display: Live coding succeeded
[2025.06.26-13.07.26:609][444]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.26-13.07.26:617][444]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.26-13.07.26:645][444]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.07.26:650][444]LogPlayLevel: PIE: StaticDuplicateObject took: (0.004927s)
[2025.06.26-13.07.26:650][444]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.004983s)
[2025.06.26-13.07.26:671][444]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.06.26-13.07.26:672][444]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.26-13.07.26:674][444]LogPlayLevel: PIE: World Init took: (0.002761s)
[2025.06.26-13.07.26:675][444]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.26-13.07.26:676][444]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.26-13.07.26:676][444]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.26-13.07.26:676][444]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.26-13.07.26:676][444]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.26-13.07.26:676][444]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.26-13.07.26:678][444]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.26-13.07.26:702][444]LogAudioMixer: Display: Using Audio Hardware Device Headset (soundcore Liberty 4 NC Hands-Free AG Audio)
[2025.06.26-13.07.26:702][444]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.26-13.07.26:702][444]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.26-13.07.26:702][444]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.26-13.07.26:703][444]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.26-13.07.26:703][444]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.26-13.07.26:706][444]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.26-13.07.26:706][444]LogInit: FAudioDevice initialized with ID 3.
[2025.06.26-13.07.26:706][444]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.26-13.07.26:706][444]LogAudio: Display: Audio Device (ID: 3) registered with world 'Main'.
[2025.06.26-13.07.26:706][444]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.26-13.07.26:710][444]LogLoad: Game class is 'GameModeBase'
[2025.06.26-13.07.26:712][444]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.26-21.07.26
[2025.06.26-13.07.26:712][444]LogWorld: Bringing up level for play took: 0.001947
[2025.06.26-13.07.26:715][444]LogOnline: OSS: Created online subsystem instance for: :Context_3
[2025.06.26-13.07.26:717][444]PIE: Server logged in
[2025.06.26-13.07.26:719][444]PIE: Play in editor total start time 0.103 seconds.
[2025.06.26-13.08.00:927][421]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.08.00:927][421]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.08.00:927][421]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.26-13.08.00:927][421]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.08.00:928][421]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.26-13.08.00:932][421]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.08.00:952][421]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.08.00:952][421]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.26-13.08.00:953][421]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=4
[2025.06.26-13.08.00:955][421]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=2
[2025.06.26-13.08.00:961][421]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.06.26-13.08.01:072][423]LogPlayLevel: Display: Destroying online subsystem :Context_3
[2025.06.26-13.09.19:046][285]LogLiveCoding: Display: Starting Live Coding compile.
[2025.06.26-13.09.22:002][293]LogLiveCoding: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debug53e6bdbb-5bf9-4d8c-919f-08545c04e57a ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\CB5.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_2.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_2.exp
Detoured run took 1.0s
[2025.06.26-13.09.22:504][294]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.06.26-13.09.22:705][295]Display: Reload/Re-instancing Complete: 1 package changed, 2 classes unchanged
[2025.06.26-13.09.22:706][295]LogLiveCoding: Display: Live coding succeeded
[2025.06.26-13.09.23:664][356]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.26-13.09.23:670][356]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.26-13.09.23:670][356]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.09.23:675][356]LogPlayLevel: PIE: StaticDuplicateObject took: (0.004948s)
[2025.06.26-13.09.23:676][356]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.004986s)
[2025.06.26-13.09.23:696][356]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.06.26-13.09.23:697][356]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.26-13.09.23:699][356]LogPlayLevel: PIE: World Init took: (0.002033s)
[2025.06.26-13.09.23:700][356]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.26-13.09.23:700][356]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.26-13.09.23:700][356]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.26-13.09.23:700][356]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.26-13.09.23:700][356]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.26-13.09.23:700][356]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.26-13.09.23:702][356]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.26-13.09.23:726][356]LogAudioMixer: Display: Using Audio Hardware Device Headset (soundcore Liberty 4 NC Hands-Free AG Audio)
[2025.06.26-13.09.23:726][356]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.26-13.09.23:726][356]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.26-13.09.23:726][356]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.26-13.09.23:727][356]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.26-13.09.23:727][356]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.26-13.09.23:729][356]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.26-13.09.23:729][356]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.26-13.09.23:729][356]LogInit: FAudioDevice initialized with ID 4.
[2025.06.26-13.09.23:729][356]LogAudio: Display: Audio Device (ID: 4) registered with world 'Main'.
[2025.06.26-13.09.23:729][356]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.26-13.09.23:733][356]LogLoad: Game class is 'GameModeBase'
[2025.06.26-13.09.23:735][356]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.26-21.09.23
[2025.06.26-13.09.23:735][356]LogWorld: Bringing up level for play took: 0.002035
[2025.06.26-13.09.23:738][356]LogOnline: OSS: Created online subsystem instance for: :Context_4
[2025.06.26-13.09.23:740][356]PIE: Server logged in
[2025.06.26-13.09.23:742][356]PIE: Play in editor total start time 0.072 seconds.
[2025.06.26-13.09.38:426][999]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.09.38:426][999]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.09.38:426][999]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.26-13.09.38:426][999]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.09.38:428][999]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.26-13.09.38:432][999]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.09.38:452][999]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.09.38:453][999]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.26-13.09.38:453][999]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=4
[2025.06.26-13.09.38:455][999]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=2
[2025.06.26-13.09.38:461][999]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.06.26-13.09.38:543][  1]LogPlayLevel: Display: Destroying online subsystem :Context_4
[2025.06.26-13.09.46:456][504]LogLiveCoding: Display: Starting Live Coding compile.
[2025.06.26-13.09.50:322][903]LogLiveCoding: Display: UbaCli v5.7.0-Uba_v1.0.0-42560232 (Rootdir: "C:\ProgramData\Epic\UbaCli", StoreCapacity: 20Gb)

---- Starting trace: Debuga5c2e873-fff5-40d5-ac8b-50ffcdf94693 ----
Running Y:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe @C:\Users\<USER>\AppData\Local\Temp\79E7.tmp
   Creating library Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_3.lib and object Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.patch_3.exp
Detoured run took 1.4s
[2025.06.26-13.09.50:574][912]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.06.26-13.09.50:625][913]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.26-13.09.50:635][913]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.26-13.09.50:635][913]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.09.50:640][913]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005394s)
[2025.06.26-13.09.50:640][913]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005437s)
[2025.06.26-13.09.50:664][913]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.06.26-13.09.50:665][913]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.26-13.09.50:667][913]LogPlayLevel: PIE: World Init took: (0.002112s)
[2025.06.26-13.09.50:668][913]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.26-13.09.50:668][913]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.26-13.09.50:669][913]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.26-13.09.50:669][913]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.26-13.09.50:669][913]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.26-13.09.50:669][913]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.26-13.09.50:669][913]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.26-13.09.50:671][913]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.26-13.09.50:695][913]LogAudioMixer: Display: Using Audio Hardware Device Headset (soundcore Liberty 4 NC Hands-Free AG Audio)
[2025.06.26-13.09.50:696][913]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.26-13.09.50:696][913]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.26-13.09.50:696][913]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.26-13.09.50:696][913]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.06.26-13.09.50:696][913]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.06.26-13.09.50:699][913]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.06.26-13.09.50:699][913]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.06.26-13.09.50:699][913]LogInit: FAudioDevice initialized with ID 5.
[2025.06.26-13.09.50:699][913]LogAudio: Display: Audio Device (ID: 5) registered with world 'Main'.
[2025.06.26-13.09.50:699][913]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.06.26-13.09.50:706][913]LogLoad: Game class is 'GameModeBase'
[2025.06.26-13.09.50:709][913]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.26-21.09.50
[2025.06.26-13.09.50:709][913]LogWorld: Bringing up level for play took: 0.002495
[2025.06.26-13.09.50:712][913]LogOnline: OSS: Created online subsystem instance for: :Context_5
[2025.06.26-13.09.50:714][913]PIE: Server logged in
[2025.06.26-13.09.50:716][913]PIE: Play in editor total start time 0.083 seconds.
[2025.06.26-13.09.50:730][913]Display: Reload/Re-instancing Complete: 1 package changed, 2 classes unchanged
[2025.06.26-13.09.50:730][913]LogLiveCoding: Display: Live coding succeeded
[2025.06.26-13.10.07:385][705]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.10.07:385][705]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.26-13.10.07:385][705]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.26-13.10.07:386][705]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.10.07:387][705]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.26-13.10.07:392][705]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.26-13.10.07:411][705]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.26-13.10.07:411][705]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.06.26-13.10.07:411][705]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=4
[2025.06.26-13.10.07:414][705]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=2
[2025.06.26-13.10.07:419][705]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.06.26-13.10.07:461][706]LogPlayLevel: Display: Destroying online subsystem :Context_5
[2025.06.26-13.12.00:173][995]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5750.105957
[2025.06.26-13.12.00:423][ 25]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.12.00:423][ 25]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5750.348633, Update Interval: 338.147522
[2025.06.26-13.16.48:768][497]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.48:768][497]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.48:777][498]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.48:777][498]LogAudioMixer: Display: Ignoring device swap request, AudioStreamInfo.StreamState: 5
[2025.06.26-13.16.48:777][498]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.48:778][498]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}]
[2025.06.26-13.16.48:778][498]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.26-13.16.48:778][498]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.26-13.16.48:968][520]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.48:969][520]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{2d2c91eb-aaac-429e-a7dc-ba7eafbad167}', because: 'FMixerPlatformXAudio2::OnSessionDisconnect() - FormatChanged', force=1
[2025.06.26-13.16.49:392][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:393][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:393][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:393][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:393][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:393][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:394][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:394][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:394][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:395][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:395][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:395][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:396][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:396][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:396][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:397][569]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Speakers (Realtek(R) Audio), InstanceID=1
[2025.06.26-13.16.49:397][569]LogAudioMixer: Display: Ignoring device swap request, swap already requested.
[2025.06.26-13.16.49:397][569]LogAudioMixer: Display: NOT-ALLOWING attempt to swap audio render device to new device: '{0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.26-13.16.49:405][570]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=48000, DeviceID={0.0.0.00000000}.{9b57218f-f7e1-4803-a74d-a3fbfa5f7c26}, Name=Speakers (Realtek(R) Audio)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=622.16
[2025.06.26-13.16.49:405][570]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.26-13.16.49:405][570]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.26-13.16.49:407][570]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.26-13.18.28:338][392]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6138.259277
[2025.06.26-13.18.29:228][498]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.18.29:228][498]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6139.139648, Update Interval: 331.522583
[2025.06.26-13.24.33:785][276]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6503.709473
[2025.06.26-13.24.34:786][279]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.24.34:786][279]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6504.376465, Update Interval: 335.102386
[2025.06.26-13.30.53:549][415]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6883.483887
[2025.06.26-13.30.54:549][418]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.30.54:549][418]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6884.151367, Update Interval: 312.942291
[2025.06.26-13.36.40:949][457]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7230.899902
[2025.06.26-13.36.41:950][460]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.26-13.36.41:950][460]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7231.567383, Update Interval: 315.269623
[2025.06.26-13.38.05:485][711]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
