﻿Log file open, 06/30/25 20:47:50
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=24020)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: ToonTank
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 37315
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.5965] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 7 3700X 8-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "Y:/UE Project/ToonTank/ToonTank.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="736956f4414f060eb3ef6884c4fdf35b"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.318379
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-7B2BCD57412A5FD6FE5B97A64A24196D
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../UE Project/ToonTank/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogAssetRegistry: Display: PlatformFileJournal is not available on volume 'Y:' of project directory 'Y:/UE Project/ToonTank/', so AssetDiscovery cache will not be read or written. Unavailability reason:
	NTFS Journal is not active for volume 'Y:'. Launch cmd.exe as admin and run command `fsutil usn createJournal Y: m=<SizeInBytes>`. Recommended <SizeInBytes> is 0x40000000 (1GB).
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading TVOS ini files took 0.07 seconds
LogConfig: Display: Loading Windows ini files took 0.07 seconds
LogConfig: Display: Loading VisionOS ini files took 0.07 seconds
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogAssetRegistry: Display: Asset registry cache read as 66.6 MiB from ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_0.bin.
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "Y:/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.30-12.47.50:718][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.30-12.47.50:718][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.30-12.47.50:719][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.30-12.47.50:721][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.06.30-12.47.50:721][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.30-12.47.50:722][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.30-12.47.50:723][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.30-12.47.50:723][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.30-12.47.50:723][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.30-12.47.50:723][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.30-12.47.50:726][  0]LogRHI: Using Default RHI: D3D12
[2025.06.30-12.47.50:726][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.30-12.47.50:726][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.30-12.47.50:728][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.30-12.47.50:728][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.30-12.47.50:912][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2060 (VendorId: 10de, DeviceId: 1f08, SubSysId: 37551462, Revision: 00a1
[2025.06.30-12.47.50:912][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.30-12.47.50:912][  0]LogD3D12RHI:   Adapter has 5954MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 2 output[s], UMA:false
[2025.06.30-12.47.50:912][  0]LogD3D12RHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.30-12.47.50:912][  0]LogD3D12RHI:      Driver Date: 6-1-2024
[2025.06.30-12.47.50:918][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.30-12.47.50:918][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.30-12.47.50:918][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 0 output[s], UMA:true
[2025.06.30-12.47.50:918][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.30-12.47.50:918][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.30-12.47.50:918][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.30-12.47.50:918][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.30-12.47.50:918][  0]LogHAL: Display: Platform has ~ 64 GB [68619431936 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.30-12.47.50:919][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.30-12.47.50:919][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.30-12.47.50:919][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.30-12.47.50:919][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.30-12.47.50:919][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.30-12.47.50:919][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.30-12.47.50:919][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.30-12.47.50:919][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.30-12.47.50:919][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.30-12.47.50:919][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.30-12.47.50:919][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.30-12.47.50:919][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.30-12.47.50:919][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Y:/UE Project/ToonTank/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.30-12.47.50:919][  0]LogInit: Computer: ADMINISTRATOR
[2025.06.30-12.47.50:919][  0]LogInit: User: maxwe
[2025.06.30-12.47.50:919][  0]LogInit: CPU Page size=4096, Cores=8
[2025.06.30-12.47.50:919][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.30-12.47.50:919][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.30-12.47.50:919][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.06.30-12.47.50:919][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.30-12.47.50:919][  0]LogMemory: Process Physical Memory: 635.73 MB used, 653.36 MB peak
[2025.06.30-12.47.50:919][  0]LogMemory: Process Virtual Memory: 651.39 MB used, 651.39 MB peak
[2025.06.30-12.47.50:919][  0]LogMemory: Physical Memory: 17271.50 MB used,  48169.09 MB free, 65440.59 MB total
[2025.06.30-12.47.50:919][  0]LogMemory: Virtual Memory: 18772.45 MB used,  56396.14 MB free, 75168.59 MB total
[2025.06.30-12.47.50:919][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.30-12.47.50:921][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.30-12.47.50:923][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.30-12.47.50:923][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.30-12.47.50:924][  0]LogInit: Using OS detected language (en-GB).
[2025.06.30-12.47.50:924][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.30-12.47.50:927][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.30-12.47.50:927][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.06.30-12.47.51:365][  0]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.06.30-12.47.51:374][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.30-12.47.51:376][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.06.30-12.47.51:380][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.30-12.47.51:381][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.30-12.47.51:518][  0]LogRHI: Using Default RHI: D3D12
[2025.06.30-12.47.51:518][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.30-12.47.51:518][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.30-12.47.51:518][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.30-12.47.51:518][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.30-12.47.51:518][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.06.30-12.47.51:518][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.30-12.47.51:519][  0]LogWindows: Attached monitors:
[2025.06.30-12.47.51:519][  0]LogWindows:     resolution: 1920x1080, work area: (1920, 0) -> (3840, 1040), device: '\\.\DISPLAY1'
[2025.06.30-12.47.51:519][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1040), device: '\\.\DISPLAY2' [PRIMARY]
[2025.06.30-12.47.51:519][  0]LogWindows: Found 2 attached monitors.
[2025.06.30-12.47.51:519][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.30-12.47.51:519][  0]LogRHI: RHI Adapter Info:
[2025.06.30-12.47.51:519][  0]LogRHI:             Name: NVIDIA GeForce RTX 2060
[2025.06.30-12.47.51:519][  0]LogRHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.30-12.47.51:519][  0]LogRHI:      Driver Date: 6-1-2024
[2025.06.30-12.47.51:519][  0]LogD3D12RHI:     GPU DeviceId: 0x1f08 (for the marketing name, search the web for "GPU Device Id")
[2025.06.30-12.47.51:519][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.30-12.47.51:606][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.30-12.47.51:606][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.30-12.47.51:664][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.06.30-12.47.51:664][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Raster order views are supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.30-12.47.51:664][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.30-12.47.51:748][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001E0387922C0)
[2025.06.30-12.47.51:748][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001E038792580)
[2025.06.30-12.47.51:748][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001E038792840)
[2025.06.30-12.47.51:748][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.30-12.47.51:748][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.30-12.47.51:906][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.06.30-12.47.51:906][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.06.30-12.47.51:906][  0]LogRHI: Texture pool is 3267 MB (70% of 4667 MB)
[2025.06.30-12.47.51:906][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.30-12.47.51:906][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.30-12.47.51:919][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.30-12.47.51:922][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.06.30-12.47.51:922][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.06.30-12.47.51:922][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.06.30-12.47.51:922][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.06.30-12.47.51:922][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.30-12.47.51:923][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all'
[2025.06.30-12.47.51:923][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all" ]
[2025.06.30-12.47.51:934][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.06.30-12.47.51:934][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.30-12.47.51:934][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.30-12.47.51:934][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.30-12.47.51:934][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.30-12.47.51:934][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.30-12.47.51:934][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.06.30-12.47.51:934][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.06.30-12.47.51:934][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.30-12.47.51:935][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.30-12.47.51:965][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.30-12.47.51:980][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.30-12.47.51:980][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.30-12.47.51:998][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.30-12.47.51:998][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.30-12.47.51:998][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.30-12.47.51:998][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.30-12.47.52:014][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.30-12.47.52:014][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.30-12.47.52:014][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.30-12.47.52:014][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.30-12.47.52:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.30-12.47.52:029][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.30-12.47.52:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.30-12.47.52:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.30-12.47.52:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.30-12.47.52:046][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.30-12.47.52:046][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.30-12.47.52:069][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.30-12.47.52:075][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.30-12.47.52:075][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.06.30-12.47.52:077][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.30-12.47.52:077][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.30-12.47.52:077][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.30-12.47.52:077][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.30-12.47.52:077][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.30-12.47.52:311][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.06.30-12.47.52:311][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.30-12.47.52:311][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.30-12.47.52:311][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.30-12.47.52:312][  0]LogZenServiceInstance: InTree version at 'Y:/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.30-12.47.52:312][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.30-12.47.52:312][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.06.30-12.47.52:516][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.30-12.47.52:516][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.204 seconds
[2025.06.30-12.47.52:518][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.30-12.47.52:523][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.30-12.47.52:524][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=1130.32MBs, RandomWriteSpeed=223.13MBs. Assigned SpeedClass 'Local'
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.30-12.47.52:525][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.30-12.47.52:525][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../UE Project/ToonTank/Intermediate/Shaders/WorkingDirectory/28120/).
[2025.06.30-12.47.52:525][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/8720ED5F4245D550A37302A57EFE9F56/'.
[2025.06.30-12.47.52:533][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.06.30-12.47.52:541][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.06.30-12.47.52:541][  0]LogShaderCompilers: Display: Using XGE Controller for shader compilation
[2025.06.30-12.47.52:541][  0]LogShaderCompilers: Display: Using 8 local workers for shader compilation
[2025.06.30-12.47.52:543][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../UE Project/ToonTank/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.30-12.47.52:543][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.30-12.47.52:805][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.30-12.47.54:905][  0]LogSlate: Using FreeType 2.10.0
[2025.06.30-12.47.54:905][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.30-12.47.54:907][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.30-12.47.54:907][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.30-12.47.54:907][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.30-12.47.54:907][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.30-12.47.54:927][  0]LogAssetRegistry: FAssetRegistry took 0.0020 seconds to start up
[2025.06.30-12.47.54:929][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.30-12.47.55:024][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.30-12.47.55:209][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.30-12.47.55:209][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.30-12.47.55:252][  0]LogDeviceProfileManager: Active device profile: [000001E06B55B400][000001E068632800 66] WindowsEditor
[2025.06.30-12.47.55:252][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.30-12.47.55:255][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.30-12.47.55:257][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.30-12.47.55:257][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.30-12.47.55:257][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.30-12.47.55:267][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.30-12.47.55:268][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR'
[2025.06.30-12.47.55:269][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR" -nocompile -nocompileuat ]
[2025.06.30-12.47.55:297][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:298][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.06.30-12.47.55:298][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:298][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:299][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.06.30-12.47.55:299][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:299][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:300][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.06.30-12.47.55:301][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.06.30-12.47.55:302][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.06.30-12.47.55:303][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:303][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:304][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:304][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.06.30-12.47.55:304][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.06.30-12.47.55:306][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.06.30-12.47.55:306][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:336][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.06.30-12.47.55:336][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:353][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.06.30-12.47.55:353][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:367][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.06.30-12.47.55:367][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.30-12.47.55:561][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.30-12.47.55:561][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.30-12.47.55:561][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.30-12.47.55:561][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.30-12.47.55:561][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.30-12.47.55:974][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.06.30-12.47.56:015][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.30-12.47.56:015][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.30-12.47.56:015][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.06.30-12.47.56:015][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.06.30-12.47.56:033][  0]LogSlate: Border
[2025.06.30-12.47.56:033][  0]LogSlate: BreadcrumbButton
[2025.06.30-12.47.56:033][  0]LogSlate: Brushes.Title
[2025.06.30-12.47.56:033][  0]LogSlate: ColorPicker.ColorThemes
[2025.06.30-12.47.56:034][  0]LogSlate: Default
[2025.06.30-12.47.56:034][  0]LogSlate: Icons.Save
[2025.06.30-12.47.56:034][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.30-12.47.56:034][  0]LogSlate: ListView
[2025.06.30-12.47.56:034][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.30-12.47.56:034][  0]LogSlate: SoftwareCursor_Grab
[2025.06.30-12.47.56:034][  0]LogSlate: TableView.DarkRow
[2025.06.30-12.47.56:034][  0]LogSlate: TableView.Row
[2025.06.30-12.47.56:034][  0]LogSlate: TreeView
[2025.06.30-12.47.56:082][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.30-12.47.56:084][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.525 ms
[2025.06.30-12.47.56:099][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.30-12.47.56:099][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.30-12.47.56:099][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.30-12.47.56:123][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.30-12.47.56:141][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.30-12.47.56:331][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.30-12.47.56:334][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.30-12.47.56:335][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.30-12.47.56:335][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:49974'.
[2025.06.30-12.47.56:338][  0]LogUdpMessaging: Display: Added local interface '192.168.0.177' to multicast group '230.0.0.1:6666'
[2025.06.30-12.47.56:341][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.06.30-12.47.56:459][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.30-12.47.56:459][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.30-12.47.56:477][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.30-12.47.56:604][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: F5917408A60D42218000000000008200 | Instance: A15AFAF94FE4F516BA2E56BE9BD17CA2 (ADMINISTRATOR-28120).
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   NPU:       no
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: Interface availability:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   GPU: yes
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   RDG: yes
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT:   NPU: no
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.30-12.47.56:645][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.30-12.47.56:684][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.06.30-12.47.56:684][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.06.30-12.47.56:689][  0]LogTimingProfiler: Initialize
[2025.06.30-12.47.56:689][  0]LogTimingProfiler: OnSessionChanged
[2025.06.30-12.47.56:689][  0]LoadingProfiler: Initialize
[2025.06.30-12.47.56:690][  0]LoadingProfiler: OnSessionChanged
[2025.06.30-12.47.56:690][  0]LogNetworkingProfiler: Initialize
[2025.06.30-12.47.56:690][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.30-12.47.56:690][  0]LogMemoryProfiler: Initialize
[2025.06.30-12.47.56:690][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.30-12.47.56:861][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.30-12.47.56:866][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.30-12.47.56:866][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.30-12.47.56:923][  0]SourceControl: Revision control is disabled
[2025.06.30-12.47.56:926][  0]SourceControl: Revision control is disabled
[2025.06.30-12.47.56:941][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.30-12.47.56:941][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.30-12.47.56:941][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.30-12.47.56:941][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.30-12.47.57:039][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.30-12.47.57:045][  0]LogCollectionManager: Loaded 0 collections in 0.001250 seconds
[2025.06.30-12.47.57:047][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Saved/Collections/' took 0.00s
[2025.06.30-12.47.57:049][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Developers/maxwe/Collections/' took 0.00s
[2025.06.30-12.47.57:051][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Collections/' took 0.00s
[2025.06.30-12.47.57:100][  0]LogTurnkeySupport: Turnkey Device: Win64@Administrator: (Name=Administrator, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.06.30-12.47.57:103][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.30-12.47.57:103][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.30-12.47.57:103][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.30-12.47.57:103][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.30-12.47.57:122][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.30-12.47.57:122][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.30-12.47.57:122][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.30-12.47.57:122][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.30-12.47.57:137][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-06-30T12:47:57.137Z using C
[2025.06.30-12.47.57:137][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=ToonTank, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.30-12.47.57:137][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.30-12.47.57:138][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.30-12.47.57:143][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.30-12.47.57:143][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.30-12.47.57:143][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.30-12.47.57:143][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000064
[2025.06.30-12.47.57:193][  0]LogUObjectArray: 42367 objects as part of root set at end of initial load.
[2025.06.30-12.47.57:193][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.30-12.47.57:301][  0]LogEngine: Initializing Engine...
[2025.06.30-12.47.57:445][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.06.30-12.47.57:446][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.30-12.47.57:681][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.30-12.47.57:693][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.30-12.47.57:701][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.30-12.47.57:710][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.06.30-12.47.57:710][  0]LogInit: Texture streaming: Enabled
[2025.06.30-12.47.57:715][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.06.30-12.47.57:719][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.30-12.47.57:721][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.30-12.47.57:721][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.30-12.47.57:722][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.30-12.47.57:722][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.30-12.47.57:722][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.30-12.47.57:722][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.30-12.47.57:722][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.30-12.47.57:722][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.30-12.47.57:722][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.30-12.47.57:727][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.30-12.47.57:986][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.30-12.47.57:986][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.30-12.47.57:988][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.30-12.47.57:988][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.30-12.47.57:988][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.30-12.47.57:988][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.30-12.47.57:990][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.30-12.47.57:990][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.30-12.47.57:990][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.30-12.47.57:990][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.30-12.47.57:990][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.30-12.47.57:995][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.30-12.47.57:999][  0]LogInit: Undo buffer set to 256 MB
[2025.06.30-12.47.57:999][  0]LogInit: Transaction tracking system initialized
[2025.06.30-12.47.58:007][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.30-12.47.58:034][  0]LocalizationService: Localization service is disabled
[2025.06.30-12.47.58:150][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/' took 0.00s
[2025.06.30-12.47.58:153][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.06.30-12.47.58:192][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.06.30-12.47.58:193][  0]LogPython: Using Python 3.11.8
[2025.06.30-12.47.58:221][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.06.30-12.47.58:571][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.30-12.47.58:587][  0]LogEditorDataStorage: Initializing
[2025.06.30-12.47.58:590][  0]LogEditorDataStorage: Initialized
[2025.06.30-12.47.58:594][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.30-12.47.58:643][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.30-12.47.58:694][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.30-12.47.58:698][  0]SourceControl: Revision control is disabled
[2025.06.30-12.47.58:698][  0]LogUnrealEdMisc: Loading editor; pre map load, took 8.808
[2025.06.30-12.47.58:699][  0]Cmd: MAP LOAD FILE="../../../../UE Project/ToonTank/Content/Maps/Main.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.30-12.47.58:702][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.30-12.47.58:703][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-12.47.58:723][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.30-12.47.58:725][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.06.30-12.47.58:839][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Main'.
[2025.06.30-12.47.58:839][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.30-12.47.58:858][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.30-12.47.58:878][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.60ms
[2025.06.30-12.47.58:878][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.30-12.47.58:879][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.061ms to complete.
[2025.06.30-12.47.58:887][  0]LogUnrealEdMisc: Total Editor Startup Time, took 8.996
[2025.06.30-12.47.58:997][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.06.30-12.47.59:151][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.47.59:238][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.47.59:323][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.47.59:407][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.47.59:461][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:461][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.30-12.47.59:462][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:463][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.30-12.47.59:463][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:464][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:465][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:465][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:467][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:467][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.30-12.47.59:468][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:469][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:470][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:470][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.30-12.47.59:471][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:471][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:472][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:472][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:473][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.30-12.47.59:473][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.30-12.47.59:577][  0]LogAssetRegistry: Display: Asset registry cache written as 66.6 MiB to ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin
[2025.06.30-12.47.59:649][  0]LogSlate: Took 0.000555 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.30-12.47.59:850][  0]LogSlate: Took 0.000205 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.30-12.47.59:897][  0]LogSlate: Took 0.000848 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.30-12.48.00:320][  0]LogStall: Startup...
[2025.06.30-12.48.00:323][  0]LogStall: Startup complete.
[2025.06.30-12.48.00:342][  0]LogLoad: (Engine Initialization) Total time: 10.45 seconds
[2025.06.30-12.48.00:679][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.30-12.48.00:679][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.30-12.48.00:816][  0]LogSlate: Took 0.000115 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.30-12.48.00:869][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-12.48.00:871][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.30-12.48.00:901][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.30-12.48.00:902][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 31.299 ms
[2025.06.30-12.48.01:171][  1]LogAssetRegistry: AssetRegistryGather time 0.1655s: AssetDataDiscovery 0.0205s, AssetDataGather 0.0618s, StoreResults 0.0831s. Wall time 6.2450s.
	NumCachedDirectories 0. NumUncachedDirectories 1470. NumCachedFiles 7410. NumUncachedFiles 2.
	BackgroundTickInterruptions 0.
[2025.06.30-12.48.01:194][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.06.30-12.48.01:205][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000001 seconds (updated 0 objects)
[2025.06.30-12.48.01:206][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.06.30-12.48.01:312][  2]LogSourceControl: Uncontrolled asset discovery finished in 0.105951 seconds (Found 7388 uncontrolled assets)
[2025.06.30-12.48.01:865][ 52]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 4.714911
[2025.06.30-12.48.01:867][ 52]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.30-12.48.01:868][ 52]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4.722401
[2025.06.30-12.48.02:141][ 75]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.30-12.48.02:396][102]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 5.243808
[2025.06.30-12.48.02:398][102]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.30-12.48.02:398][102]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5.243808, Update Interval: 335.082245
[2025.06.30-12.49.52:525][792]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.30-12.53.22:564][713]LogSlate: Took 0.000524 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.30-12.53.22:572][714]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.30-12.53.22:679][717]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.30-12.53.28:708][224]Running Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.30-12.53.28:814][224]Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.06.30-12.53.28:814][224]Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.30-12.53.29:318][224]Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log_GPF.txt
[2025.06.30-12.53.29:620][224]
[2025.06.30-12.53.29:620][224]Generating VisualStudioCode project files:
[2025.06.30-12.53.29:620][224]Discovering modules, targets and source code for project...
[2025.06.30-12.53.29:620][224]Adding projects for all targets...
[2025.06.30-12.53.29:923][224]Available x64 toolchains (1):
[2025.06.30-12.53.29:923][224] * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.06.30-12.53.29:923][224]    (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.06.30-12.53.29:923][224]Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.06.30-12.53.30:023][224]Adding projects for all targets took 0.37s
[2025.06.30-12.53.30:023][224]Adding projects for all game projects took 0.00s
[2025.06.30-12.53.30:325][224]Adding projects for all modules took 0.31s
[2025.06.30-12.53.30:425][224]Compiling Rules Assemblies
[2025.06.30-12.53.30:425][224]Compiling Rules Assemblies 100%
[2025.06.30-12.53.30:425][224]Creating Build Targets
[2025.06.30-12.53.32:641][224]Creating Build Targets 100%
[2025.06.30-12.53.32:642][224]Creating Build Targets took 2.17s
[2025.06.30-12.53.32:642][224]Binding IntelliSense data...
[2025.06.30-12.53.37:985][224]Binding IntelliSense data... 100%
[2025.06.30-12.53.37:986][224]Binding IntelliSense data took 5.32s
[2025.06.30-12.53.37:986][224]Writing project files...
[2025.06.30-12.53.38:589][224]Writing project files... 100%
[2025.06.30-12.53.38:589][224]Writing project files took 0.69s
[2025.06.30-12.53.38:690][224]
[2025.06.30-12.53.38:690][224]Generating QueryTargets data for editor...
[2025.06.30-12.53.38:690][224]Compiling Rules Assemblies
[2025.06.30-12.53.38:690][224]Compiling Rules Assemblies 100%
[2025.06.30-12.53.38:690][224]Writing Query Target Info
[2025.06.30-12.53.38:690][224]Writing Query Target Info 100%
[2025.06.30-12.53.38:690][224]Generating QueryTargets data for editor took 0.03s
[2025.06.30-12.53.38:690][224]
[2025.06.30-12.53.38:690][224]Result: Succeeded
[2025.06.30-12.53.38:690][224]Total execution time: 9.75 seconds
[2025.06.30-12.53.38:919][225]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 341.775452
[2025.06.30-12.53.39:223][235]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-12.53.39:223][235]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 342.069061, Update Interval: 324.862823
[2025.06.30-12.55.01:480][ 51]LogSlate: Took 0.000192 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.30-12.55.31:473][135]LogStreaming: Display: FlushAsyncLoading(330): 1 QueuedPackages, 0 AsyncPackages
[2025.06.30-12.55.31:491][135]Candidate modules for hot reload:
[2025.06.30-12.55.31:491][135]  ToonTank
[2025.06.30-12.55.31:498][135]Launching UnrealBuildTool... [Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -ModuleWithSuffix=ToonTank,9439 ToonTankEditor Win64 Development -Project="Y:/UE Project/ToonTank/ToonTank.uproject" "Y:/UE Project/ToonTank/ToonTank.uproject"  -IgnoreJunk]
[2025.06.30-12.55.40:987][135]CompilerResultsLog: Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -ModuleWithSuffix=ToonTank,9439 ToonTankEditor Win64 Development -Project="Y:/UE Project/ToonTank/ToonTank.uproject" "Y:/UE Project/ToonTank/ToonTank.uproject"  -IgnoreJunk
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Invalidating makefile for ToonTankEditor (source file added)
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Available x64 toolchains (1):
[2025.06.30-12.55.40:988][135]CompilerResultsLog:  * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.06.30-12.55.40:988][135]CompilerResultsLog:     (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Parsing headers for ToonTankEditor
[2025.06.30-12.55.40:988][135]CompilerResultsLog:   Running Internal UnrealHeaderTool "Y:\UE Project\ToonTank\ToonTank.uproject" "Y:\UE Project\ToonTank\Intermediate\Build\Win64\ToonTankEditor\Development\ToonTankEditor.uhtmanifest" -WarningsAsErrors -installed
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Total of 3 written
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Reflection code generated for ToonTankEditor in 2.3218753 seconds
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Building ToonTankEditor...
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Using Visual Studio 2022 14.44.35211 toolchain (C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207) and Windows 10.0.22621.0 SDK (C:\Program Files (x86)\Windows Kits\10).
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Warning: Visual Studio 2022 compiler is not a preferred version
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade]
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade] Using backward-compatible include order. The latest version of UE has changed the order of includes, which may require code changes. The current setting is:
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade]     IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_3
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade] Suppress this message by setting 'IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_6;' in ToonTankEditor.Target.cs.
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade] Alternatively you can set this to 'EngineIncludeOrderVersion.Latest' to always use the latest include order. This will potentially cause compile errors when integrating new versions of the engine.
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [Upgrade]
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Determining max actions to execute in parallel (8 physical cores, 16 logical cores)
[2025.06.30-12.55.40:988][135]CompilerResultsLog:   Executing up to 8 processes, one per physical core
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Using Unreal Build Accelerator local executor to run 5 action(s)
[2025.06.30-12.55.40:988][135]CompilerResultsLog:   Storage capacity 40Gb
[2025.06.30-12.55.40:988][135]CompilerResultsLog: ---- Starting trace: 250630_205537 ----
[2025.06.30-12.55.40:988][135]CompilerResultsLog: UbaSessionServer - Disable remote execution (remote sessions will finish current processes)
[2025.06.30-12.55.40:988][135]CompilerResultsLog: ------ Building 5 action(s) started ------
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [1/5] Compile [x64] ToonTanksGameMode.cpp
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [2/5] Compile [x64] Module.ToonTank.gen.cpp
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [3/5] Link [x64] UnrealEditor-ToonTank-9439.lib
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [4/5] Link [x64] UnrealEditor-ToonTank-9439.dll
[2025.06.30-12.55.40:988][135]CompilerResultsLog: [5/5] WriteMetadata ToonTankEditor.target (UBA disabled)
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Trace written to file C:/Users/<USER>/AppData/Local/UnrealBuildTool/Log.uba with size 3.8kb
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Total time in Unreal Build Accelerator local executor: 3.84 seconds
[2025.06.30-12.55.40:988][135]CompilerResultsLog: 
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Result: Succeeded
[2025.06.30-12.55.40:988][135]CompilerResultsLog: Total execution time: 9.27 seconds
[2025.06.30-12.55.40:988][135]LogMainFrame: MainFrame: Module compiling took 9.515 seconds
[2025.06.30-12.55.41:066][135]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.06.30-12.55.41:095][135]LogClass: UPackage /Script/ToonTank Reload.
[2025.06.30-12.55.41:098][135]LogClass: Could not find existing class ToonTanksGameMode in package /Script/ToonTank for reload, assuming new or modified class
[2025.06.30-12.55.41:185][135]LogUObjectHash: Compacting FUObjectHashTables data took   1.38ms
[2025.06.30-12.55.41:216][135]Display: HotReload took  9.7s.
[2025.06.30-12.55.41:216][135]Display: Reload/Re-instancing Complete: 1 package changed, 1 class new, 5 classes unchanged, 2 functions remapped
[2025.06.30-12.55.41:216][135]Warning: HotReload operation took  9.7s.
[2025.06.30-12.55.41:276][135]LogSlate: Window 'Add C++ Class' being destroyed
[2025.06.30-12.55.41:290][135]LogHotReload: New module detected: UnrealEditor-ToonTank-9439.dll
[2025.06.30-12.55.41:326][135]LogHotReload: Starting Hot-Reload from IDE
[2025.06.30-12.55.41:547][135]LogUObjectHash: Compacting FUObjectHashTables data took   0.32ms
[2025.06.30-12.55.41:580][135]LogUObjectHash: Compacting FUObjectHashTables data took   0.31ms
[2025.06.30-12.55.41:608][135]Display: HotReload took  0.3s.
[2025.06.30-12.55.41:608][135]Display: Reload/Re-instancing Complete: No object changes detected
[2025.06.30-12.56.04:286][289]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.30-12.56.13:004][111]LogContentBrowser: Deferred new asset folder creation: NewFolder
[2025.06.30-12.56.13:013][111]LogContentBrowser: Creating deferred item: NewFolder
[2025.06.30-12.56.13:027][112]LogContentBrowser: Renaming the item being created (Deferred Item: NewFolder).
[2025.06.30-12.56.16:362][453]LogContentBrowser: Attempting asset rename: NewFolder -> GameMode
[2025.06.30-12.56.16:362][453]LogContentBrowser: End creating deferred item NewFolder
[2025.06.30-12.56.29:655][942]LogSlate: Window 'Pick Parent Class' being destroyed
[2025.06.30-12.56.29:667][942]LogContentBrowser: Deferred new asset file creation: NewBlueprint
[2025.06.30-12.56.29:678][942]LogContentBrowser: Creating deferred item: NewBlueprint
[2025.06.30-12.56.29:706][944]LogContentBrowser: Renaming the item being created (Deferred Item: NewBlueprint).
[2025.06.30-12.56.37:729][646]LogContentBrowser: Attempting asset rename: NewBlueprint -> BP_ToonTanksGameMode
[2025.06.30-12.56.37:739][646]LogContentBrowser: End creating deferred item NewBlueprint
[2025.06.30-12.56.39:176][765]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode
[2025.06.30-12.56.39:176][765]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.30-12.56.39:272][765]LogStreaming: Display: FlushAsyncLoading(332): 1 QueuedPackages, 0 AsyncPackages
[2025.06.30-12.56.49:806][330]LogSlate: Window 'BP_ToonTanksGameMode' being destroyed
[2025.06.30-12.56.55:186][778]LogSlate: External Image Picker: DecompressImage failed
[2025.06.30-12.56.55:433][779]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.30-12.58.02:371][298]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.06.30-12.58.02:966][355]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.30-12.58.03:030][355]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode] ([2] browsable assets)...
[2025.06.30-12.58.03:030][355]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/GameMode/BP_ToonTanksGameMode]
[2025.06.30-12.58.03:030][355]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/GameMode/BP_ToonTanksGameMode" FILE="../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset" SILENT=true
[2025.06.30-12.58.03:033][355]LogWorldPartition: Can't find class descriptor '/Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode_C' for saving '/Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode_C'
[2025.06.30-12.58.03:033][355]LogWorldPartition: Can't find class descriptor '/Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode_C' for saving '/Game/Blueprints/GameMode/BP_ToonTanksGameMode.BP_ToonTanksGameMode_C'
[2025.06.30-12.58.03:034][355]LogSavePackage: Moving output files for package: /Game/Blueprints/GameMode/BP_ToonTanksGameMode
[2025.06.30-12.58.03:034][355]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_ToonTanksGameMode429A07784CDDD05078D4AF80C7FADF0B.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/GameMode/BP_ToonTanksGameMode.uasset'
[2025.06.30-12.58.03:046][355]LogFileHelpers: InternalPromptForCheckoutAndSave took 80.228 ms
[2025.06.30-12.58.03:094][355]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.30-12.58.03:094][355]LogContentValidation: Enabled validators:
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.30-12.58.03:095][355]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.30-12.58.03:095][355]AssetCheck: /Game/Blueprints/GameMode/BP_ToonTanksGameMode Validating asset
[2025.06.30-12.58.03:113][355]LogSourceControl: Display: Uncontrolled Changelist persistency file saved ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.30-12.58.13:415][421]LogStreaming: Display: FlushAsyncLoading(529): 1 QueuedPackages, 0 AsyncPackages
[2025.06.30-12.58.43:769][552]Cmd: DELETE
[2025.06.30-12.58.43:769][552]Cmd: ACTOR DELETE
[2025.06.30-12.58.43:776][552]LogEditorActor: Deleted Actor: BP_PawnTank_C
[2025.06.30-12.58.43:819][552]LogUObjectHash: Compacting FUObjectHashTables data took   1.37ms
[2025.06.30-12.58.43:823][552]LogEditorActor: Deleted 1 Actors (0.050 secs)
[2025.06.30-12.59.07:396][ 22]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 670.252075
[2025.06.30-12.59.07:647][ 48]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-12.59.07:647][ 48]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 670.493286, Update Interval: 351.558594
[2025.06.30-12.59.23:923][801]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.30-12.59.23:934][801]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.30-12.59.23:939][801]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.30-12.59.23:939][801]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.30-12.59.23:939][801]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.30-12.59.23:945][801]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005930s)
[2025.06.30-12.59.23:945][801]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.005991s)
[2025.06.30-12.59.23:977][801]LogUObjectHash: Compacting FUObjectHashTables data took   1.50ms
[2025.06.30-12.59.23:982][801]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.30-12.59.23:985][801]LogPlayLevel: PIE: World Init took: (0.002590s)
[2025.06.30-12.59.23:986][801]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.30-12.59.23:986][801]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.30-12.59.23:986][801]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.30-12.59.23:986][801]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.30-12.59.23:986][801]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.30-12.59.23:986][801]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.30-12.59.23:989][801]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.30-12.59.24:083][801]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.30-12.59.24:083][801]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.30-12.59.24:083][801]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.30-12.59.24:083][801]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.30-12.59.24:084][801]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.30-12.59.24:084][801]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.30-12.59.24:087][801]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.30-12.59.24:087][801]LogInit: FAudioDevice initialized with ID 2.
[2025.06.30-12.59.24:087][801]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.30-12.59.24:087][801]LogAudio: Display: Audio Device (ID: 2) registered with world 'Main'.
[2025.06.30-12.59.24:087][801]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.30-12.59.24:091][801]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.06.30-12.59.24:093][801]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.30-20.59.24
[2025.06.30-12.59.24:094][801]LogWorld: Bringing up level for play took: 0.001926
[2025.06.30-12.59.24:096][801]LogOnline: OSS: Created online subsystem instance for: :Context_6
[2025.06.30-12.59.24:103][801]PIE: Server logged in
[2025.06.30-12.59.24:105][801]PIE: Play in editor total start time 0.175 seconds.
[2025.06.30-12.59.31:985][620]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.59.31:985][620]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.30-12.59.31:985][620]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.30-12.59.31:986][620]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-12.59.31:988][620]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.30-12.59.31:996][620]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-12.59.32:027][620]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-12.59.32:027][620]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.30-12.59.32:027][620]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.06.30-12.59.32:029][620]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.06.30-12.59.32:038][620]LogUObjectHash: Compacting FUObjectHashTables data took   1.37ms
[2025.06.30-12.59.32:144][621]LogPlayLevel: Display: Destroying online subsystem :Context_6
[2025.06.30-12.59.40:854][398]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank
[2025.06.30-12.59.40:854][398]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.30-12.59.41:672][401]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.30-13.00.35:066][524]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.06.30-13.00.35:068][524]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Maps/Main" FILE="../../../../UE Project/ToonTank/Saved/Autosaves/Game/Maps/Main_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.06.30-13.00.35:084][524]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Maps/Main_Auto1
[2025.06.30-13.00.35:084][524]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/Main_Auto1D811A5534A0FF3FC1CF4F3B152D5E8FD.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Maps/Main_Auto1.umap'
[2025.06.30-13.00.35:085][524]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Maps/Main' took 0.073
[2025.06.30-13.00.35:085][524]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.073
[2025.06.30-13.00.35:086][524]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/Pawn/BP_PawnTank] ([2] browsable assets)...
[2025.06.30-13.00.35:218][524]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank]
[2025.06.30-13.00.35:220][524]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.30-13.00.35:305][524]OBJ SavePackage:     Rendered thumbnail for [BlueprintGeneratedClass /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank_C]
[2025.06.30-13.00.35:305][524]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Pawn/BP_PawnTank]
[2025.06.30-13.00.35:305][524]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Pawn/BP_PawnTank" FILE="../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Pawn/BP_PawnTank_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.06.30-13.00.35:311][524]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/Pawn/BP_PawnTank_Auto1
[2025.06.30-13.00.35:311][524]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_PawnTank_Auto12AEA8A9248AA5F6E876F5D9CE8144C8C.tmp' to '../../../../UE Project/ToonTank/Saved/Autosaves/Game/Blueprints/Pawn/BP_PawnTank_Auto1.uasset'
[2025.06.30-13.00.35:311][524]LogFileHelpers: Auto-saving content packages took 0.225
[2025.06.30-13.04.51:950][ 28]LogUObjectHash: Compacting FUObjectHashTables data took   1.60ms
[2025.06.30-13.04.55:269][357]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.30-13.04.55:330][357]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/Pawn/BP_PawnTank] ([2] browsable assets)...
[2025.06.30-13.04.55:390][357]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank]
[2025.06.30-13.04.55:390][357]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Pawn/BP_PawnTank]
[2025.06.30-13.04.55:390][357]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Pawn/BP_PawnTank" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTank.uasset" SILENT=true
[2025.06.30-13.04.55:395][357]LogSavePackage: Moving output files for package: /Game/Blueprints/Pawn/BP_PawnTank
[2025.06.30-13.04.55:395][357]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_PawnTank02AC312348022A98EBC8E5AF2BDC0039.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTank.uasset'
[2025.06.30-13.04.55:403][357]LogFileHelpers: InternalPromptForCheckoutAndSave took 133.330 ms (total: 213.558 ms)
[2025.06.30-13.04.55:462][357]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.30-13.04.55:462][357]LogContentValidation: Enabled validators:
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.30-13.04.55:462][357]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.30-13.04.55:462][357]AssetCheck: /Game/Blueprints/Pawn/BP_PawnTank Validating asset
[2025.06.30-13.05.03:768][562]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1026.624146
[2025.06.30-13.05.04:768][565]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.05.04:768][565]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1027.290894, Update Interval: 307.716309
[2025.06.30-13.05.12:856][928]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.30-13.05.12:866][928]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.30-13.05.12:867][928]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.30-13.05.12:873][928]LogPlayLevel: PIE: StaticDuplicateObject took: (0.005997s)
[2025.06.30-13.05.12:873][928]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006044s)
[2025.06.30-13.05.12:903][928]LogUObjectHash: Compacting FUObjectHashTables data took   1.45ms
[2025.06.30-13.05.12:905][928]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.30-13.05.12:908][928]LogPlayLevel: PIE: World Init took: (0.002490s)
[2025.06.30-13.05.12:909][928]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.30-13.05.12:909][928]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.30-13.05.12:909][928]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.30-13.05.12:909][928]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.30-13.05.12:909][928]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.30-13.05.12:909][928]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.30-13.05.12:911][928]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.30-13.05.13:000][928]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.30-13.05.13:000][928]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.30-13.05.13:000][928]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.30-13.05.13:000][928]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.30-13.05.13:000][928]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.30-13.05.13:001][928]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.30-13.05.13:003][928]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.30-13.05.13:003][928]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.30-13.05.13:003][928]LogInit: FAudioDevice initialized with ID 3.
[2025.06.30-13.05.13:003][928]LogAudio: Display: Audio Device (ID: 3) registered with world 'Main'.
[2025.06.30-13.05.13:003][928]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.30-13.05.13:007][928]LogLoad: Game class is 'BP_ToonTanksGameMode_C'
[2025.06.30-13.05.13:009][928]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.30-21.05.13
[2025.06.30-13.05.13:009][928]LogWorld: Bringing up level for play took: 0.001820
[2025.06.30-13.05.13:012][928]LogOnline: OSS: Created online subsystem instance for: :Context_10
[2025.06.30-13.05.13:015][928]PIE: Server logged in
[2025.06.30-13.05.13:017][928]PIE: Play in editor total start time 0.153 seconds.
[2025.06.30-13.05.18:945][538]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.06.30-13.05.27:957][480]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.06.30-13.05.28:023][487]LogTemp: Warning: BP_PawnTank_C_0 Health: 50.000000
[2025.06.30-13.05.28:071][492]LogTemp: Warning: BP_PawnTank_C_0 Health: 0.000000
[2025.06.30-13.05.30:966][798]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.06.30-13.05.31:950][900]LogTemp: Warning: BP_PawnTank_C_0 Health: -50.000000
[2025.06.30-13.05.36:947][422]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.06.30-13.05.36:975][425]LogTemp: Warning: BP_PawnTank_C_0 Health: -100.000000
[2025.06.30-13.05.36:985][426]LogTemp: Warning: BP_PawnTank_C_0 Health: -150.000000
[2025.06.30-13.05.37:871][518]LogTemp: Warning: BP_PawnTank_C_0 Health: -200.000000
[2025.06.30-13.05.37:951][527]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.06.30-13.05.37:961][528]LogTemp: Warning: BP_PawnTank_C_0 Health: -250.000000
[2025.06.30-13.05.38:861][626]LogTemp: Warning: BP_PawnTank_C_0 Health: -300.000000
[2025.06.30-13.05.38:943][635]LogTemp: Warning: BP_PawnTurret_C_5 Health: 0.000000
[2025.06.30-13.05.38:951][636]LogTemp: Warning: BP_PawnTank_C_0 Health: -350.000000
[2025.06.30-13.05.39:304][670]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-13.05.39:304][670]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.30-13.05.39:304][670]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.30-13.05.39:305][670]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-13.05.39:306][670]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.30-13.05.39:315][670]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.30-13.05.39:345][670]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.30-13.05.39:346][670]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.30-13.05.39:346][670]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=4
[2025.06.30-13.05.39:348][670]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=2
[2025.06.30-13.05.39:357][670]LogUObjectHash: Compacting FUObjectHashTables data took   1.46ms
[2025.06.30-13.05.39:515][671]LogPlayLevel: Display: Destroying online subsystem :Context_10
[2025.06.30-13.10.22:961][ 85]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1345.812866
[2025.06.30-13.10.23:961][ 88]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.10.23:961][ 88]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1346.479858, Update Interval: 350.307922
[2025.06.30-13.16.30:733][188]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1713.585205
[2025.06.30-13.16.31:734][191]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.16.31:734][191]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1714.252563, Update Interval: 316.979889
[2025.06.30-13.22.42:651][347]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2085.499268
[2025.06.30-13.22.43:652][350]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.22.43:652][350]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2086.166504, Update Interval: 323.352142
[2025.06.30-13.28.54:408][462]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2457.255371
[2025.06.30-13.28.55:411][465]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.28.55:411][466]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2457.923828, Update Interval: 334.672089
[2025.06.30-13.35.13:161][598]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2836.008301
[2025.06.30-13.35.14:161][601]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.35.14:161][601]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2836.675293, Update Interval: 356.321289
[2025.06.30-13.41.44:878][773]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Headphones (soundcore Liberty 4 NC Stereo), InstanceID=1
[2025.06.30-13.41.44:878][773]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.30-13.41.44:923][773]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}]
[2025.06.30-13.41.44:923][773]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.30-13.41.44:923][773]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.30-13.41.45:258][774]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=44100, DeviceID={0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}, Name=Headphones (soundcore Liberty 4 NC Stereo)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=40.89
[2025.06.30-13.41.45:258][774]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.30-13.41.45:258][774]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.30-13.41.45:260][774]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.30-13.41.57:932][812]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3240.778564
[2025.06.30-13.41.59:599][817]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.41.59:599][817]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3242.112061, Update Interval: 315.027924
[2025.06.30-13.47.59:331][896]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3602.175293
[2025.06.30-13.48.00:331][899]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.48.00:331][899]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3602.842041, Update Interval: 330.830414
[2025.06.30-13.49.52:525][281]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.30-13.54.11:641][ 58]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3974.486084
[2025.06.30-13.54.12:641][ 61]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-13.54.12:641][ 61]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3975.153809, Update Interval: 354.682465
[2025.06.30-14.00.48:711][249]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4371.558594
[2025.06.30-14.00.49:712][252]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-14.00.49:712][252]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4372.226562, Update Interval: 355.381927
[2025.06.30-14.06.21:436][657]Running Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.30-14.06.21:542][657]Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.06.30-14.06.21:542][657]Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -projectfiles -project="Y:/UE Project/ToonTank/ToonTank.uproject" -game -rocket -progress
[2025.06.30-14.06.21:945][657]Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log_GPF.txt
[2025.06.30-14.06.22:147][657]
[2025.06.30-14.06.22:147][657]Generating VisualStudioCode project files:
[2025.06.30-14.06.22:147][657]Discovering modules, targets and source code for project...
[2025.06.30-14.06.22:147][657]Adding projects for all targets...
[2025.06.30-14.06.22:449][657]Available x64 toolchains (1):
[2025.06.30-14.06.22:449][657] * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.06.30-14.06.22:449][657]    (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.06.30-14.06.22:449][657]Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.06.30-14.06.22:550][657]Adding projects for all targets took 0.41s
[2025.06.30-14.06.22:550][657]Adding projects for all game projects took 0.00s
[2025.06.30-14.06.22:953][657]Adding projects for all modules took 0.42s
[2025.06.30-14.06.23:153][657]Compiling Rules Assemblies
[2025.06.30-14.06.23:153][657]Compiling Rules Assemblies 100%
[2025.06.30-14.06.23:153][657]Creating Build Targets
[2025.06.30-14.06.25:067][657]Creating Build Targets 100%
[2025.06.30-14.06.25:067][657]Creating Build Targets took 1.96s
[2025.06.30-14.06.25:067][657]Binding IntelliSense data...
[2025.06.30-14.06.29:498][657]Binding IntelliSense data... 100%
[2025.06.30-14.06.29:498][657]Binding IntelliSense data took 4.43s
[2025.06.30-14.06.29:498][657]Writing project files...
[2025.06.30-14.06.30:203][657]Writing project files... 100%
[2025.06.30-14.06.30:203][657]Writing project files took 0.67s
[2025.06.30-14.06.30:203][657]
[2025.06.30-14.06.30:203][657]Generating QueryTargets data for editor...
[2025.06.30-14.06.30:203][657]Compiling Rules Assemblies
[2025.06.30-14.06.30:203][657]Compiling Rules Assemblies 100%
[2025.06.30-14.06.30:203][657]Writing Query Target Info
[2025.06.30-14.06.30:203][657]Writing Query Target Info 100%
[2025.06.30-14.06.30:203][657]Generating QueryTargets data for editor took 0.02s
[2025.06.30-14.06.30:203][657]
[2025.06.30-14.06.30:203][657]Result: Succeeded
[2025.06.30-14.06.30:203][657]Total execution time: 8.59 seconds
[2025.06.30-14.07.37:458][889]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4780.311035
[2025.06.30-14.07.38:459][892]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.30-14.07.38:459][892]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4780.978516, Update Interval: 339.399994
