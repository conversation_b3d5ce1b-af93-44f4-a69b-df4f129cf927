{"Version": "1.2", "Data": {"Source": "y:\\ue project\\toontank\\source\\toontank\\toontanksgamemode.cpp", "ProvidedModule": "", "PCH": "y:\\ue project\\toontank\\intermediate\\build\\win64\\x64\\toontankeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["y:\\ue project\\toontank\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\toontank\\definitions.toontank.h", "y:\\ue project\\toontank\\source\\toontank\\toontanksgamemode.h", "y:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "y:\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "y:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "y:\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "y:\\ue project\\toontank\\intermediate\\build\\win64\\unrealeditor\\inc\\toontank\\uht\\toontanksgamemode.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}