/FI "Y:\UE_5.6\Engine\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\Definitions.h" 
/FI "Y:\UE_5.6\Engine\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\PCH.UnrealEd.h" 
/I "Y:\UE_5.6\Engine\Source" 
/I "Y:\UE_5.6\Engine\Source\Editor\UnrealEd\Private" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ImageCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Core\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Core\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TraceLog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AutoRTFM\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AssetRegistry\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AssetRegistry\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AssetTagsEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VNI" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VerseVMBytecode" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CoreUObject\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CoreUObject\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Slate\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\InputCore\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\InputCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Json\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SlateCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\DeveloperSettings\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ApplicationCore\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ApplicationCore\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RHI\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RHI\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ImageWrapper\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BSPUtils\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BSPUtils\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\BSPUtils\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelSequence\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelSequence\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\LevelSequence\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Engine\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Engine\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Engine\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CoreOnline\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\FieldNotification\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Net\Core\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Net\Core\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Net\Common\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\JsonUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Messaging\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MessagingCommon\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RenderCore\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RenderCore\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsET\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Sockets\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\EngineMessages\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\EngineSettings\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\EngineSettings\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SynthBenchmark\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GameplayTags\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GameplayTags\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PacketHandlers\PacketHandler\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PacketHandlers\PacketHandler\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioPlatformConfiguration\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MeshDescription\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\StaticMeshDescription\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SkeletalMeshDescription\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AnimationCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PakFile\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PakFile\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RSA\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PhysicsCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\ChaosCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Voronoi\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GeometryCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NNE\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NNE\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\NNE\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SignalProcessing\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StateStream\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StateStream\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\StateStream\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioExtensions\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioMixerCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioMixer\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioMixer\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TargetPlatform\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureFormat\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\DesktopPlatform\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\DesktopPlatform\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioLink\AudioLinkEngine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AudioLink\AudioLinkCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CookOnTheFly\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Networking\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemandCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemandCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemandCore\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemandCore\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TextureBuildUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Horde\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemand\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemand\VNI" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MovieSceneCapture\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Renderer\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Renderer\Internal" 
/I "Y:\UE_5.6\Engine\Shaders\Public" 
/I "Y:\UE_5.6\Engine\Shaders\Shared" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Tests" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TypedElementRuntime\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\AnimationDataController\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\Kismet\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\Kismet\Public" 
/I "Y:\UE_5.6\Engine\Source\Editor\Kismet\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\Persona\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SkeletonEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\AnimationWidgets\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\ToolWidgets\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\ToolMenus\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimationEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AdvancedPreviewScene\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PropertyEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\EditorConfig\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\EditorFramework\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\EditorSubsystem\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\VNI" 
/I "Y:\UE_5.6\Engine\Source\Programs\UnrealLightmass\Public" 
/I "Y:\UE_5.6\Engine\Source\Editor\UnrealEd\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\UnrealEd\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\CollectionManager\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ContentBrowser\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\AssetTools\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\AssetTools\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AssetDefinition\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Merge\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ContentBrowserData\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Projects\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Projects\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MeshUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MeshMergeUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MeshReductionInterface\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\RawMesh\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MaterialUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\KismetCompiler\Public" 
/I "Y:\UE_5.6\Engine\Source\Editor\KismetCompiler\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GameplayTasks\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GameplayTasks\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ClassViewer\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\Documentation\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\MainFrame\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SandboxFile\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\SourceControl\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\UncontrolledChangelists\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\UnrealEdMessages\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\UnrealEdMessages\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\BlueprintGraph\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\BlueprintGraph\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Online\HTTP\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Online\HTTP\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\FunctionalTesting\Classes" 
/I "Y:\UE_5.6\Engine\Source\Developer\FunctionalTesting\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\AutomationController\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AutomationTest\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Localization\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AudioEditor\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\AudioEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\VNI" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\libSampleRate\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\LevelEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\CommonMenuExtensions\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Settings\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\VREditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ViewportInteraction\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\HeadMountedDisplay\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Landscape\Classes" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Landscape\Public" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Landscape\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\DetailCustomizations\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\GraphEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\StructViewer\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\MaterialEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\NetworkFileSystem\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\UMG\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MovieScene\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TimeManagement\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\UniversalObjectLocator\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Animation\Constraints\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\PropertyPath\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\NavigationSystem\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Core\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Engine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ISMPool\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ISMPool\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\ISMPool\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MeshBuilder\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MeshUtilitiesCommon\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\MaterialShaderQualitySettings\Classes" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ToolMenusEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\StatusBar\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Interchange\Core\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Interchange\Engine\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\DeveloperToolSettings\Classes" 
/I "Y:\UE_5.6\Engine\Source\Developer\DeveloperToolSettings\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SubobjectDataInterface\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SubobjectEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\PhysicsUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\WidgetRegistration\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\VNI" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ActorPickerMode\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SceneDepthPickerMode\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimationEditMode\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimGraph\Public" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimGraph\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AppFramework\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AppFramework\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\AppFramework\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CinematicCamera\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CinematicCamera\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\CinematicCamera\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookMetadata\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookMetadata\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\CookMetadata\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CurveEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CurveEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\CurveEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerWidgets\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerWidgets\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SequencerWidgets\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerCore\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerCore\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SequencerCore\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataLayerEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataLayerEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\DataLayerEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DerivedDataCache\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DerivedDataCache\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\DerivedDataCache\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\DerivedDataCache\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Zen\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Zen\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Zen\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\Zen\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LauncherPlatform\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LauncherPlatform\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Portal\LauncherPlatform\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IESFile\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IESFile\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\IESFile\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWriteQueue\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWriteQueue\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ImageWriteQueue\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonObjectGraph\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonObjectGraph\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Experimental\JsonObjectGraph\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LauncherServices\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LauncherServices\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\LauncherServices\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetDeviceServices\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetDeviceServices\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TargetDeviceServices\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessageLog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessageLog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MessageLog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshUtilitiesCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshUtilitiesCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\SkeletalMeshUtilitiesCommon\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureUtilitiesCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureUtilitiesCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\TextureUtilitiesCommon\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatsViewer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatsViewer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\StatsViewer\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\StatsViewer\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SwarmInterface\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SwarmInterface\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SwarmInterface\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorWidgets\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorWidgets\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\EditorWidgets\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshPaint\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshPaint\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\MeshPaint\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Foliage\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FoliageEdit\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FoliageEdit\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\FoliageEdit\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LocalizationService\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LocalizationService\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\LocalizationService\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AddContentDialog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AddContentDialog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AddContentDialog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameProjectGeneration\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameProjectGeneration\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\GameProjectGeneration\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\GameProjectGeneration\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HardwareTargeting\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HardwareTargeting\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\HardwareTargeting\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HierarchicalLODUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HierarchicalLODUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\HierarchicalLODUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PixelInspectorModule\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PixelInspectorModule\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PixelInspector\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ClothingSystemEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeCommon\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeCommon\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeNv\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeNv\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeNv\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemEditorInterface\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemEditorInterface\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ClothingSystemEditorInterface\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PIEPreviewDeviceProfileSelector\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PIEPreviewDeviceProfileSelector\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PIEPreviewDeviceProfileSelector\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFileUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFileUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\PakFileUtilities\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ScriptDisassembler\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ScriptDisassembler\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\ScriptDisassembler\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UATHelper\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UATHelper\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\UATHelper\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreUtilities\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreUtilities\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\IoStoreUtilities\Public" 
/I "Y:\UE_5.6\Engine\Source\Developer\IoStoreUtilities\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorInteractiveToolsFramework\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorInteractiveToolsFramework\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceAnalysis\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceAnalysis\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TraceAnalysis\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceServices\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceServices\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\TraceServices\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Cbor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Cbor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Cbor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintLibrary\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintLibrary\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\AnimationBlueprintLibrary\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialBaking\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialBaking\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\MaterialBaking\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFlyNetServer\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFlyNetServer\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\CookOnTheFlyNetServer\Internal" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BuildSettings\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BuildSettings\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\BuildSettings\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VirtualizationEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VirtualizationEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\VirtualizationEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocatorEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocatorEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\UniversalObjectLocatorEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WindowsPlatformFeatures\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WindowsPlatformFeatures\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Windows\WindowsPlatformFeatures\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayMediaEncoder\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayMediaEncoder\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\GameplayMediaEncoder\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Navmesh\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Navmesh\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Navmesh\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TurnkeySupport\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TurnkeySupport\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\TurnkeySupport\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PlacementMode\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PlacementMode\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PlacementMode\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SettingsEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SettingsEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\SettingsEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportSnapping\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportSnapping\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ViewportSnapping\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceCodeAccess\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceCodeAccess\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\SourceCodeAccess\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OutputLog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OutputLog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\OutputLog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PortalServices\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PortalServices\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Portal\Services\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsAssetEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsAssetEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PhysicsAssetEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Media\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Media\VNI" 
/I "Y:\UE_5.6\Engine\Source\Runtime\Media\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VirtualTexturingEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VirtualTexturingEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\VirtualTexturingEditor\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\VirtualTexturingEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HotReload\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HotReload\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\HotReload\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\StaticMeshEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WorkspaceMenuStructure\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WorkspaceMenuStructure\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\WorkspaceMenuStructure\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\EditorStyle\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LandscapeEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LandscapeEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\LandscapeEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Blutility\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Blutility\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\Blutility\Classes" 
/I "Y:\UE_5.6\Engine\Source\Editor\Blutility\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateReflector\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateReflector\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\SlateReflector\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PackagesDialog\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PackagesDialog\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\PackagesDialog\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControlWindows\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControlWindows\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\SourceControlWindows\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ProjectTargetPlatformEditor\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ProjectTargetPlatformEditor\VNI" 
/I "Y:\UE_5.6\Engine\Source\Editor\ProjectTargetPlatformEditor\Public" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LiveCoding\UHT" 
/I "Y:\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LiveCoding\VNI" 
/I "Y:\UE_5.6\Engine\Source\Developer\Windows\LiveCoding\Public" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\GuidelinesSupportLibrary\GSL-1144\include" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\AtomicQueue" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\RapidJSON\1.1.0" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\LibTiff\Source\Win64" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\LibTiff\Source" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\OpenGL" 
/I "Y:\UE_5.6\Engine\Source\ThirdParty\FreeImage\FreeImage-3.18.0\Dist" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SymsLib" 
/I "Y:\UE_5.6\Engine\Source\Runtime\SymsLib\syms" 
