/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-ToonTank-0010.dll"
/IGNORE:4221
/NODEFAULTLIB
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/ToonTankEditor/Development/UnrealEd/SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.InclOrderUnreal5_3.h.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Module.ToonTank.gen.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/BasePawn.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/HealthComponent.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Projectile.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Tank.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/ToonTank.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/ToonTanksGameMode.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/ToonTanksPlayerController.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Tower.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/PerModuleInline.gen.cpp.obj"
"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Default.rc2.res"
/OUT:"Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/UnrealEditor-ToonTank-0010.lib"