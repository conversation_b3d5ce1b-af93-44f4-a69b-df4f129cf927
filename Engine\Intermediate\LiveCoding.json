{"LinkerPath": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe", "LinkerEnvironment": {"ProgramFiles(x86)": "C:\\Program Files (x86)", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "COMPUTERNAME": "ADMINISTRATOR", "PROMPT": "$P$G", "CommonProgramFiles": "C:\\Program Files\\Common Files", "__PSLockDownPolicy": "0", "UE_DOTNET_DIR": "Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "LANG": "en_US.UTF-8", "VC_COMPILER_DIR": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "CommonProgramW6432": "C:\\Program Files\\Common Files", "HOMEPATH": "\\Users\\maxwe", "HOMEDRIVE": "C:", "LockFile": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\Y-UE_5.6-Engine-Build-BatchFiles-Build.bat.lock", "PGO_PATH_TRANSLATION": "Y:\\UE_5.6=ROOT;Y:\\UE Project\\ToonTank=PROJ", "USERDOMAIN": "ADMINISTRATOR", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "OS": "Windows_NT", "VSCODE_GIT_IPC_HANDLE": "\\\\.\\pipe\\vscode-git-bc28c4eda4-sock", "EOS_LAUNCHED_BY_EPIC": "0", "NUMBER_OF_PROCESSORS": "16", "UE-ZenSubprocessDataPath": "C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data", "DOTNET_HOST_PATH": "Y:\\UE_5.6\\Engine\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64\\dotnet.exe", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "NVTOOLSEXT_PATH": "C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\", "TERM_PROGRAM_VERSION": "1.101.1", "ALLUSERSPROFILE": "C:\\ProgramData", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "PUBLIC": "C:\\Users\\<USER>", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "CUDA_PATH_V11_8": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8", "SystemDrive": "C:", "ProgramFiles": "C:\\Program Files", "SystemRoot": "C:\\Windows", "VSCODE_GIT_ASKPASS_MAIN": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js", "OneDrive": "E:\\OneDrive\\Rsk Group Limited", "UnrealBuildTool_TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "ProgramData": "C:\\ProgramData", "CUDA_PATH": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8", "PSModulePath": "C:\\Users\\<USER>\\OneDrive\\文件\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "USERNAME": "maxwe", "ProjectFile": "\"Programs\\UnrealBuildTool\\UnrealBuildTool.csproj\"", "CHROME_CRASHPAD_PIPE_NAME": "\\\\.\\pipe\\crashpad_11440_SOHXYZURJKFYWZEV", "DOTNET_ROOT": "Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "PROCESSOR_LEVEL": "23", "UE_DesktopUnrealProcess": "1", "VSCODE_GIT_ASKPASS_NODE": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "GIT_ASKPASS": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_REVISION": "7100", "windir": "C:\\Windows", "MOZ_PLUGIN_PATH": "C:\\Program Files (x86)\\Nuance\\PDF Professional 7\\Bin\\", "TERM_PROGRAM": "vscode", "UE_DOTNET_ARCH": "win-x64", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "DOTNET_ROLL_FORWARD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WaitingForLock": "0", "Path": "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x64;Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64;Y:\\UE_5.6\\Engine\\Binaries\\DotNET\\UnrealBuildTool;Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64;C:\\flutter\\bin\\cache\\dart-sdk\\bin\\;Y:\\UE_5.6\\Engine\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Microsoft SQL Server\\120\\Tools\\Binn\\;C:\\Program Files\\Common Files\\Autodesk Shared\\;C:\\Program Files (x86)\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\Pandoc\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\Program Files\\Android\\Android Studio\\jbr\\bin;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\bin;Y:\\Microsoft Visual Studio\\2022\\Community;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Incredibuild;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\flutter\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.dotnet\\tools;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand", "PROCESSOR_IDENTIFIER": "AMD64 Family 23 Model 113 Stepping 0, AuthenticAMD", "UE_DOTNET_VERSION": "8.0.300", "USERPROFILE": "C:\\Users\\<USER>", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "USERDOMAIN_ROAMINGPROFILE": "ADMINISTRATOR", "DOTNET_MULTILEVEL_LOOKUP": "0", "VC_COMPILER_PATH": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "COLORTERM": "truecolor", "ProgramW6432": "C:\\Program Files", "UBTPath": "\"..\\..\\Engine\\Binaries\\DotNET\\UnrealBuildTool\\UnrealBuildTool.dll\"", "OneDriveCommercial": "E:\\OneDrive\\Rsk Group Limited", "LOGONSERVER": "\\\\ADMINISTRATOR", "VC_TOOLCHAIN_DIR": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64"}, "Modules": [{"Output": "Y:\\UE Project\\ToonTank\\Binaries\\Win64\\UnrealEditor-ToonTank.dll", "Inputs": ["Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\BasePawn.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Module.ToonTank.gen.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Tank.cpp.lc.obj"], "Libraries": ["Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\CoreUObject\\UnrealEditor-CoreUObject.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Core\\UnrealEditor-Core.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Engine\\UnrealEditor-Engine.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\InputCore\\UnrealEditor-InputCore.lib"]}], "Vfs": []}