{"LinkerPath": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\link.exe", "LinkerEnvironment": {"USERDOMAIN": "ADMINISTRATOR", "CUDA_PATH": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8", "EOS_LAUNCHED_BY_EPIC": "0", "CommonProgramFiles": "C:\\Program Files\\Common Files", "USERPROFILE": "C:\\Users\\<USER>", "Path": "C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.22621.0\\x64;Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64;Y:\\UE_5.6\\Engine\\Binaries\\DotNET\\UnrealBuildTool;Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Microsoft SQL Server\\120\\Tools\\Binn\\;C:\\Program Files\\Common Files\\Autodesk Shared\\;C:\\Program Files (x86)\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\;C:\\Program Files\\Pandoc\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\Program Files\\Android\\Android Studio\\jbr\\bin;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\bin;Y:\\Microsoft Visual Studio\\2022\\Community;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Git\\cmd;C:\\Program Files (x86)\\Incredibuild;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\flutter\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\.dotnet\\tools", "ProgramW6432": "C:\\Program Files", "ProjectFile": "\"Programs\\UnrealBuildTool\\UnrealBuildTool.csproj\"", "PGO_PATH_TRANSLATION": "Y:\\UE_5.6=ROOT;Y:\\UE Project\\ToonTank=PROJ", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "SystemRoot": "C:\\Windows", "UE_EditorUIPid": "15048", "VC_COMPILER_DIR": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64", "MOZ_PLUGIN_PATH": "C:\\Program Files (x86)\\Nuance\\PDF Professional 7\\Bin\\", "LOGONSERVER": "\\\\ADMINISTRATOR", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "OneDriveCommercial": "E:\\OneDrive\\Rsk Group Limited", "HOMEPATH": "\\Users\\maxwe", "UE_DOTNET_VERSION": "8.0.300", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC", "LockFile": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\Y-UE_5.6-Engine-Build-BatchFiles-Build.bat.lock", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "UE_DesktopUnrealProcess": "1", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "NUMBER_OF_PROCESSORS": "16", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "OS": "Windows_NT", "DOTNET_ROOT": "Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "UE_DOTNET_DIR": "Y:\\UE_5.6\\Engine\\Build\\BatchFiles\\..\\..\\Binaries\\ThirdParty\\DotNet\\8.0.300\\win-x64", "PUBLIC": "C:\\Users\\<USER>", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramData": "C:\\ProgramData", "UBTPath": "\"..\\..\\Engine\\Binaries\\DotNET\\UnrealBuildTool\\UnrealBuildTool.dll\"", "VC_COMPILER_PATH": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "windir": "C:\\Windows", "HOMEDRIVE": "C:", "PROCESSOR_LEVEL": "23", "LPP_PROCESS_RESTART_ID": "13968", "__PSLockDownPolicy": "0", "USERDOMAIN_ROAMINGPROFILE": "ADMINISTRATOR", "PROCESSOR_ARCHITECTURE": "AMD64", "DOTNET_MULTILEVEL_LOOKUP": "0", "ALLUSERSPROFILE": "C:\\ProgramData", "USERNAME": "maxwe", "UE-ZenSubprocessDataPath": "C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data", "UE_DOTNET_ARCH": "win-x64", "PROMPT": "$P$G", "CommonProgramW6432": "C:\\Program Files\\Common Files", "PROCESSOR_IDENTIFIER": "AMD64 Family 23 Model 113 Stepping 0, AuthenticAMD", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "COMPUTERNAME": "ADMINISTRATOR", "CUDA_PATH_V11_8": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8", "DOTNET_ROLL_FORWARD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PROCESSOR_REVISION": "7100", "WaitingForLock": "0", "SystemDrive": "C:", "NVTOOLSEXT_PATH": "C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "ProgramFiles": "C:\\Program Files", "OneDrive": "E:\\OneDrive\\Rsk Group Limited", "UnrealBuildTool_TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\UnrealBuildTool\\cd94382c", "VC_TOOLCHAIN_DIR": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64"}, "Modules": [{"Output": "Y:\\UE Project\\ToonTank\\Binaries\\Win64\\UnrealEditor-ToonTank.dll", "Inputs": ["Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\BasePawn.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Module.ToonTank.gen.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Projectile.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Tank.cpp.lc.obj", "Y:\\UE Project\\ToonTank\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\ToonTank\\Tower.cpp.lc.obj"], "Libraries": ["Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\CoreUObject\\UnrealEditor-CoreUObject.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Core\\UnrealEditor-Core.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\Engine\\UnrealEditor-Engine.lib", "Y:\\UE_5.6\\Engine\\Intermediate\\Build\\Win64\\x64\\UnrealEditor\\Development\\InputCore\\UnrealEditor-InputCore.lib"]}], "Vfs": []}