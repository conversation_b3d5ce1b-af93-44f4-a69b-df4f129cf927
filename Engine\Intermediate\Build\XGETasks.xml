<BuildSet FormatVersion="1">
  <Environments>
    <Environment Name="Default">
      <Tools>
        <Tool Name="Tool0" AllowRemote="True" AllowIntercept="True" OutputPrefix="@action_0 " Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/BasePawn.cpp.obj.rsp&quot; " Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="BasePawn.cpp.dep.json,BasePawn.cpp.obj,BasePawn.cpp.sarif" AutoRecover="C1060,C1076,C2855,C3435,C3859" />
        <Tool Name="Tool1" AllowRemote="True" AllowIntercept="True" OutputPrefix="@action_1 " Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Module.ToonTank.gen.cpp.obj.rsp&quot; " Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="Module.ToonTank.gen.cpp.dep.json,Module.ToonTank.gen.cpp.obj,Module.ToonTank.gen.cpp.sarif" AutoRecover="C1060,C1076,C2855,C3435,C3859" />
        <Tool Name="Tool2" AllowRemote="True" AllowIntercept="True" OutputPrefix="@action_2 " Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Tank.cpp.obj.rsp&quot; " Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="Tank.cpp.dep.json,Tank.cpp.obj,Tank.cpp.sarif" AutoRecover="C1060,C1076,C2855,C3435,C3859" />
        <Tool Name="Tool3" AllowRemote="True" AllowIntercept="True" OutputPrefix="@action_3 " Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/ToonTanksGameMode.cpp.obj.rsp&quot; " Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="ToonTanksGameMode.cpp.dep.json,ToonTanksGameMode.cpp.obj,ToonTanksGameMode.cpp.sarif" AutoRecover="C1060,C1076,C2855,C3435,C3859" />
        <Tool Name="Tool4" AllowRemote="True" AllowIntercept="True" OutputPrefix="@action_4 " Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/Tower.cpp.obj.rsp&quot; " Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\cl.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="Tower.cpp.dep.json,Tower.cpp.obj,Tower.cpp.sarif" AutoRecover="C1060,C1076,C2855,C3435,C3859" />
        <Tool Name="Tool5" AllowRemote="False" AllowIntercept="False" OutputPrefix="@action_5 UnrealEditor-ToonTank-0012.dll" Params="@&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/UnrealEditor-ToonTank-0012.dll.rsp&quot;" Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="UnrealEditor-ToonTank-0012.dll,UnrealEditor-ToonTank-0012.pdb" AutoRecover="Unexpected PDB error; OK (0)" />
        <Tool Name="Tool6" AllowRemote="False" AllowIntercept="False" OutputPrefix="@action_6 UnrealEditor-ToonTank-0012.lib" Params="/LIB @&quot;Y:/UE Project/ToonTank/Intermediate/Build/Win64/x64/UnrealEditor/Development/ToonTank/UnrealEditor-ToonTank-0012.lib.rsp&quot;" Path="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx64\x64\link.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="UnrealEditor-ToonTank-0012.lib" AutoRecover="Unexpected PDB error; OK (0)" />
        <Tool Name="Tool7" AllowRemote="False" AllowIntercept="False" OutputPrefix="@action_7 ToonTankEditor.target" Params="&quot;Y:\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll&quot; -Session=&quot;{af2e1791-ec4c-4b96-9992-b700870ed19b}&quot; -Mode=WriteMetadata -Input=&quot;Y:\UE Project\ToonTank\Intermediate\Build\Win64\x64\ToonTankEditor\Development\Metadata-HotReload.dat&quot; -Version=2" Path="Y:\UE_5.6\Engine\Binaries\ThirdParty\DotNet\8.0.300\win-x64\dotnet.exe" SkipIfProjectFailed="true" AutoReserveMemory="*.pch" OutputFileMasks="ToonTankEditor.target,UnrealEditor.modules" />
      </Tools>
    </Environment>
  </Environments>
  <Project Name="Default" Env="Default">
    <Task SourceFile="" Caption="BasePawn.cpp" Name="Action0" Tool="Tool0" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" />
    <Task SourceFile="" Caption="Module.ToonTank.gen.cpp" Name="Action1" Tool="Tool1" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" />
    <Task SourceFile="" Caption="Tank.cpp" Name="Action2" Tool="Tool2" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" />
    <Task SourceFile="" Caption="ToonTanksGameMode.cpp" Name="Action3" Tool="Tool3" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" />
    <Task SourceFile="" Caption="Tower.cpp" Name="Action4" Tool="Tool4" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" />
    <Task SourceFile="" Caption="UnrealEditor-ToonTank-0012.dll" Name="Action5" Tool="Tool5" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" DependsOn="Action0;Action1;Action2;Action3;Action4" />
    <Task SourceFile="" Caption="UnrealEditor-ToonTank-0012.lib" Name="Action6" Tool="Tool6" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" DependsOn="Action0;Action1;Action2;Action3;Action4" />
    <Task SourceFile="" Caption="ToonTankEditor.target" Name="Action7" Tool="Tool7" WorkingDir="Y:\UE_5.6\Engine\Source" SkipIfProjectFailed="true" AllowRestartOnLocal="true" DependsOn="Action5;Action6" />
  </Project>
</BuildSet>