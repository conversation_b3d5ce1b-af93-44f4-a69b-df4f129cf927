﻿Log file open, 06/29/25 21:43:42
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=7044)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: ToonTank
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 36314
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.0-43139311+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.5965] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 7 3700X 8-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" "Y:/UE Project/ToonTank/ToonTank.uproject"""
LogCsvProfiler: Display: Metadata set : loginid="736956f4414f060eb3ef6884c4fdf35b"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.320452
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-0A0B5BC4407DC4943908FF969AF4849A
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../UE Project/ToonTank/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogAssetRegistry: Display: PlatformFileJournal is not available on volume 'Y:' of project directory 'Y:/UE Project/ToonTank/', so AssetDiscovery cache will not be read or written. Unavailability reason:
	NTFS Journal is not active for volume 'Y:'. Launch cmd.exe as admin and run command `fsutil usn createJournal Y: m=<SizeInBytes>`. Recommended <SizeInBytes> is 0x40000000 (1GB).
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 66.6 MiB from ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading Unix ini files took 0.07 seconds
LogConfig: Display: Loading TVOS ini files took 0.07 seconds
LogConfig: Display: Loading Windows ini files took 0.07 seconds
LogConfig: Display: Loading VisionOS ini files took 0.07 seconds
LogPluginManager: Found matching target receipt: ../../../../UE Project/ToonTank/Binaries/Win64/ToonTankEditor.target
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogEOSShared: Loaded "Y:/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:29:58
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: 
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.29-13.43.42:888][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.29-13.43.42:888][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.29-13.43.42:889][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.29-13.43.42:891][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1920"
[2025.06.29-13.43.42:891][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="1080"
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.29-13.43.42:892][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.29-13.43.42:893][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.29-13.43.42:893][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.29-13.43.42:893][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.29-13.43.42:893][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.29-13.43.42:896][  0]LogRHI: Using Default RHI: D3D12
[2025.06.29-13.43.42:896][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.29-13.43.42:896][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.29-13.43.42:898][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.29-13.43.42:898][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.29-13.43.43:080][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 2060 (VendorId: 10de, DeviceId: 1f08, SubSysId: 37551462, Revision: 00a1
[2025.06.29-13.43.43:080][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.29-13.43.43:080][  0]LogD3D12RHI:   Adapter has 5954MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 2 output[s], UMA:false
[2025.06.29-13.43.43:080][  0]LogD3D12RHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.29-13.43.43:080][  0]LogD3D12RHI:      Driver Date: 6-1-2024
[2025.06.29-13.43.43:085][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.29-13.43.43:085][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.29-13.43.43:085][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32720MB of shared system memory, 0 output[s], UMA:true
[2025.06.29-13.43.43:085][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.29-13.43.43:085][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.29-13.43.43:085][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.29-13.43.43:085][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.29-13.43.43:085][  0]LogHAL: Display: Platform has ~ 64 GB [68619431936 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.29-13.43.43:085][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.29-13.43.43:085][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.29-13.43.43:085][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.29-13.43.43:085][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.29-13.43.43:085][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.29-13.43.43:086][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.29-13.43.43:086][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.29-13.43.43:086][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.29-13.43.43:086][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.29-13.43.43:086][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.29-13.43.43:086][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.29-13.43.43:086][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.29-13.43.43:086][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Y:/UE Project/ToonTank/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.29-13.43.43:086][  0]LogInit: Computer: ADMINISTRATOR
[2025.06.29-13.43.43:086][  0]LogInit: User: maxwe
[2025.06.29-13.43.43:086][  0]LogInit: CPU Page size=4096, Cores=8
[2025.06.29-13.43.43:086][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.29-13.43.43:086][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.29-13.43.43:086][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.06.29-13.43.43:086][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.29-13.43.43:086][  0]LogMemory: Process Physical Memory: 638.31 MB used, 655.94 MB peak
[2025.06.29-13.43.43:086][  0]LogMemory: Process Virtual Memory: 658.96 MB used, 658.96 MB peak
[2025.06.29-13.43.43:086][  0]LogMemory: Physical Memory: 22298.58 MB used,  43142.01 MB free, 65440.59 MB total
[2025.06.29-13.43.43:086][  0]LogMemory: Virtual Memory: 24178.56 MB used,  50990.03 MB free, 75168.59 MB total
[2025.06.29-13.43.43:086][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.29-13.43.43:088][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.29-13.43.43:090][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.29-13.43.43:090][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.29-13.43.43:090][  0]LogInit: Using OS detected language (en-GB).
[2025.06.29-13.43.43:090][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.29-13.43.43:093][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.29-13.43.43:093][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.06.29-13.43.43:555][  0]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.06.29-13.43.43:564][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.29-13.43.43:566][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.06.29-13.43.43:570][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.29-13.43.43:570][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.29-13.43.43:715][  0]LogRHI: Using Default RHI: D3D12
[2025.06.29-13.43.43:715][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.29-13.43.43:715][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.29-13.43.43:715][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.29-13.43.43:715][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.29-13.43.43:715][  0]LogD3D12RHI: Integrated GPU (iGPU): false
[2025.06.29-13.43.43:715][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.29-13.43.43:715][  0]LogWindows: Attached monitors:
[2025.06.29-13.43.43:716][  0]LogWindows:     resolution: 1920x1080, work area: (1920, 0) -> (3840, 1040), device: '\\.\DISPLAY1'
[2025.06.29-13.43.43:716][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1040), device: '\\.\DISPLAY2' [PRIMARY]
[2025.06.29-13.43.43:716][  0]LogWindows: Found 2 attached monitors.
[2025.06.29-13.43.43:716][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.29-13.43.43:716][  0]LogRHI: RHI Adapter Info:
[2025.06.29-13.43.43:716][  0]LogRHI:             Name: NVIDIA GeForce RTX 2060
[2025.06.29-13.43.43:716][  0]LogRHI:   Driver Version: 555.99 (internal:32.0.15.5599, unified:555.99)
[2025.06.29-13.43.43:716][  0]LogRHI:      Driver Date: 6-1-2024
[2025.06.29-13.43.43:716][  0]LogD3D12RHI:     GPU DeviceId: 0x1f08 (for the marketing name, search the web for "GPU Device Id")
[2025.06.29-13.43.43:716][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.29-13.43.43:804][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.29-13.43.43:804][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.29-13.43.43:865][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.06.29-13.43.43:865][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Raster order views are supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.29-13.43.43:865][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.29-13.43.43:957][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001FB327722C0)
[2025.06.29-13.43.43:957][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001FB32772580)
[2025.06.29-13.43.43:957][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000001FB32772840)
[2025.06.29-13.43.43:957][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.29-13.43.43:957][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.29-13.43.44:114][  0]LogD3D12RHI: NVIDIA Shader Execution Reordering NOT supported!
[2025.06.29-13.43.44:114][  0]LogD3D12RHI: Display: Batched command list execution is disabled for async queues due to known bugs in the current driver.
[2025.06.29-13.43.44:114][  0]LogRHI: Texture pool is 3267 MB (70% of 4667 MB)
[2025.06.29-13.43.44:114][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.29-13.43.44:114][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.29-13.43.44:125][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.29-13.43.44:128][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D12"
[2025.06.29-13.43.44:128][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D12"
[2025.06.29-13.43.44:128][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM6"
[2025.06.29-13.43.44:128][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM6"
[2025.06.29-13.43.44:128][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.29-13.43.44:130][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all'
[2025.06.29-13.43.44:130][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_0.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_0.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -platform=all" ]
[2025.06.29-13.43.44:140][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.06.29-13.43.44:140][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.29-13.43.44:140][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.29-13.43.44:140][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.29-13.43.44:140][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.29-13.43.44:140][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.29-13.43.44:140][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.06.29-13.43.44:140][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.06.29-13.43.44:140][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.29-13.43.44:141][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.29-13.43.44:171][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.29-13.43.44:186][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.29-13.43.44:186][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.29-13.43.44:201][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.29-13.43.44:201][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.29-13.43.44:201][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.29-13.43.44:201][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.29-13.43.44:216][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.29-13.43.44:216][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.29-13.43.44:216][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.29-13.43.44:216][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.29-13.43.44:230][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.29-13.43.44:230][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.29-13.43.44:246][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.29-13.43.44:246][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.29-13.43.44:246][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.29-13.43.44:246][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.29-13.43.44:246][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.29-13.43.44:269][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.29-13.43.44:274][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.29-13.43.44:275][  0]LogRendererCore: Ray tracing is disabled. Reason: disabled through project setting (r.RayTracing=0).
[2025.06.29-13.43.44:278][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.29-13.43.44:278][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.29-13.43.44:278][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.29-13.43.44:278][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../UE Project/ToonTank/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.29-13.43.44:278][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.29-13.43.44:512][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1351 MiB)
[2025.06.29-13.43.44:513][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.29-13.43.44:513][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.29-13.43.44:513][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.29-13.43.44:514][  0]LogZenServiceInstance: InTree version at 'Y:/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.29-13.43.44:514][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.06.29-13.43.44:514][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.06.29-13.43.44:957][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.29-13.43.45:423][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.29-13.43.45:423][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.909 seconds
[2025.06.29-13.43.45:424][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.29-13.43.45:431][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.29-13.43.45:431][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=757.07MBs, RandomWriteSpeed=224.68MBs. Assigned SpeedClass 'Local'
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.29-13.43.45:432][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.29-13.43.45:432][  0]LogShaderCompilers: Guid format shader working directory is 15 characters bigger than the processId version (../../../../UE Project/ToonTank/Intermediate/Shaders/WorkingDirectory/9936/).
[2025.06.29-13.43.45:433][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/8536E4D34A3C282798458CBF9EAEA084/'.
[2025.06.29-13.43.45:441][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.06.29-13.43.45:451][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.06.29-13.43.45:451][  0]LogShaderCompilers: Display: Using XGE Controller for shader compilation
[2025.06.29-13.43.45:451][  0]LogShaderCompilers: Display: Using 8 local workers for shader compilation
[2025.06.29-13.43.45:453][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../UE Project/ToonTank/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.29-13.43.45:453][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.29-13.43.47:032][  0]LogSlate: Using FreeType 2.10.0
[2025.06.29-13.43.47:032][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.29-13.43.47:035][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.29-13.43.47:035][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.29-13.43.47:035][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.29-13.43.47:035][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.29-13.43.47:097][  0]LogAssetRegistry: FAssetRegistry took 0.0021 seconds to start up
[2025.06.29-13.43.47:099][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.29-13.43.47:132][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.29-13.43.47:396][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.29-13.43.47:396][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.29-13.43.47:445][  0]LogDeviceProfileManager: Active device profile: [000001FB65597A80][000001FB62592800 66] WindowsEditor
[2025.06.29-13.43.47:445][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.29-13.43.47:448][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.29-13.43.47:449][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.29-13.43.47:449][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.29-13.43.47:449][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.29-13.43.47:458][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.29-13.43.47:460][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR'
[2025.06.29-13.43.47:460][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""Y:/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="Y:/UE Project/ToonTank/ToonTank.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="Y:/UE Project/ToonTank/Intermediate/TurnkeyReport_1.log" -log="Y:/UE Project/ToonTank/Intermediate/TurnkeyLog_1.log" -project="Y:/UE Project/ToonTank/ToonTank.uproject"  -Device=Win64@ADMINISTRATOR" -nocompile -nocompileuat ]
[2025.06.29-13.43.47:486][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:486][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.06.29-13.43.47:486][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:487][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:488][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.06.29-13.43.47:488][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:488][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:489][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.06.29-13.43.47:490][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.06.29-13.43.47:492][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.06.29-13.43.47:493][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:494][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:494][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:495][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.06.29-13.43.47:495][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.06.29-13.43.47:497][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.06.29-13.43.47:497][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:530][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.06.29-13.43.47:530][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:547][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.06.29-13.43.47:547][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:561][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.06.29-13.43.47:561][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.29-13.43.47:760][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.29-13.43.47:760][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.29-13.43.47:760][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.29-13.43.47:760][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.29-13.43.47:760][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.29-13.43.48:205][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.06.29-13.43.48:245][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.29-13.43.48:245][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.29-13.43.48:245][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.06.29-13.43.48:245][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.06.29-13.43.48:264][  0]LogSlate: Border
[2025.06.29-13.43.48:264][  0]LogSlate: BreadcrumbButton
[2025.06.29-13.43.48:264][  0]LogSlate: Brushes.Title
[2025.06.29-13.43.48:264][  0]LogSlate: ColorPicker.ColorThemes
[2025.06.29-13.43.48:264][  0]LogSlate: Default
[2025.06.29-13.43.48:264][  0]LogSlate: Icons.Save
[2025.06.29-13.43.48:264][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.29-13.43.48:264][  0]LogSlate: ListView
[2025.06.29-13.43.48:264][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.29-13.43.48:264][  0]LogSlate: SoftwareCursor_Grab
[2025.06.29-13.43.48:264][  0]LogSlate: TableView.DarkRow
[2025.06.29-13.43.48:264][  0]LogSlate: TableView.Row
[2025.06.29-13.43.48:264][  0]LogSlate: TreeView
[2025.06.29-13.43.48:318][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.29-13.43.48:320][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.383 ms
[2025.06.29-13.43.48:334][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.29-13.43.48:334][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.29-13.43.48:334][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.29-13.43.48:357][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.29-13.43.48:373][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   RHI D3D12: yes
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   NPU:       no
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: Interface availability:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   GPU: yes
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   RDG: yes
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT:   NPU: no
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: No NPU adapter found with attribute DXCORE_ADAPTER_ATTRIBUTE_D3D12_GENERIC_ML (Windows 11 Version 24H2 or newer)!
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2060 (Compute, Graphics)
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.29-13.43.48:579][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.29-13.43.48:682][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 8E4D7308C03943088000000000006300 | Instance: 0ACC6791401E04400EFD158587EDB0FF (ADMINISTRATOR-9936).
[2025.06.29-13.43.48:734][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.29-13.43.48:734][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.29-13.43.48:734][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:60570'.
[2025.06.29-13.43.48:738][  0]LogUdpMessaging: Display: Added local interface '192.168.0.177' to multicast group '230.0.0.1:6666'
[2025.06.29-13.43.48:741][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.06.29-13.43.48:744][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.29-13.43.48:848][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.29-13.43.48:849][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.29-13.43.48:867][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.29-13.43.48:934][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.06.29-13.43.48:934][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.06.29-13.43.48:939][  0]LogTimingProfiler: Initialize
[2025.06.29-13.43.48:939][  0]LogTimingProfiler: OnSessionChanged
[2025.06.29-13.43.48:939][  0]LoadingProfiler: Initialize
[2025.06.29-13.43.48:939][  0]LoadingProfiler: OnSessionChanged
[2025.06.29-13.43.48:939][  0]LogNetworkingProfiler: Initialize
[2025.06.29-13.43.48:939][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.29-13.43.48:939][  0]LogMemoryProfiler: Initialize
[2025.06.29-13.43.48:939][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.29-13.43.49:098][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.29-13.43.49:131][  0]SourceControl: Revision control is disabled
[2025.06.29-13.43.49:138][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.29-13.43.49:138][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.29-13.43.49:138][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.29-13.43.49:138][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.29-13.43.49:142][  0]SourceControl: Revision control is disabled
[2025.06.29-13.43.49:250][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.29-13.43.49:255][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.29-13.43.49:255][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.29-13.43.49:313][  0]LogCollectionManager: Loaded 0 collections in 0.001241 seconds
[2025.06.29-13.43.49:315][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Saved/Collections/' took 0.00s
[2025.06.29-13.43.49:317][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Developers/maxwe/Collections/' took 0.00s
[2025.06.29-13.43.49:319][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/Collections/' took 0.00s
[2025.06.29-13.43.49:341][  0]LogTurnkeySupport: Turnkey Device: Win64@Administrator: (Name=Administrator, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.06.29-13.43.49:344][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.29-13.43.49:344][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.06.29-13.43.49:344][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.29-13.43.49:344][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.06.29-13.43.49:361][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.06.29-13.43.49:361][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.06.29-13.43.49:361][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.06.29-13.43.49:361][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.06.29-13.43.49:377][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-41373641 booting at 2025-06-29T13:43:49.377Z using C
[2025.06.29-13.43.49:377][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=ToonTank, ProductVersion=++UE5+Release-5.6-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.29-13.43.49:377][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.29-13.43.49:377][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.29-13.43.49:382][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.29-13.43.49:382][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.29-13.43.49:382][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.29-13.43.49:382][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000057
[2025.06.29-13.43.49:434][  0]LogUObjectArray: 42364 objects as part of root set at end of initial load.
[2025.06.29-13.43.49:434][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.29-13.43.49:552][  0]LogEngine: Initializing Engine...
[2025.06.29-13.43.49:699][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.06.29-13.43.49:700][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.29-13.43.49:937][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.29-13.43.49:949][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.29-13.43.49:956][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.29-13.43.49:965][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.06.29-13.43.49:965][  0]LogInit: Texture streaming: Enabled
[2025.06.29-13.43.49:969][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.0-43139311+++UE5+Release-5.6 )
[2025.06.29-13.43.49:973][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.29-13.43.49:975][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.29-13.43.49:976][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.29-13.43.49:976][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.29-13.43.49:977][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.29-13.43.49:977][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.29-13.43.49:977][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.29-13.43.49:977][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.29-13.43.49:977][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.29-13.43.49:977][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.29-13.43.49:982][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.29-13.43.50:092][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.29-13.43.50:092][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.29-13.43.50:094][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.29-13.43.50:094][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.29-13.43.50:094][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.29-13.43.50:094][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.29-13.43.50:096][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.29-13.43.50:096][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.29-13.43.50:096][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.29-13.43.50:097][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.29-13.43.50:097][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.29-13.43.50:101][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.29-13.43.50:105][  0]LogInit: Undo buffer set to 256 MB
[2025.06.29-13.43.50:105][  0]LogInit: Transaction tracking system initialized
[2025.06.29-13.43.50:111][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../UE Project/ToonTank/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.29-13.43.50:138][  0]LocalizationService: Localization service is disabled
[2025.06.29-13.43.50:253][  0]LogFileCache: Scanning file cache for directory 'Y:/UE Project/ToonTank/Content/' took 0.00s
[2025.06.29-13.43.50:288][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.06.29-13.43.50:293][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.06.29-13.43.50:293][  0]LogPython: Using Python 3.11.8
[2025.06.29-13.43.50:312][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.06.29-13.43.50:659][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.29-13.43.50:681][  0]LogEditorDataStorage: Initializing
[2025.06.29-13.43.50:684][  0]LogEditorDataStorage: Initialized
[2025.06.29-13.43.50:687][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.29-13.43.50:744][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.29-13.43.50:784][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.29-13.43.50:788][  0]SourceControl: Revision control is disabled
[2025.06.29-13.43.50:788][  0]LogUnrealEdMisc: Loading editor; pre map load, took 8.729
[2025.06.29-13.43.50:789][  0]Cmd: MAP LOAD FILE="../../../../UE Project/ToonTank/Content/Maps/Main.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.29-13.43.50:791][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.29-13.43.50:791][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.43.50:810][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.29-13.43.50:812][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.06.29-13.43.50:900][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Main'.
[2025.06.29-13.43.50:900][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.29-13.43.50:917][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.29-13.43.50:937][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.60ms
[2025.06.29-13.43.50:938][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.29-13.43.50:938][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.055ms to complete.
[2025.06.29-13.43.50:945][  0]LogUnrealEdMisc: Total Editor Startup Time, took 8.886
[2025.06.29-13.43.51:055][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.06.29-13.43.51:177][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.43.51:256][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.43.51:337][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.43.51:421][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.43.51:462][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:462][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.29-13.43.51:462][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:463][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.29-13.43.51:463][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:463][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:463][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:463][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:464][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:464][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.29-13.43.51:464][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:465][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:465][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:465][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.29-13.43.51:465][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:465][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:466][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:466][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:466][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.29-13.43.51:466][  0]LogPakFile: Display: Mounted Pak file 'Y:/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.29-13.43.51:529][  0]LogSlate: Took 0.000201 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.29-13.43.51:678][  0]LogAssetRegistry: Display: Asset registry cache written as 66.6 MiB to ../../../../UE Project/ToonTank/Intermediate/CachedAssetRegistry_*.bin
[2025.06.29-13.43.51:709][  0]LogSlate: Took 0.000207 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.29-13.43.51:713][  0]LogSlate: Took 0.000207 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.29-13.43.51:715][  0]LogSlate: Took 0.000159 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.29-13.43.51:798][  0]LogSlate: Took 0.000226 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.29-13.43.52:167][  0]LogStall: Startup...
[2025.06.29-13.43.52:170][  0]LogStall: Startup complete.
[2025.06.29-13.43.52:187][  0]LogLoad: (Engine Initialization) Total time: 10.13 seconds
[2025.06.29-13.43.52:545][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.29-13.43.52:545][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.29-13.43.52:614][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.43.52:616][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.29-13.43.52:648][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.29-13.43.52:652][  0]LogPython: Display: Running start-up script Y:/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 36.042 ms
[2025.06.29-13.43.52:930][  1]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.29-13.43.52:940][  1]LogAssetRegistry: AssetRegistryGather time 0.1705s: AssetDataDiscovery 0.0184s, AssetDataGather 0.0671s, StoreResults 0.0851s. Wall time 5.8440s.
	NumCachedDirectories 0. NumUncachedDirectories 1470. NumCachedFiles 7412. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.06.29-13.43.52:974][  1]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.06.29-13.43.52:986][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.29-13.43.52:987][  1]LogSourceControl: Uncontrolled asset discovery started...
[2025.06.29-13.43.53:133][  1]LogSourceControl: Uncontrolled asset discovery finished in 0.146477 seconds (Found 7388 uncontrolled assets)
[2025.06.29-13.43.53:197][  2]LogSlate: Took 0.000227 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.29-13.43.53:247][  3]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.29-13.43.53:807][ 50]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 4.416569
[2025.06.29-13.43.53:808][ 50]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.29-13.43.53:809][ 50]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4.424917
[2025.06.29-13.43.55:239][197]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.29-13.43.56:187][297]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 6.796352
[2025.06.29-13.43.56:189][297]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.29-13.43.56:189][297]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6.796352, Update Interval: 323.324677
[2025.06.29-13.44.00:089][707]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.29-13.44.00:111][707]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.29-13.44.00:117][707]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.29-13.44.00:117][707]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.29-13.44.00:117][707]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.29-13.44.00:123][707]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006137s)
[2025.06.29-13.44.00:123][707]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006198s)
[2025.06.29-13.44.00:192][707]LogUObjectHash: Compacting FUObjectHashTables data took   0.62ms
[2025.06.29-13.44.00:194][707]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.29-13.44.00:196][707]LogPlayLevel: PIE: World Init took: (0.002008s)
[2025.06.29-13.44.00:197][707]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.29-13.44.00:197][707]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.29-13.44.00:197][707]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.29-13.44.00:197][707]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.29-13.44.00:197][707]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.29-13.44.00:197][707]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.29-13.44.00:200][707]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.29-13.44.00:287][707]LogAudioMixer: Display: Using Audio Hardware Device Speakers (Realtek(R) Audio)
[2025.06.29-13.44.00:287][707]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.29-13.44.00:287][707]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.29-13.44.00:287][707]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.29-13.44.00:288][707]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.29-13.44.00:288][707]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.29-13.44.00:291][707]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.29-13.44.00:291][707]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.29-13.44.00:291][707]LogInit: FAudioDevice initialized with ID 2.
[2025.06.29-13.44.00:291][707]LogAudio: Display: Audio Device (ID: 2) registered with world 'Main'.
[2025.06.29-13.44.00:291][707]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.29-13.44.00:295][707]LogLoad: Game class is 'GameModeBase'
[2025.06.29-13.44.00:297][707]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.29-21.44.00
[2025.06.29-13.44.00:298][707]LogWorld: Bringing up level for play took: 0.002443
[2025.06.29-13.44.00:300][707]LogOnline: OSS: Created online subsystem instance for: :Context_3
[2025.06.29-13.44.00:306][707]PIE: Server logged in
[2025.06.29-13.44.00:309][707]PIE: Play in editor total start time 0.201 seconds.
[2025.06.29-13.44.01:112][782]LogTemp: Warning: OnHit
[2025.06.29-13.44.01:257][797]LogTemp: Warning: OnHit
[2025.06.29-13.44.01:266][798]LogTemp: Warning: OnHit
[2025.06.29-13.44.01:274][799]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:103][893]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:103][893]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:232][907]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:232][907]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:232][907]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:232][907]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:241][908]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:241][908]LogTemp: Warning: OnHit
[2025.06.29-13.44.02:628][952]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:100][  5]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:100][  5]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:258][ 22]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:258][ 22]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:579][ 56]LogTemp: Warning: OnHit
[2025.06.29-13.44.03:579][ 56]LogTemp: Warning: OnHit
[2025.06.29-13.44.15:883][352]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.29-13.44.18:466][601]LogTemp: Warning: OnHit
[2025.06.29-13.44.19:437][710]LogTemp: Warning: OnHit
[2025.06.29-13.44.20:625][842]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.44.20:625][842]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.29-13.44.20:626][842]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.29-13.44.20:626][842]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.44.20:628][842]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.29-13.44.20:632][842]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.44.20:653][842]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-13.44.20:653][842]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.29-13.44.20:653][842]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=4
[2025.06.29-13.44.20:656][842]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2, StreamState=2
[2025.06.29-13.44.20:665][842]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.06.29-13.44.20:727][843]LogPlayLevel: Display: Destroying online subsystem :Context_3
[2025.06.29-13.44.20:982][868]Cmd: SELECT NONE
[2025.06.29-13.45.45:415][665]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.29-13.49.20:070][308]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 330.733154
[2025.06.29-13.49.21:736][313]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-13.49.21:736][313]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 332.066223, Update Interval: 347.233490
[2025.06.29-13.53.21:149][219]LogSlate: Took 0.000237 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.29-13.53.33:855][687]LogStreaming: Display: FlushAsyncLoading(330): 1 QueuedPackages, 0 AsyncPackages
[2025.06.29-13.53.33:872][687]Candidate modules for hot reload:
[2025.06.29-13.53.33:872][687]  ToonTank
[2025.06.29-13.53.33:878][687]Launching UnrealBuildTool... [Y:/UE_5.6/Engine/Build/BatchFiles/Build.bat  -ModuleWithSuffix=ToonTank,6062 ToonTankEditor Win64 Development -Project="Y:/UE Project/ToonTank/ToonTank.uproject" "Y:/UE Project/ToonTank/ToonTank.uproject"  -IgnoreJunk]
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Using bundled DotNet SDK version: 8.0.300 win-x64
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" -ModuleWithSuffix=ToonTank,6062 ToonTankEditor Win64 Development -Project="Y:/UE Project/ToonTank/ToonTank.uproject" "Y:/UE Project/ToonTank/ToonTank.uproject"  -IgnoreJunk
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Invalidating makefile for ToonTankEditor (source file added)
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Available x64 toolchains (1):
[2025.06.29-13.53.42:005][687]CompilerResultsLog:  * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
[2025.06.29-13.53.42:005][687]CompilerResultsLog:     (Family=14.44.35207, FamilyRank=1, Version=14.44.35211, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Visual Studio 2022 compiler version 14.44.35211 is not a preferred version. Please use the latest preferred version 14.38.33130
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Parsing headers for ToonTankEditor
[2025.06.29-13.53.42:005][687]CompilerResultsLog:   Running Internal UnrealHeaderTool "Y:\UE Project\ToonTank\ToonTank.uproject" "Y:\UE Project\ToonTank\Intermediate\Build\Win64\ToonTankEditor\Development\ToonTankEditor.uhtmanifest" -WarningsAsErrors -installed
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Total of 3 written
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Reflection code generated for ToonTankEditor in 2.4787287 seconds
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Building ToonTankEditor...
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Using Visual Studio 2022 14.44.35211 toolchain (C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207) and Windows 10.0.22621.0 SDK (C:\Program Files (x86)\Windows Kits\10).
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Warning: Visual Studio 2022 compiler is not a preferred version
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade]
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade] Using backward-compatible include order. The latest version of UE has changed the order of includes, which may require code changes. The current setting is:
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade]     IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_3
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade] Suppress this message by setting 'IncludeOrderVersion = EngineIncludeOrderVersion.Unreal5_6;' in ToonTankEditor.Target.cs.
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade] Alternatively you can set this to 'EngineIncludeOrderVersion.Latest' to always use the latest include order. This will potentially cause compile errors when integrating new versions of the engine.
[2025.06.29-13.53.42:005][687]CompilerResultsLog: [Upgrade]
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Determining max actions to execute in parallel (8 physical cores, 16 logical cores)
[2025.06.29-13.53.42:005][687]CompilerResultsLog:   Executing up to 8 processes, one per physical core
[2025.06.29-13.53.42:005][687]CompilerResultsLog: Using Unreal Build Accelerator local executor to run 6 action(s)
[2025.06.29-13.53.42:005][687]CompilerResultsLog:   Storage capacity 40Gb
[2025.06.29-13.53.42:005][687]CompilerResultsLog: ---- Starting trace: 250629_215339 ----
[2025.06.29-13.53.42:006][687]CompilerResultsLog: UbaSessionServer - Disable remote execution (remote sessions will finish current processes)
[2025.06.29-13.53.42:006][687]CompilerResultsLog: ------ Building 6 action(s) started ------
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [1/6] Compile [x64] HealthComponent.cpp
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [2/6] Compile [x64] Module.ToonTank.gen.cpp
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [3/6] Compile [x64] Projectile.cpp
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [4/6] Link [x64] UnrealEditor-ToonTank-6062.lib
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [5/6] Link [x64] UnrealEditor-ToonTank-6062.dll
[2025.06.29-13.53.42:006][687]CompilerResultsLog: [6/6] WriteMetadata ToonTankEditor.target (UBA disabled)
[2025.06.29-13.53.42:006][687]CompilerResultsLog: Trace written to file C:/Users/<USER>/AppData/Local/UnrealBuildTool/Log.uba with size 3.8kb
[2025.06.29-13.53.42:006][687]CompilerResultsLog: Total time in Unreal Build Accelerator local executor: 2.03 seconds
[2025.06.29-13.53.42:006][687]CompilerResultsLog: 
[2025.06.29-13.53.42:006][687]CompilerResultsLog: Result: Succeeded
[2025.06.29-13.53.42:006][687]CompilerResultsLog: Total execution time: 7.86 seconds
[2025.06.29-13.53.42:006][687]LogMainFrame: MainFrame: Module compiling took 8.151 seconds
[2025.06.29-13.53.42:033][687]LogUObjectHash: Compacting FUObjectHashTables data took   0.92ms
[2025.06.29-13.53.42:063][687]LogClass: UPackage /Script/ToonTank Reload.
[2025.06.29-13.53.42:066][687]LogClass: Could not find existing class HealthComponent in package /Script/ToonTank for reload, assuming new or modified class
[2025.06.29-13.53.42:142][687]LogUObjectHash: Compacting FUObjectHashTables data took   0.34ms
[2025.06.29-13.53.42:161][687]Display: HotReload took  8.3s.
[2025.06.29-13.53.42:161][687]Display: Reload/Re-instancing Complete: 1 package changed, 1 class new, 4 classes unchanged, 1 function remapped
[2025.06.29-13.53.42:168][687]Warning: HotReload operation took  8.3s.
[2025.06.29-13.53.42:220][687]LogSlate: Window 'Add C++ Class' being destroyed
[2025.06.29-13.53.42:228][687]LogHotReload: New module detected: UnrealEditor-ToonTank-6062.dll
[2025.06.29-13.53.42:323][687]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-13.53.42:561][687]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.06.29-13.53.42:619][687]LogUObjectHash: Compacting FUObjectHashTables data took   0.48ms
[2025.06.29-13.53.42:657][687]Display: HotReload took  0.3s.
[2025.06.29-13.53.42:657][687]Display: Reload/Re-instancing Complete: No object changes detected
[2025.06.29-13.55.12:845][958]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 683.551880
[2025.06.29-13.55.13:845][961]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-13.55.13:845][961]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 684.219543, Update Interval: 303.521240
[2025.06.29-13.57.09:200][307]LogHotReload: New module detected: UnrealEditor-ToonTank-0004.dll
[2025.06.29-13.57.09:542][308]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-13.57.09:611][308]LogUObjectHash: Compacting FUObjectHashTables data took   0.44ms
[2025.06.29-13.57.09:644][308]LogClass: UPackage /Script/ToonTank Reload.
[2025.06.29-13.57.09:644][308]LogClass: UClass HealthComponent Reload.
[2025.06.29-13.57.09:647][308]LogClass: Could not find existing class HealthComponent in package /Script/ToonTank for reload, assuming new or modified class
[2025.06.29-13.57.09:664][308]Re-instancing HealthComponent after reload.
[2025.06.29-13.57.09:732][308]LogUObjectHash: Compacting FUObjectHashTables data took   0.63ms
[2025.06.29-13.57.09:773][308]Display: HotReload took  0.2s.
[2025.06.29-13.57.09:773][308]Display: Reload/Re-instancing Complete: 1 package changed, 1 class changed, 4 classes unchanged, 1 function remapped
[2025.06.29-13.57.20:388][811]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank
[2025.06.29-13.57.20:388][811]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.29-13.57.20:427][811]LogStreaming: Display: FlushAsyncLoading(332): 1 QueuedPackages, 0 AsyncPackages
[2025.06.29-13.57.20:967][811]LogStreaming: Display: FlushAsyncLoading(526): 1 QueuedPackages, 0 AsyncPackages
[2025.06.29-13.57.21:235][814]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.29-13.57.22:655][976]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.29-13.57.22:655][976]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.57.22:684][976]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank
[2025.06.29-13.57.22:685][976]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.29-13.57.22:711][976]LogStreaming: Display: FlushAsyncLoading(527): 1 QueuedPackages, 0 AsyncPackages
[2025.06.29-13.57.24:075][976]LogUObjectHash: Compacting FUObjectHashTables data took   1.24ms
[2025.06.29-13.58.23:765][680]LogUObjectHash: Compacting FUObjectHashTables data took   1.36ms
[2025.06.29-13.58.24:209][717]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.29-13.58.24:265][717]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/Pawn/BP_PawnTank] ([2] browsable assets)...
[2025.06.29-13.58.24:320][717]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Pawn/BP_PawnTank.BP_PawnTank]
[2025.06.29-13.58.24:321][717]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Pawn/BP_PawnTank]
[2025.06.29-13.58.24:321][717]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Pawn/BP_PawnTank" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTank.uasset" SILENT=true
[2025.06.29-13.58.24:327][717]LogSavePackage: Moving output files for package: /Game/Blueprints/Pawn/BP_PawnTank
[2025.06.29-13.58.24:328][717]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_PawnTank8B62BF99454BA2282596ADB4851F37D0.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTank.uasset'
[2025.06.29-13.58.24:341][717]LogFileHelpers: InternalPromptForCheckoutAndSave took 132.404 ms
[2025.06.29-13.58.24:400][717]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.29-13.58.24:400][717]LogContentValidation: Enabled validators:
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.29-13.58.24:400][717]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.29-13.58.24:400][717]AssetCheck: /Game/Blueprints/Pawn/BP_PawnTank Validating asset
[2025.06.29-13.58.27:705][ 24]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Pawn/BP_PawnTurret.BP_PawnTurret
[2025.06.29-13.58.27:705][ 24]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.29-13.58.29:546][178]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.29-13.58.29:546][178]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-13.58.29:567][178]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/Pawn/BP_PawnTurret.BP_PawnTurret
[2025.06.29-13.58.29:568][178]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.29-13.58.30:108][178]LogUObjectHash: Compacting FUObjectHashTables data took   1.80ms
[2025.06.29-13.58.34:757][635]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.29-13.58.34:798][635]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/Pawn/BP_PawnTurret] ([2] browsable assets)...
[2025.06.29-13.58.34:865][635]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Pawn/BP_PawnTurret.BP_PawnTurret]
[2025.06.29-13.58.34:866][635]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Pawn/BP_PawnTurret]
[2025.06.29-13.58.34:866][635]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Pawn/BP_PawnTurret" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTurret.uasset" SILENT=true
[2025.06.29-13.58.34:870][635]LogSavePackage: Moving output files for package: /Game/Blueprints/Pawn/BP_PawnTurret
[2025.06.29-13.58.34:881][635]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_PawnTurret3EF4A3094A3C0CE8FD180F9D81E6BFCE.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTurret.uasset'
[2025.06.29-13.58.34:889][635]LogFileHelpers: InternalPromptForCheckoutAndSave took 131.396 ms (total: 263.801 ms)
[2025.06.29-13.58.34:946][635]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.29-13.58.34:946][635]LogContentValidation: Enabled validators:
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.29-13.58.34:946][635]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.29-13.58.34:946][635]AssetCheck: /Game/Blueprints/Pawn/BP_PawnTurret Validating asset
[2025.06.29-13.58.35:378][677]LogUObjectHash: Compacting FUObjectHashTables data took   1.38ms
[2025.06.29-13.58.36:427][796]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.29-13.58.36:463][796]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/Pawn/BP_PawnTurret] ([2] browsable assets)...
[2025.06.29-13.58.36:513][796]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/Pawn/BP_PawnTurret.BP_PawnTurret]
[2025.06.29-13.58.36:513][796]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/Pawn/BP_PawnTurret]
[2025.06.29-13.58.36:513][796]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/Pawn/BP_PawnTurret" FILE="../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTurret.uasset" SILENT=true
[2025.06.29-13.58.36:517][796]LogSavePackage: Moving output files for package: /Game/Blueprints/Pawn/BP_PawnTurret
[2025.06.29-13.58.36:517][796]LogSavePackage: Moving '../../../../UE Project/ToonTank/Saved/BP_PawnTurret92365CC1427D9029F68BA28DEDC1ECD6.tmp' to '../../../../UE Project/ToonTank/Content/Blueprints/Pawn/BP_PawnTurret.uasset'
[2025.06.29-13.58.36:527][796]LogFileHelpers: InternalPromptForCheckoutAndSave took 99.919 ms (total: 363.720 ms)
[2025.06.29-13.58.36:563][796]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.29-13.58.36:563][796]LogContentValidation: Enabled validators:
[2025.06.29-13.58.36:563][796]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.29-13.58.36:563][796]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.29-13.58.36:563][796]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.29-13.58.36:564][796]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.29-13.58.36:564][796]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.29-13.58.36:564][796]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.29-13.58.36:564][796]AssetCheck: /Game/Blueprints/Pawn/BP_PawnTurret Validating asset
[2025.06.29-13.58.39:845][176]LogUObjectHash: Compacting FUObjectHashTables data took   1.34ms
[2025.06.29-14.00.23:451][596]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 994.191040
[2025.06.29-14.00.23:726][629]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.00.23:726][629]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 994.457275, Update Interval: 301.140778
[2025.06.29-14.03.47:889][102]LogTemp: Warning: UI Min (1.0) >= UI Max (.01) for Ranged Numeric property /Script/Engine.PostProcessSettings:LumenDiffuseColorBoost
[2025.06.29-14.05.35:713][648]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1306.484253
[2025.06.29-14.05.36:714][651]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.05.36:714][651]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1307.150879, Update Interval: 343.736084
[2025.06.29-14.11.42:438][748]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1673.239258
[2025.06.29-14.11.44:105][753]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.11.44:106][753]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1674.573242, Update Interval: 326.516296
[2025.06.29-14.17.42:493][828]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2033.319458
[2025.06.29-14.17.43:493][831]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.17.43:493][831]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2033.987305, Update Interval: 316.805939
[2025.06.29-14.19.33:258][161]LogAudioMixer: Display: FMixerPlatformXAudio2: Changing default audio render device to new device: Role=Console, DeviceName=Headphones (soundcore Liberty 4 NC Stereo), InstanceID=1
[2025.06.29-14.19.33:258][161]LogAudioMixer: Display: Attempt to swap audio render device to new device: '{0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}', because: 'FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged', force=1
[2025.06.29-14.19.33:509][161]LogAudioMixer: Display: FMixerPlatformXAudio2::PreDeviceSwap - Starting swap to [{0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}]
[2025.06.29-14.19.33:509][161]LogAudioMixer: Display: FMixerPlatformXAudio2::EnqueueAsyncDeviceSwap - enqueuing async device swap
[2025.06.29-14.19.33:509][161]LogAudioMixer: Display: FMixerPlatformXAudio2::PerformDeviceSwap - AsyncTask Start. Because=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged
[2025.06.29-14.19.33:844][162]LogAudioMixer: Display: FMixerPlatformXAudio2::PostDeviceSwap - successful Swap new Device is (NumChannels=2, SampleRate=44100, DeviceID={0.0.0.00000000}.{30722f7d-8149-4503-a93f-dffed306e7b0}, Name=Headphones (soundcore Liberty 4 NC Stereo)), Reason=FMixerPlatformXAudio2::OnDefaultRenderDeviceChanged, InstanceID=1, DurationMS=31.65
[2025.06.29-14.19.33:844][162]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.29-14.19.33:844][162]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.29-14.19.33:846][162]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.29-14.23.33:885][882]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2384.736328
[2025.06.29-14.23.34:884][885]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.23.34:884][885]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2385.403076, Update Interval: 308.780182
[2025.06.29-14.29.33:265][960]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2744.133545
[2025.06.29-14.29.34:264][963]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.29.34:264][963]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2744.800537, Update Interval: 339.912720
[2025.06.29-14.30.36:277][149]LogHotReload: New module detected: UnrealEditor-ToonTank-0006.dll
[2025.06.29-14.30.36:952][151]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-14.30.37:044][151]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.06.29-14.30.37:082][151]LogClass: UPackage /Script/ToonTank Reload.
[2025.06.29-14.30.37:082][151]LogClass: UClass HealthComponent Reload.
[2025.06.29-14.30.37:083][151]LogClass: UClass Projectile Reload.
[2025.06.29-14.30.37:085][151]LogClass: Could not find existing class HealthComponent in package /Script/ToonTank for reload, assuming new or modified class
[2025.06.29-14.30.37:085][151]LogClass: Function DamageTaken is new or belongs to a modified class.
[2025.06.29-14.30.37:085][151]LogClass: Could not find existing class Projectile in package /Script/ToonTank for reload, assuming new or modified class
[2025.06.29-14.30.37:085][151]LogClass: Function OnHit is new or belongs to a modified class.
[2025.06.29-14.30.37:111][151]Re-instancing HealthComponent after reload.
[2025.06.29-14.30.37:155][151]Re-instancing Projectile after reload.
[2025.06.29-14.30.37:305][151]LogUObjectHash: Compacting FUObjectHashTables data took   1.41ms
[2025.06.29-14.30.37:353][151]Display: HotReload took  0.4s.
[2025.06.29-14.30.37:353][151]Display: Reload/Re-instancing Complete: 1 package changed, 2 classes changed, 3 classes unchanged
[2025.06.29-14.36.05:576][134]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3136.465576
[2025.06.29-14.36.06:575][137]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.36.06:575][137]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3137.133057, Update Interval: 311.193573
[2025.06.29-14.42.14:965][242]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3505.876709
[2025.06.29-14.42.15:964][245]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.42.15:964][245]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3506.545166, Update Interval: 340.275269
[2025.06.29-14.45.45:415][874]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.29-14.46.18:330][972]LogHotReload: New module detected: UnrealEditor-ToonTank-0007.dll
[2025.06.29-14.46.18:672][973]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-14.46.18:833][973]LogUObjectHash: Compacting FUObjectHashTables data took   2.01ms
[2025.06.29-14.46.19:086][973]LogUObjectHash: Compacting FUObjectHashTables data took   1.52ms
[2025.06.29-14.46.19:147][973]Display: HotReload took  0.5s.
[2025.06.29-14.46.19:147][973]Display: Reload/Re-instancing Complete: 1 package changed, 5 classes unchanged, 2 functions remapped
[2025.06.29-14.48.27:659][358]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3878.586670
[2025.06.29-14.48.28:660][361]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.48.28:660][361]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3879.255127, Update Interval: 319.559937
[2025.06.29-14.52.09:352][ 23]LogHotReload: New module detected: UnrealEditor-ToonTank-0008.dll
[2025.06.29-14.52.09:693][ 24]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-14.52.09:856][ 24]LogUObjectHash: Compacting FUObjectHashTables data took   1.88ms
[2025.06.29-14.52.10:075][ 24]LogUObjectHash: Compacting FUObjectHashTables data took   2.75ms
[2025.06.29-14.52.10:135][ 24]Display: HotReload took  0.4s.
[2025.06.29-14.52.10:135][ 24]Display: Reload/Re-instancing Complete: 1 package changed, 5 classes unchanged, 2 functions remapped
[2025.06.29-14.52.42:323][344]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.29-14.52.42:333][344]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.29-14.52.42:333][344]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.52.42:340][344]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006145s)
[2025.06.29-14.52.42:340][344]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006179s)
[2025.06.29-14.52.42:447][344]LogUObjectHash: Compacting FUObjectHashTables data took   1.61ms
[2025.06.29-14.52.42:448][344]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.29-14.52.42:451][344]LogPlayLevel: PIE: World Init took: (0.002397s)
[2025.06.29-14.52.42:451][344]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.29-14.52.42:451][344]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.29-14.52.42:451][344]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.29-14.52.42:451][344]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.29-14.52.42:451][344]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.29-14.52.42:451][344]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.29-14.52.42:452][344]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.29-14.52.42:452][344]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.29-14.52.42:452][344]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.29-14.52.42:452][344]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.29-14.52.42:452][344]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.29-14.52.42:452][344]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.29-14.52.42:455][344]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.29-14.52.42:484][344]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.06.29-14.52.42:484][344]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.29-14.52.42:484][344]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.29-14.52.42:484][344]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.29-14.52.42:485][344]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.29-14.52.42:485][344]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.29-14.52.42:488][344]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.29-14.52.42:488][344]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.29-14.52.42:488][344]LogInit: FAudioDevice initialized with ID 3.
[2025.06.29-14.52.42:488][344]LogAudio: Display: Audio Device (ID: 3) registered with world 'Main'.
[2025.06.29-14.52.42:488][344]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.29-14.52.42:493][344]LogLoad: Game class is 'GameModeBase'
[2025.06.29-14.52.42:495][344]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.29-22.52.42
[2025.06.29-14.52.42:496][344]LogWorld: Bringing up level for play took: 0.002465
[2025.06.29-14.52.42:498][344]LogOnline: OSS: Created online subsystem instance for: :Context_10
[2025.06.29-14.52.42:500][344]PIE: Server logged in
[2025.06.29-14.52.42:503][344]PIE: Play in editor total start time 0.17 seconds.
[2025.06.29-14.52.43:339][411]LogTemp: Warning: Health: 50.000000
[2025.06.29-14.52.43:495][427]LogTemp: Warning: Health: 0.000000
[2025.06.29-14.52.43:495][427]LogTemp: Warning: Health: 50.000000
[2025.06.29-14.52.43:513][429]LogTemp: Warning: Health: -50.000000
[2025.06.29-14.52.50:718][225]LogTemp: Warning: Health: -100.000000
[2025.06.29-14.52.54:591][620]LogTemp: Warning: Health: -150.000000
[2025.06.29-14.52.55:558][710]LogTemp: Warning: Health: -200.000000
[2025.06.29-14.52.55:569][711]LogTemp: Warning: Health: -250.000000
[2025.06.29-14.52.55:579][712]LogTemp: Warning: Health: -300.000000
[2025.06.29-14.52.55:579][712]LogTemp: Warning: Health: -350.000000
[2025.06.29-14.52.55:588][713]LogTemp: Warning: Health: -400.000000
[2025.06.29-14.52.55:597][714]LogTemp: Warning: Health: -450.000000
[2025.06.29-14.52.55:606][715]LogTemp: Warning: Health: -500.000000
[2025.06.29-14.52.55:616][716]LogTemp: Warning: Health: -550.000000
[2025.06.29-14.52.55:625][717]LogTemp: Warning: Health: -600.000000
[2025.06.29-14.52.55:634][718]LogTemp: Warning: Health: -650.000000
[2025.06.29-14.52.55:643][719]LogTemp: Warning: Health: -700.000000
[2025.06.29-14.52.55:651][720]LogTemp: Warning: Health: -750.000000
[2025.06.29-14.52.55:661][721]LogTemp: Warning: Health: -800.000000
[2025.06.29-14.52.55:669][722]LogTemp: Warning: Health: -850.000000
[2025.06.29-14.52.55:678][723]LogTemp: Warning: Health: -900.000000
[2025.06.29-14.52.55:688][724]LogTemp: Warning: Health: -950.000000
[2025.06.29-14.52.55:698][725]LogTemp: Warning: Health: -1000.000000
[2025.06.29-14.52.55:707][726]LogTemp: Warning: Health: -1050.000000
[2025.06.29-14.52.55:717][727]LogTemp: Warning: Health: -1100.000000
[2025.06.29-14.52.55:726][728]LogTemp: Warning: Health: -1150.000000
[2025.06.29-14.52.55:736][729]LogTemp: Warning: Health: -1200.000000
[2025.06.29-14.52.55:744][730]LogTemp: Warning: Health: -1250.000000
[2025.06.29-14.52.55:753][731]LogTemp: Warning: Health: -1300.000000
[2025.06.29-14.52.55:762][732]LogTemp: Warning: Health: -1350.000000
[2025.06.29-14.52.55:762][732]LogTemp: Warning: Health: -1400.000000
[2025.06.29-14.52.55:771][733]LogTemp: Warning: Health: -1450.000000
[2025.06.29-14.52.55:781][734]LogTemp: Warning: Health: -1500.000000
[2025.06.29-14.52.55:791][735]LogTemp: Warning: Health: -1550.000000
[2025.06.29-14.52.55:799][736]LogTemp: Warning: Health: -1600.000000
[2025.06.29-14.52.55:807][737]LogTemp: Warning: Health: -1650.000000
[2025.06.29-14.52.55:817][738]LogTemp: Warning: Health: -1700.000000
[2025.06.29-14.52.55:826][739]LogTemp: Warning: Health: -1750.000000
[2025.06.29-14.52.55:835][740]LogTemp: Warning: Health: -1800.000000
[2025.06.29-14.52.55:844][741]LogTemp: Warning: Health: -1850.000000
[2025.06.29-14.52.55:853][742]LogTemp: Warning: Health: -1900.000000
[2025.06.29-14.52.55:862][743]LogTemp: Warning: Health: -1950.000000
[2025.06.29-14.52.55:871][744]LogTemp: Warning: Health: -2000.000000
[2025.06.29-14.52.55:881][745]LogTemp: Warning: Health: -2050.000000
[2025.06.29-14.52.55:890][746]LogTemp: Warning: Health: -2100.000000
[2025.06.29-14.52.55:898][747]LogTemp: Warning: Health: -2150.000000
[2025.06.29-14.52.55:908][748]LogTemp: Warning: Health: -2200.000000
[2025.06.29-14.52.55:917][749]LogTemp: Warning: Health: -2250.000000
[2025.06.29-14.52.55:925][750]LogTemp: Warning: Health: -2300.000000
[2025.06.29-14.52.55:934][751]LogTemp: Warning: Health: -2350.000000
[2025.06.29-14.52.55:944][752]LogTemp: Warning: Health: -2400.000000
[2025.06.29-14.52.55:952][753]LogTemp: Warning: Health: -2450.000000
[2025.06.29-14.52.55:961][754]LogTemp: Warning: Health: -2500.000000
[2025.06.29-14.52.55:970][755]LogTemp: Warning: Health: -2550.000000
[2025.06.29-14.52.55:979][756]LogTemp: Warning: Health: -2600.000000
[2025.06.29-14.52.55:988][757]LogTemp: Warning: Health: -2650.000000
[2025.06.29-14.52.55:998][758]LogTemp: Warning: Health: -2700.000000
[2025.06.29-14.52.56:007][759]LogTemp: Warning: Health: -2750.000000
[2025.06.29-14.52.56:015][760]LogTemp: Warning: Health: -2800.000000
[2025.06.29-14.52.56:024][761]LogTemp: Warning: Health: -2850.000000
[2025.06.29-14.52.56:033][762]LogTemp: Warning: Health: -2900.000000
[2025.06.29-14.52.56:043][763]LogTemp: Warning: Health: -2950.000000
[2025.06.29-14.52.56:052][764]LogTemp: Warning: Health: -3000.000000
[2025.06.29-14.52.56:062][765]LogTemp: Warning: Health: -3050.000000
[2025.06.29-14.52.56:071][766]LogTemp: Warning: Health: -3100.000000
[2025.06.29-14.52.56:081][767]LogTemp: Warning: Health: -3150.000000
[2025.06.29-14.52.56:089][768]LogTemp: Warning: Health: -3200.000000
[2025.06.29-14.52.56:099][769]LogTemp: Warning: Health: -3250.000000
[2025.06.29-14.52.56:107][770]LogTemp: Warning: Health: -3300.000000
[2025.06.29-14.52.56:117][771]LogTemp: Warning: Health: -3350.000000
[2025.06.29-14.52.56:340][796]LogTemp: Warning: Health: -3400.000000
[2025.06.29-14.52.56:349][797]LogTemp: Warning: Health: -3450.000000
[2025.06.29-14.52.56:357][798]LogTemp: Warning: Health: -3500.000000
[2025.06.29-14.52.56:366][799]LogTemp: Warning: Health: -3550.000000
[2025.06.29-14.52.56:374][800]LogTemp: Warning: Health: -3600.000000
[2025.06.29-14.52.56:383][801]LogTemp: Warning: Health: -3650.000000
[2025.06.29-14.52.56:391][802]LogTemp: Warning: Health: -3700.000000
[2025.06.29-14.52.56:400][803]LogTemp: Warning: Health: -3750.000000
[2025.06.29-14.52.56:409][804]LogTemp: Warning: Health: -3800.000000
[2025.06.29-14.52.56:417][805]LogTemp: Warning: Health: -3850.000000
[2025.06.29-14.52.56:427][806]LogTemp: Warning: Health: -3900.000000
[2025.06.29-14.52.56:435][807]LogTemp: Warning: Health: -3950.000000
[2025.06.29-14.52.56:444][808]LogTemp: Warning: Health: -4000.000000
[2025.06.29-14.52.56:454][809]LogTemp: Warning: Health: -4050.000000
[2025.06.29-14.52.56:463][810]LogTemp: Warning: Health: -4100.000000
[2025.06.29-14.52.56:472][811]LogTemp: Warning: Health: -4150.000000
[2025.06.29-14.52.56:480][812]LogTemp: Warning: Health: -4200.000000
[2025.06.29-14.52.56:489][813]LogTemp: Warning: Health: -4250.000000
[2025.06.29-14.52.56:499][814]LogTemp: Warning: Health: -4300.000000
[2025.06.29-14.52.56:508][815]LogTemp: Warning: Health: -4350.000000
[2025.06.29-14.52.56:517][816]LogTemp: Warning: Health: -4400.000000
[2025.06.29-14.52.56:525][817]LogTemp: Warning: Health: -4450.000000
[2025.06.29-14.52.56:534][818]LogTemp: Warning: Health: -4500.000000
[2025.06.29-14.52.56:543][819]LogTemp: Warning: Health: -4550.000000
[2025.06.29-14.52.56:543][819]LogTemp: Warning: Health: -4600.000000
[2025.06.29-14.52.56:551][820]LogTemp: Warning: Health: -4650.000000
[2025.06.29-14.52.56:560][821]LogTemp: Warning: Health: -4700.000000
[2025.06.29-14.52.56:560][821]LogTemp: Warning: Health: -4750.000000
[2025.06.29-14.52.56:569][822]LogTemp: Warning: Health: -4800.000000
[2025.06.29-14.52.56:578][823]LogTemp: Warning: Health: -4850.000000
[2025.06.29-14.52.56:586][824]LogTemp: Warning: Health: -4900.000000
[2025.06.29-14.52.56:595][825]LogTemp: Warning: Health: -4950.000000
[2025.06.29-14.52.56:603][826]LogTemp: Warning: Health: -5000.000000
[2025.06.29-14.52.56:612][827]LogTemp: Warning: Health: -5050.000000
[2025.06.29-14.52.56:621][828]LogTemp: Warning: Health: -5100.000000
[2025.06.29-14.52.56:630][829]LogTemp: Warning: Health: -5150.000000
[2025.06.29-14.52.56:638][830]LogTemp: Warning: Health: -5200.000000
[2025.06.29-14.52.56:648][831]LogTemp: Warning: Health: -5250.000000
[2025.06.29-14.52.56:657][832]LogTemp: Warning: Health: -5300.000000
[2025.06.29-14.52.56:665][833]LogTemp: Warning: Health: -5350.000000
[2025.06.29-14.52.56:674][834]LogTemp: Warning: Health: -5400.000000
[2025.06.29-14.52.56:682][835]LogTemp: Warning: Health: -5450.000000
[2025.06.29-14.52.56:690][836]LogTemp: Warning: Health: -5500.000000
[2025.06.29-14.52.56:699][837]LogTemp: Warning: Health: -5550.000000
[2025.06.29-14.52.56:707][838]LogTemp: Warning: Health: -5600.000000
[2025.06.29-14.52.56:715][839]LogTemp: Warning: Health: -5650.000000
[2025.06.29-14.52.56:725][840]LogTemp: Warning: Health: -5700.000000
[2025.06.29-14.52.56:732][841]LogTemp: Warning: Health: -5750.000000
[2025.06.29-14.52.56:742][842]LogTemp: Warning: Health: -5800.000000
[2025.06.29-14.52.56:750][843]LogTemp: Warning: Health: -5850.000000
[2025.06.29-14.52.56:759][844]LogTemp: Warning: Health: -5900.000000
[2025.06.29-14.52.56:767][845]LogTemp: Warning: Health: -5950.000000
[2025.06.29-14.52.56:776][846]LogTemp: Warning: Health: -6000.000000
[2025.06.29-14.52.56:784][847]LogTemp: Warning: Health: -6050.000000
[2025.06.29-14.52.56:793][848]LogTemp: Warning: Health: -6100.000000
[2025.06.29-14.52.56:801][849]LogTemp: Warning: Health: -6150.000000
[2025.06.29-14.52.56:810][850]LogTemp: Warning: Health: -6200.000000
[2025.06.29-14.52.56:818][851]LogTemp: Warning: Health: -6250.000000
[2025.06.29-14.52.56:827][852]LogTemp: Warning: Health: -6300.000000
[2025.06.29-14.52.56:836][853]LogTemp: Warning: Health: -6350.000000
[2025.06.29-14.52.56:845][854]LogTemp: Warning: Health: -6400.000000
[2025.06.29-14.52.56:852][855]LogTemp: Warning: Health: -6450.000000
[2025.06.29-14.52.56:862][856]LogTemp: Warning: Health: -6500.000000
[2025.06.29-14.52.56:870][857]LogTemp: Warning: Health: -6550.000000
[2025.06.29-14.52.56:880][858]LogTemp: Warning: Health: -6600.000000
[2025.06.29-14.52.56:888][859]LogTemp: Warning: Health: -6650.000000
[2025.06.29-14.52.56:898][860]LogTemp: Warning: Health: -6700.000000
[2025.06.29-14.52.56:907][861]LogTemp: Warning: Health: -6750.000000
[2025.06.29-14.52.56:918][862]LogTemp: Warning: Health: -6800.000000
[2025.06.29-14.52.56:928][863]LogTemp: Warning: Health: -6850.000000
[2025.06.29-14.52.56:937][864]LogTemp: Warning: Health: -6900.000000
[2025.06.29-14.52.56:946][865]LogTemp: Warning: Health: -6950.000000
[2025.06.29-14.52.56:955][866]LogTemp: Warning: Health: -7000.000000
[2025.06.29-14.52.56:964][867]LogTemp: Warning: Health: -7050.000000
[2025.06.29-14.52.56:973][868]LogTemp: Warning: Health: -7100.000000
[2025.06.29-14.52.56:982][869]LogTemp: Warning: Health: -7150.000000
[2025.06.29-14.52.56:991][870]LogTemp: Warning: Health: -7200.000000
[2025.06.29-14.52.57:001][871]LogTemp: Warning: Health: -7250.000000
[2025.06.29-14.52.57:010][872]LogTemp: Warning: Health: -7300.000000
[2025.06.29-14.52.57:018][873]LogTemp: Warning: Health: -7350.000000
[2025.06.29-14.52.57:028][874]LogTemp: Warning: Health: -7400.000000
[2025.06.29-14.52.57:037][875]LogTemp: Warning: Health: -7450.000000
[2025.06.29-14.52.57:047][876]LogTemp: Warning: Health: -7500.000000
[2025.06.29-14.52.57:056][877]LogTemp: Warning: Health: -7550.000000
[2025.06.29-14.52.57:066][878]LogTemp: Warning: Health: -7600.000000
[2025.06.29-14.52.57:074][879]LogTemp: Warning: Health: -7650.000000
[2025.06.29-14.52.57:084][880]LogTemp: Warning: Health: -7700.000000
[2025.06.29-14.52.57:093][881]LogTemp: Warning: Health: -7750.000000
[2025.06.29-14.52.57:103][882]LogTemp: Warning: Health: -7800.000000
[2025.06.29-14.52.57:112][883]LogTemp: Warning: Health: -7850.000000
[2025.06.29-14.52.57:121][884]LogTemp: Warning: Health: -7900.000000
[2025.06.29-14.52.57:130][885]LogTemp: Warning: Health: -7950.000000
[2025.06.29-14.52.57:138][886]LogTemp: Warning: Health: -8000.000000
[2025.06.29-14.52.57:148][887]LogTemp: Warning: Health: -8050.000000
[2025.06.29-14.52.57:158][888]LogTemp: Warning: Health: -8100.000000
[2025.06.29-14.52.57:167][889]LogTemp: Warning: Health: -8150.000000
[2025.06.29-14.52.57:176][890]LogTemp: Warning: Health: -8200.000000
[2025.06.29-14.52.57:186][891]LogTemp: Warning: Health: -8250.000000
[2025.06.29-14.52.57:195][892]LogTemp: Warning: Health: -8300.000000
[2025.06.29-14.52.57:204][893]LogTemp: Warning: Health: -8350.000000
[2025.06.29-14.52.57:213][894]LogTemp: Warning: Health: -8400.000000
[2025.06.29-14.52.57:222][895]LogTemp: Warning: Health: -8450.000000
[2025.06.29-14.52.57:232][896]LogTemp: Warning: Health: -8500.000000
[2025.06.29-14.52.57:241][897]LogTemp: Warning: Health: -8550.000000
[2025.06.29-14.52.57:251][898]LogTemp: Warning: Health: -8600.000000
[2025.06.29-14.52.57:261][899]LogTemp: Warning: Health: -8650.000000
[2025.06.29-14.52.57:270][900]LogTemp: Warning: Health: -8700.000000
[2025.06.29-14.52.57:280][901]LogTemp: Warning: Health: -8750.000000
[2025.06.29-14.52.57:292][902]LogTemp: Warning: Health: -8800.000000
[2025.06.29-14.52.57:303][903]LogTemp: Warning: Health: -8850.000000
[2025.06.29-14.52.57:313][904]LogTemp: Warning: Health: -8900.000000
[2025.06.29-14.52.57:322][905]LogTemp: Warning: Health: -8950.000000
[2025.06.29-14.52.57:331][906]LogTemp: Warning: Health: -9000.000000
[2025.06.29-14.52.57:340][907]LogTemp: Warning: Health: -9050.000000
[2025.06.29-14.52.57:351][908]LogTemp: Warning: Health: -9100.000000
[2025.06.29-14.52.57:360][909]LogTemp: Warning: Health: -9150.000000
[2025.06.29-14.52.57:368][910]LogTemp: Warning: Health: -9200.000000
[2025.06.29-14.52.58:671][ 42]LogTemp: Warning: Health: -9250.000000
[2025.06.29-14.53.02:624][482]LogTemp: Warning: Health: -9300.000000
[2025.06.29-14.53.02:767][498]LogTemp: Warning: Health: -9350.000000
[2025.06.29-14.53.03:278][555]LogTemp: Warning: Health: -9400.000000
[2025.06.29-14.53.03:288][556]LogTemp: Warning: Health: -9450.000000
[2025.06.29-14.53.03:297][557]LogTemp: Warning: Health: -9500.000000
[2025.06.29-14.53.03:306][558]LogTemp: Warning: Health: -9550.000000
[2025.06.29-14.53.03:314][559]LogTemp: Warning: Health: -9600.000000
[2025.06.29-14.53.03:323][560]LogTemp: Warning: Health: -9650.000000
[2025.06.29-14.53.03:333][561]LogTemp: Warning: Health: -9700.000000
[2025.06.29-14.53.03:342][562]LogTemp: Warning: Health: -9750.000000
[2025.06.29-14.53.03:354][563]LogTemp: Warning: Health: -9800.000000
[2025.06.29-14.53.03:363][564]LogTemp: Warning: Health: -9850.000000
[2025.06.29-14.53.03:372][565]LogTemp: Warning: Health: -9900.000000
[2025.06.29-14.53.03:382][566]LogTemp: Warning: Health: -9950.000000
[2025.06.29-14.53.03:391][567]LogTemp: Warning: Health: -10000.000000
[2025.06.29-14.53.03:401][568]LogTemp: Warning: Health: -10050.000000
[2025.06.29-14.53.03:410][569]LogTemp: Warning: Health: -10100.000000
[2025.06.29-14.53.03:419][570]LogTemp: Warning: Health: -10150.000000
[2025.06.29-14.53.03:427][571]LogTemp: Warning: Health: -10200.000000
[2025.06.29-14.53.03:435][572]LogTemp: Warning: Health: -10250.000000
[2025.06.29-14.53.03:446][573]LogTemp: Warning: Health: -10300.000000
[2025.06.29-14.53.03:456][574]LogTemp: Warning: Health: -10350.000000
[2025.06.29-14.53.03:467][575]LogTemp: Warning: Health: -10400.000000
[2025.06.29-14.53.03:475][576]LogTemp: Warning: Health: -10450.000000
[2025.06.29-14.53.03:484][577]LogTemp: Warning: Health: -10500.000000
[2025.06.29-14.53.03:493][578]LogTemp: Warning: Health: -10550.000000
[2025.06.29-14.53.03:502][579]LogTemp: Warning: Health: -10600.000000
[2025.06.29-14.53.03:509][580]LogTemp: Warning: Health: -10650.000000
[2025.06.29-14.53.03:519][581]LogTemp: Warning: Health: -10700.000000
[2025.06.29-14.53.03:529][582]LogTemp: Warning: Health: -10750.000000
[2025.06.29-14.53.03:538][583]LogTemp: Warning: Health: -10800.000000
[2025.06.29-14.53.03:546][584]LogTemp: Warning: Health: -10850.000000
[2025.06.29-14.53.03:555][585]LogTemp: Warning: Health: -10900.000000
[2025.06.29-14.53.03:565][586]LogTemp: Warning: Health: -10950.000000
[2025.06.29-14.53.03:574][587]LogTemp: Warning: Health: -11000.000000
[2025.06.29-14.53.03:584][588]LogTemp: Warning: Health: -11050.000000
[2025.06.29-14.53.03:593][589]LogTemp: Warning: Health: -11100.000000
[2025.06.29-14.53.03:603][590]LogTemp: Warning: Health: -11150.000000
[2025.06.29-14.53.03:614][591]LogTemp: Warning: Health: -11200.000000
[2025.06.29-14.53.03:623][592]LogTemp: Warning: Health: -11250.000000
[2025.06.29-14.53.03:632][593]LogTemp: Warning: Health: -11300.000000
[2025.06.29-14.53.03:640][594]LogTemp: Warning: Health: -11350.000000
[2025.06.29-14.53.03:649][595]LogTemp: Warning: Health: -11400.000000
[2025.06.29-14.53.03:658][596]LogTemp: Warning: Health: -11450.000000
[2025.06.29-14.53.03:667][597]LogTemp: Warning: Health: -11500.000000
[2025.06.29-14.53.03:676][598]LogTemp: Warning: Health: -11550.000000
[2025.06.29-14.53.03:685][599]LogTemp: Warning: Health: -11600.000000
[2025.06.29-14.53.03:694][600]LogTemp: Warning: Health: -11650.000000
[2025.06.29-14.53.03:703][601]LogTemp: Warning: Health: -11700.000000
[2025.06.29-14.53.03:711][602]LogTemp: Warning: Health: -11750.000000
[2025.06.29-14.53.03:719][603]LogTemp: Warning: Health: -11800.000000
[2025.06.29-14.53.03:728][604]LogTemp: Warning: Health: -11850.000000
[2025.06.29-14.53.03:737][605]LogTemp: Warning: Health: -11900.000000
[2025.06.29-14.53.03:745][606]LogTemp: Warning: Health: -11950.000000
[2025.06.29-14.53.03:754][607]LogTemp: Warning: Health: -12000.000000
[2025.06.29-14.53.03:763][608]LogTemp: Warning: Health: -12050.000000
[2025.06.29-14.53.03:772][609]LogTemp: Warning: Health: -12100.000000
[2025.06.29-14.53.03:782][610]LogTemp: Warning: Health: -12150.000000
[2025.06.29-14.53.03:791][611]LogTemp: Warning: Health: -12200.000000
[2025.06.29-14.53.03:800][612]LogTemp: Warning: Health: -12250.000000
[2025.06.29-14.53.03:810][613]LogTemp: Warning: Health: -12300.000000
[2025.06.29-14.53.03:818][614]LogTemp: Warning: Health: -12350.000000
[2025.06.29-14.53.03:827][615]LogTemp: Warning: Health: -12400.000000
[2025.06.29-14.53.03:836][616]LogTemp: Warning: Health: -12450.000000
[2025.06.29-14.53.03:846][617]LogTemp: Warning: Health: -12500.000000
[2025.06.29-14.53.03:854][618]LogTemp: Warning: Health: -12550.000000
[2025.06.29-14.53.03:864][619]LogTemp: Warning: Health: -12600.000000
[2025.06.29-14.53.03:873][620]LogTemp: Warning: Health: -12650.000000
[2025.06.29-14.53.03:882][621]LogTemp: Warning: Health: -12700.000000
[2025.06.29-14.53.03:891][622]LogTemp: Warning: Health: -12750.000000
[2025.06.29-14.53.03:900][623]LogTemp: Warning: Health: -12800.000000
[2025.06.29-14.53.03:909][624]LogTemp: Warning: Health: -12850.000000
[2025.06.29-14.53.03:916][625]LogTemp: Warning: Health: -12900.000000
[2025.06.29-14.53.03:925][626]LogTemp: Warning: Health: -12950.000000
[2025.06.29-14.53.03:935][627]LogTemp: Warning: Health: -13000.000000
[2025.06.29-14.53.03:945][628]LogTemp: Warning: Health: -13050.000000
[2025.06.29-14.53.03:954][629]LogTemp: Warning: Health: -13100.000000
[2025.06.29-14.53.03:962][630]LogTemp: Warning: Health: -13150.000000
[2025.06.29-14.53.03:971][631]LogTemp: Warning: Health: -13200.000000
[2025.06.29-14.53.03:979][632]LogTemp: Warning: Health: -13250.000000
[2025.06.29-14.53.03:987][633]LogTemp: Warning: Health: -13300.000000
[2025.06.29-14.53.03:995][634]LogTemp: Warning: Health: -13350.000000
[2025.06.29-14.53.04:004][635]LogTemp: Warning: Health: -13400.000000
[2025.06.29-14.53.04:013][636]LogTemp: Warning: Health: -13450.000000
[2025.06.29-14.53.04:023][637]LogTemp: Warning: Health: -13500.000000
[2025.06.29-14.53.04:031][638]LogTemp: Warning: Health: -13550.000000
[2025.06.29-14.53.04:040][639]LogTemp: Warning: Health: -13600.000000
[2025.06.29-14.53.04:048][640]LogTemp: Warning: Health: -13650.000000
[2025.06.29-14.53.05:577][816]LogTemp: Warning: Health: -13700.000000
[2025.06.29-14.53.05:585][817]LogTemp: Warning: Health: -13750.000000
[2025.06.29-14.53.05:594][818]LogTemp: Warning: Health: -13800.000000
[2025.06.29-14.53.05:604][819]LogTemp: Warning: Health: -13850.000000
[2025.06.29-14.53.05:612][820]LogTemp: Warning: Health: -13900.000000
[2025.06.29-14.53.05:621][821]LogTemp: Warning: Health: -13950.000000
[2025.06.29-14.53.05:629][822]LogTemp: Warning: Health: -14000.000000
[2025.06.29-14.53.05:638][823]LogTemp: Warning: Health: -14050.000000
[2025.06.29-14.53.05:646][824]LogTemp: Warning: Health: -14100.000000
[2025.06.29-14.53.05:654][825]LogTemp: Warning: Health: -14150.000000
[2025.06.29-14.53.05:663][826]LogTemp: Warning: Health: -14200.000000
[2025.06.29-14.53.05:672][827]LogTemp: Warning: Health: -14250.000000
[2025.06.29-14.53.05:681][828]LogTemp: Warning: Health: -14300.000000
[2025.06.29-14.53.05:689][829]LogTemp: Warning: Health: -14350.000000
[2025.06.29-14.53.05:698][830]LogTemp: Warning: Health: -14400.000000
[2025.06.29-14.53.05:707][831]LogTemp: Warning: Health: -14450.000000
[2025.06.29-14.53.05:716][832]LogTemp: Warning: Health: -14500.000000
[2025.06.29-14.53.05:724][833]LogTemp: Warning: Health: -14550.000000
[2025.06.29-14.53.05:732][834]LogTemp: Warning: Health: -14600.000000
[2025.06.29-14.53.05:741][835]LogTemp: Warning: Health: -14650.000000
[2025.06.29-14.53.05:749][836]LogTemp: Warning: Health: -14700.000000
[2025.06.29-14.53.05:758][837]LogTemp: Warning: Health: -14750.000000
[2025.06.29-14.53.05:766][838]LogTemp: Warning: Health: -14800.000000
[2025.06.29-14.53.05:774][839]LogTemp: Warning: Health: -14850.000000
[2025.06.29-14.53.05:782][840]LogTemp: Warning: Health: -14900.000000
[2025.06.29-14.53.05:792][841]LogTemp: Warning: Health: -14950.000000
[2025.06.29-14.53.05:800][842]LogTemp: Warning: Health: -15000.000000
[2025.06.29-14.53.05:809][843]LogTemp: Warning: Health: -15050.000000
[2025.06.29-14.53.05:817][844]LogTemp: Warning: Health: -15100.000000
[2025.06.29-14.53.05:826][845]LogTemp: Warning: Health: -15150.000000
[2025.06.29-14.53.05:835][846]LogTemp: Warning: Health: -15200.000000
[2025.06.29-14.53.05:843][847]LogTemp: Warning: Health: -15250.000000
[2025.06.29-14.53.05:850][848]LogTemp: Warning: Health: -15300.000000
[2025.06.29-14.53.05:860][849]LogTemp: Warning: Health: -15350.000000
[2025.06.29-14.53.05:868][850]LogTemp: Warning: Health: -15400.000000
[2025.06.29-14.53.05:877][851]LogTemp: Warning: Health: -15450.000000
[2025.06.29-14.53.05:885][852]LogTemp: Warning: Health: -15500.000000
[2025.06.29-14.53.05:895][853]LogTemp: Warning: Health: -15550.000000
[2025.06.29-14.53.05:903][854]LogTemp: Warning: Health: -15600.000000
[2025.06.29-14.53.05:911][855]LogTemp: Warning: Health: -15650.000000
[2025.06.29-14.53.05:920][856]LogTemp: Warning: Health: -15700.000000
[2025.06.29-14.53.05:928][857]LogTemp: Warning: Health: -15750.000000
[2025.06.29-14.53.05:936][858]LogTemp: Warning: Health: -15800.000000
[2025.06.29-14.53.05:945][859]LogTemp: Warning: Health: -15850.000000
[2025.06.29-14.53.06:542][926]LogTemp: Warning: Health: -15900.000000
[2025.06.29-14.53.06:552][927]LogTemp: Warning: Health: -15950.000000
[2025.06.29-14.53.06:560][928]LogTemp: Warning: Health: -16000.000000
[2025.06.29-14.53.06:569][929]LogTemp: Warning: Health: -16050.000000
[2025.06.29-14.53.06:578][930]LogTemp: Warning: Health: -16100.000000
[2025.06.29-14.53.06:586][931]LogTemp: Warning: Health: -16150.000000
[2025.06.29-14.53.06:595][932]LogTemp: Warning: Health: -16200.000000
[2025.06.29-14.53.06:605][933]LogTemp: Warning: Health: -16250.000000
[2025.06.29-14.53.06:614][934]LogTemp: Warning: Health: -16300.000000
[2025.06.29-14.53.06:623][935]LogTemp: Warning: Health: -16350.000000
[2025.06.29-14.53.06:631][936]LogTemp: Warning: Health: -16400.000000
[2025.06.29-14.53.06:641][937]LogTemp: Warning: Health: -16450.000000
[2025.06.29-14.53.06:650][938]LogTemp: Warning: Health: -16500.000000
[2025.06.29-14.53.06:660][939]LogTemp: Warning: Health: -16550.000000
[2025.06.29-14.53.06:669][940]LogTemp: Warning: Health: -16600.000000
[2025.06.29-14.53.06:678][941]LogTemp: Warning: Health: -16650.000000
[2025.06.29-14.53.06:687][942]LogTemp: Warning: Health: -16700.000000
[2025.06.29-14.53.06:696][943]LogTemp: Warning: Health: -16750.000000
[2025.06.29-14.53.06:704][944]LogTemp: Warning: Health: -16800.000000
[2025.06.29-14.53.06:713][945]LogTemp: Warning: Health: -16850.000000
[2025.06.29-14.53.06:722][946]LogTemp: Warning: Health: -16900.000000
[2025.06.29-14.53.06:731][947]LogTemp: Warning: Health: -16950.000000
[2025.06.29-14.53.06:740][948]LogTemp: Warning: Health: -17000.000000
[2025.06.29-14.53.06:748][949]LogTemp: Warning: Health: -17050.000000
[2025.06.29-14.53.06:757][950]LogTemp: Warning: Health: -17100.000000
[2025.06.29-14.53.06:767][951]LogTemp: Warning: Health: -17150.000000
[2025.06.29-14.53.06:776][952]LogTemp: Warning: Health: -17200.000000
[2025.06.29-14.53.06:786][953]LogTemp: Warning: Health: -17250.000000
[2025.06.29-14.53.06:794][954]LogTemp: Warning: Health: -17300.000000
[2025.06.29-14.53.06:803][955]LogTemp: Warning: Health: -17350.000000
[2025.06.29-14.53.06:813][956]LogTemp: Warning: Health: -17400.000000
[2025.06.29-14.53.06:821][957]LogTemp: Warning: Health: -17450.000000
[2025.06.29-14.53.06:829][958]LogTemp: Warning: Health: -17500.000000
[2025.06.29-14.53.06:838][959]LogTemp: Warning: Health: -17550.000000
[2025.06.29-14.53.06:847][960]LogTemp: Warning: Health: -17600.000000
[2025.06.29-14.53.06:856][961]LogTemp: Warning: Health: -17650.000000
[2025.06.29-14.53.06:865][962]LogTemp: Warning: Health: -17700.000000
[2025.06.29-14.53.06:875][963]LogTemp: Warning: Health: -17750.000000
[2025.06.29-14.53.06:884][964]LogTemp: Warning: Health: -17800.000000
[2025.06.29-14.53.06:892][965]LogTemp: Warning: Health: -17850.000000
[2025.06.29-14.53.06:902][966]LogTemp: Warning: Health: -17900.000000
[2025.06.29-14.53.06:911][967]LogTemp: Warning: Health: -17950.000000
[2025.06.29-14.53.06:920][968]LogTemp: Warning: Health: -18000.000000
[2025.06.29-14.53.06:929][969]LogTemp: Warning: Health: -18050.000000
[2025.06.29-14.53.06:937][970]LogTemp: Warning: Health: -18100.000000
[2025.06.29-14.53.06:945][971]LogTemp: Warning: Health: -18150.000000
[2025.06.29-14.53.06:955][972]LogTemp: Warning: Health: -18200.000000
[2025.06.29-14.53.06:964][973]LogTemp: Warning: Health: -18250.000000
[2025.06.29-14.53.06:973][974]LogTemp: Warning: Health: -18300.000000
[2025.06.29-14.53.06:981][975]LogTemp: Warning: Health: -18350.000000
[2025.06.29-14.53.06:991][976]LogTemp: Warning: Health: -18400.000000
[2025.06.29-14.53.07:000][977]LogTemp: Warning: Health: -18450.000000
[2025.06.29-14.53.07:008][978]LogTemp: Warning: Health: -18500.000000
[2025.06.29-14.53.07:018][979]LogTemp: Warning: Health: -18550.000000
[2025.06.29-14.53.07:027][980]LogTemp: Warning: Health: -18600.000000
[2025.06.29-14.53.07:036][981]LogTemp: Warning: Health: -18650.000000
[2025.06.29-14.53.07:045][982]LogTemp: Warning: Health: -18700.000000
[2025.06.29-14.53.07:054][983]LogTemp: Warning: Health: -18750.000000
[2025.06.29-14.53.07:063][984]LogTemp: Warning: Health: -18800.000000
[2025.06.29-14.53.07:071][985]LogTemp: Warning: Health: -18850.000000
[2025.06.29-14.53.07:081][986]LogTemp: Warning: Health: -18900.000000
[2025.06.29-14.53.07:092][987]LogTemp: Warning: Health: -18950.000000
[2025.06.29-14.53.07:100][988]LogTemp: Warning: Health: -19000.000000
[2025.06.29-14.53.07:109][989]LogTemp: Warning: Health: -19050.000000
[2025.06.29-14.53.07:117][990]LogTemp: Warning: Health: -19100.000000
[2025.06.29-14.53.07:126][991]LogTemp: Warning: Health: -19150.000000
[2025.06.29-14.53.07:134][992]LogTemp: Warning: Health: -19200.000000
[2025.06.29-14.53.07:143][993]LogTemp: Warning: Health: -19250.000000
[2025.06.29-14.53.07:151][994]LogTemp: Warning: Health: -19300.000000
[2025.06.29-14.53.07:159][995]LogTemp: Warning: Health: -19350.000000
[2025.06.29-14.53.07:168][996]LogTemp: Warning: Health: -19400.000000
[2025.06.29-14.53.07:176][997]LogTemp: Warning: Health: -19450.000000
[2025.06.29-14.53.07:184][998]LogTemp: Warning: Health: -19500.000000
[2025.06.29-14.53.07:192][999]LogTemp: Warning: Health: -19550.000000
[2025.06.29-14.53.07:201][  0]LogTemp: Warning: Health: -19600.000000
[2025.06.29-14.53.07:209][  1]LogTemp: Warning: Health: -19650.000000
[2025.06.29-14.53.07:218][  2]LogTemp: Warning: Health: -19700.000000
[2025.06.29-14.53.07:227][  3]LogTemp: Warning: Health: -19750.000000
[2025.06.29-14.53.07:235][  4]LogTemp: Warning: Health: -19800.000000
[2025.06.29-14.53.07:244][  5]LogTemp: Warning: Health: -19850.000000
[2025.06.29-14.53.07:252][  6]LogTemp: Warning: Health: -19900.000000
[2025.06.29-14.53.07:261][  7]LogTemp: Warning: Health: -19950.000000
[2025.06.29-14.53.07:269][  8]LogTemp: Warning: Health: -20000.000000
[2025.06.29-14.53.07:277][  9]LogTemp: Warning: Health: -20050.000000
[2025.06.29-14.53.07:286][ 10]LogTemp: Warning: Health: -20100.000000
[2025.06.29-14.53.07:295][ 11]LogTemp: Warning: Health: -20150.000000
[2025.06.29-14.53.07:304][ 12]LogTemp: Warning: Health: -20200.000000
[2025.06.29-14.53.07:312][ 13]LogTemp: Warning: Health: -20250.000000
[2025.06.29-14.53.07:320][ 14]LogTemp: Warning: Health: -20300.000000
[2025.06.29-14.53.07:329][ 15]LogTemp: Warning: Health: -20350.000000
[2025.06.29-14.53.07:337][ 16]LogTemp: Warning: Health: -20400.000000
[2025.06.29-14.53.07:345][ 17]LogTemp: Warning: Health: -20450.000000
[2025.06.29-14.53.07:353][ 18]LogTemp: Warning: Health: -20500.000000
[2025.06.29-14.53.07:362][ 19]LogTemp: Warning: Health: -20550.000000
[2025.06.29-14.53.07:371][ 20]LogTemp: Warning: Health: -20600.000000
[2025.06.29-14.53.07:379][ 21]LogTemp: Warning: Health: -20650.000000
[2025.06.29-14.53.07:387][ 22]LogTemp: Warning: Health: -20700.000000
[2025.06.29-14.53.07:396][ 23]LogTemp: Warning: Health: -20750.000000
[2025.06.29-14.53.07:404][ 24]LogTemp: Warning: Health: -20800.000000
[2025.06.29-14.53.07:412][ 25]LogTemp: Warning: Health: -20850.000000
[2025.06.29-14.53.07:421][ 26]LogTemp: Warning: Health: -20900.000000
[2025.06.29-14.53.07:429][ 27]LogTemp: Warning: Health: -20950.000000
[2025.06.29-14.53.07:438][ 28]LogTemp: Warning: Health: -21000.000000
[2025.06.29-14.53.07:446][ 29]LogTemp: Warning: Health: -21050.000000
[2025.06.29-14.53.07:456][ 30]LogTemp: Warning: Health: -21100.000000
[2025.06.29-14.53.07:466][ 31]LogTemp: Warning: Health: -21150.000000
[2025.06.29-14.53.07:474][ 32]LogTemp: Warning: Health: -21200.000000
[2025.06.29-14.53.07:483][ 33]LogTemp: Warning: Health: -21250.000000
[2025.06.29-14.53.07:491][ 34]LogTemp: Warning: Health: -21300.000000
[2025.06.29-14.53.07:500][ 35]LogTemp: Warning: Health: -21350.000000
[2025.06.29-14.53.07:509][ 36]LogTemp: Warning: Health: -21400.000000
[2025.06.29-14.53.07:518][ 37]LogTemp: Warning: Health: -21450.000000
[2025.06.29-14.53.07:528][ 38]LogTemp: Warning: Health: -21500.000000
[2025.06.29-14.53.07:536][ 39]LogTemp: Warning: Health: -21550.000000
[2025.06.29-14.53.07:546][ 40]LogTemp: Warning: Health: -21600.000000
[2025.06.29-14.53.07:555][ 41]LogTemp: Warning: Health: -21650.000000
[2025.06.29-14.53.07:564][ 42]LogTemp: Warning: Health: -21700.000000
[2025.06.29-14.53.07:573][ 43]LogTemp: Warning: Health: -21750.000000
[2025.06.29-14.53.07:581][ 44]LogTemp: Warning: Health: -21800.000000
[2025.06.29-14.53.07:590][ 45]LogTemp: Warning: Health: -21850.000000
[2025.06.29-14.53.07:598][ 46]LogTemp: Warning: Health: -21900.000000
[2025.06.29-14.53.07:607][ 47]LogTemp: Warning: Health: -21950.000000
[2025.06.29-14.53.07:617][ 48]LogTemp: Warning: Health: -22000.000000
[2025.06.29-14.53.07:626][ 49]LogTemp: Warning: Health: -22050.000000
[2025.06.29-14.53.07:635][ 50]LogTemp: Warning: Health: -22100.000000
[2025.06.29-14.53.07:644][ 51]LogTemp: Warning: Health: -22150.000000
[2025.06.29-14.53.07:653][ 52]LogTemp: Warning: Health: -22200.000000
[2025.06.29-14.53.07:661][ 53]LogTemp: Warning: Health: -22250.000000
[2025.06.29-14.53.07:670][ 54]LogTemp: Warning: Health: -22300.000000
[2025.06.29-14.53.07:678][ 55]LogTemp: Warning: Health: -22350.000000
[2025.06.29-14.53.07:686][ 56]LogTemp: Warning: Health: -22400.000000
[2025.06.29-14.53.07:695][ 57]LogTemp: Warning: Health: -22450.000000
[2025.06.29-14.53.07:704][ 58]LogTemp: Warning: Health: -22500.000000
[2025.06.29-14.53.07:711][ 59]LogTemp: Warning: Health: -22550.000000
[2025.06.29-14.53.07:720][ 60]LogTemp: Warning: Health: -22600.000000
[2025.06.29-14.53.07:729][ 61]LogTemp: Warning: Health: -22650.000000
[2025.06.29-14.53.07:737][ 62]LogTemp: Warning: Health: -22700.000000
[2025.06.29-14.53.07:746][ 63]LogTemp: Warning: Health: -22750.000000
[2025.06.29-14.53.07:754][ 64]LogTemp: Warning: Health: -22800.000000
[2025.06.29-14.53.07:762][ 65]LogTemp: Warning: Health: -22850.000000
[2025.06.29-14.53.07:770][ 66]LogTemp: Warning: Health: -22900.000000
[2025.06.29-14.53.07:779][ 67]LogTemp: Warning: Health: -22950.000000
[2025.06.29-14.53.07:787][ 68]LogTemp: Warning: Health: -23000.000000
[2025.06.29-14.53.07:796][ 69]LogTemp: Warning: Health: -23050.000000
[2025.06.29-14.53.07:804][ 70]LogTemp: Warning: Health: -23100.000000
[2025.06.29-14.53.07:813][ 71]LogTemp: Warning: Health: -23150.000000
[2025.06.29-14.53.07:821][ 72]LogTemp: Warning: Health: -23200.000000
[2025.06.29-14.53.07:831][ 73]LogTemp: Warning: Health: -23250.000000
[2025.06.29-14.53.07:841][ 74]LogTemp: Warning: Health: -23300.000000
[2025.06.29-14.53.07:848][ 75]LogTemp: Warning: Health: -23350.000000
[2025.06.29-14.53.07:857][ 76]LogTemp: Warning: Health: -23400.000000
[2025.06.29-14.53.07:865][ 77]LogTemp: Warning: Health: -23450.000000
[2025.06.29-14.53.07:874][ 78]LogTemp: Warning: Health: -23500.000000
[2025.06.29-14.53.07:882][ 79]LogTemp: Warning: Health: -23550.000000
[2025.06.29-14.53.07:891][ 80]LogTemp: Warning: Health: -23600.000000
[2025.06.29-14.53.07:899][ 81]LogTemp: Warning: Health: -23650.000000
[2025.06.29-14.53.07:907][ 82]LogTemp: Warning: Health: -23700.000000
[2025.06.29-14.53.07:916][ 83]LogTemp: Warning: Health: -23750.000000
[2025.06.29-14.53.07:924][ 84]LogTemp: Warning: Health: -23800.000000
[2025.06.29-14.53.07:932][ 85]LogTemp: Warning: Health: -23850.000000
[2025.06.29-14.53.07:941][ 86]LogTemp: Warning: Health: -23900.000000
[2025.06.29-14.53.07:949][ 87]LogTemp: Warning: Health: -23950.000000
[2025.06.29-14.53.07:958][ 88]LogTemp: Warning: Health: -24000.000000
[2025.06.29-14.53.07:966][ 89]LogTemp: Warning: Health: -24050.000000
[2025.06.29-14.53.07:974][ 90]LogTemp: Warning: Health: -24100.000000
[2025.06.29-14.53.07:982][ 91]LogTemp: Warning: Health: -24150.000000
[2025.06.29-14.53.07:991][ 92]LogTemp: Warning: Health: -24200.000000
[2025.06.29-14.53.08:000][ 93]LogTemp: Warning: Health: -24250.000000
[2025.06.29-14.53.08:008][ 94]LogTemp: Warning: Health: -24300.000000
[2025.06.29-14.53.08:016][ 95]LogTemp: Warning: Health: -24350.000000
[2025.06.29-14.53.08:025][ 96]LogTemp: Warning: Health: -24400.000000
[2025.06.29-14.53.08:033][ 97]LogTemp: Warning: Health: -24450.000000
[2025.06.29-14.53.08:041][ 98]LogTemp: Warning: Health: -24500.000000
[2025.06.29-14.53.08:050][ 99]LogTemp: Warning: Health: -24550.000000
[2025.06.29-14.53.08:058][100]LogTemp: Warning: Health: -24600.000000
[2025.06.29-14.53.08:067][101]LogTemp: Warning: Health: -24650.000000
[2025.06.29-14.53.08:076][102]LogTemp: Warning: Health: -24700.000000
[2025.06.29-14.53.08:085][103]LogTemp: Warning: Health: -24750.000000
[2025.06.29-14.53.08:094][104]LogTemp: Warning: Health: -24800.000000
[2025.06.29-14.53.08:102][105]LogTemp: Warning: Health: -24850.000000
[2025.06.29-14.53.08:110][106]LogTemp: Warning: Health: -24900.000000
[2025.06.29-14.53.08:119][107]LogTemp: Warning: Health: -24950.000000
[2025.06.29-14.53.08:127][108]LogTemp: Warning: Health: -25000.000000
[2025.06.29-14.53.08:136][109]LogTemp: Warning: Health: -25050.000000
[2025.06.29-14.53.08:144][110]LogTemp: Warning: Health: -25100.000000
[2025.06.29-14.53.08:152][111]LogTemp: Warning: Health: -25150.000000
[2025.06.29-14.53.08:161][112]LogTemp: Warning: Health: -25200.000000
[2025.06.29-14.53.08:170][113]LogTemp: Warning: Health: -25250.000000
[2025.06.29-14.53.08:178][114]LogTemp: Warning: Health: -25300.000000
[2025.06.29-14.53.08:187][115]LogTemp: Warning: Health: -25350.000000
[2025.06.29-14.53.08:195][116]LogTemp: Warning: Health: -25400.000000
[2025.06.29-14.53.08:203][117]LogTemp: Warning: Health: -25450.000000
[2025.06.29-14.53.08:212][118]LogTemp: Warning: Health: -25500.000000
[2025.06.29-14.53.08:220][119]LogTemp: Warning: Health: -25550.000000
[2025.06.29-14.53.10:414][363]LogTemp: Warning: Health: -25600.000000
[2025.06.29-14.53.10:423][364]LogTemp: Warning: Health: -25650.000000
[2025.06.29-14.53.10:432][365]LogTemp: Warning: Health: -25700.000000
[2025.06.29-14.53.10:441][366]LogTemp: Warning: Health: -25750.000000
[2025.06.29-14.53.10:451][367]LogTemp: Warning: Health: -25800.000000
[2025.06.29-14.53.10:462][368]LogTemp: Warning: Health: -25850.000000
[2025.06.29-14.53.10:471][369]LogTemp: Warning: Health: -25900.000000
[2025.06.29-14.53.10:480][370]LogTemp: Warning: Health: -25950.000000
[2025.06.29-14.53.10:488][371]LogTemp: Warning: Health: -26000.000000
[2025.06.29-14.53.10:497][372]LogTemp: Warning: Health: -26050.000000
[2025.06.29-14.53.10:505][373]LogTemp: Warning: Health: -26100.000000
[2025.06.29-14.53.10:514][374]LogTemp: Warning: Health: -26150.000000
[2025.06.29-14.53.10:522][375]LogTemp: Warning: Health: -26200.000000
[2025.06.29-14.53.10:531][376]LogTemp: Warning: Health: -26250.000000
[2025.06.29-14.53.10:540][377]LogTemp: Warning: Health: -26300.000000
[2025.06.29-14.53.10:549][378]LogTemp: Warning: Health: -26350.000000
[2025.06.29-14.53.10:558][379]LogTemp: Warning: Health: -26400.000000
[2025.06.29-14.53.10:570][380]LogTemp: Warning: Health: -26450.000000
[2025.06.29-14.53.10:579][381]LogTemp: Warning: Health: -26500.000000
[2025.06.29-14.53.10:587][382]LogTemp: Warning: Health: -26550.000000
[2025.06.29-14.53.10:596][383]LogTemp: Warning: Health: -26600.000000
[2025.06.29-14.53.10:604][384]LogTemp: Warning: Health: -26650.000000
[2025.06.29-14.53.10:613][385]LogTemp: Warning: Health: -26700.000000
[2025.06.29-14.53.10:621][386]LogTemp: Warning: Health: -26750.000000
[2025.06.29-14.53.10:629][387]LogTemp: Warning: Health: -26800.000000
[2025.06.29-14.53.10:638][388]LogTemp: Warning: Health: -26850.000000
[2025.06.29-14.53.10:646][389]LogTemp: Warning: Health: -26900.000000
[2025.06.29-14.53.10:655][390]LogTemp: Warning: Health: -26950.000000
[2025.06.29-14.53.10:664][391]LogTemp: Warning: Health: -27000.000000
[2025.06.29-14.53.10:672][392]LogTemp: Warning: Health: -27050.000000
[2025.06.29-14.53.10:681][393]LogTemp: Warning: Health: -27100.000000
[2025.06.29-14.53.10:690][394]LogTemp: Warning: Health: -27150.000000
[2025.06.29-14.53.10:698][395]LogTemp: Warning: Health: -27200.000000
[2025.06.29-14.53.10:706][396]LogTemp: Warning: Health: -27250.000000
[2025.06.29-14.53.10:715][397]LogTemp: Warning: Health: -27300.000000
[2025.06.29-14.53.10:724][398]LogTemp: Warning: Health: -27350.000000
[2025.06.29-14.53.10:733][399]LogTemp: Warning: Health: -27400.000000
[2025.06.29-14.53.10:740][400]LogTemp: Warning: Health: -27450.000000
[2025.06.29-14.53.10:749][401]LogTemp: Warning: Health: -27500.000000
[2025.06.29-14.53.10:757][402]LogTemp: Warning: Health: -27550.000000
[2025.06.29-14.53.10:767][403]LogTemp: Warning: Health: -27600.000000
[2025.06.29-14.53.10:776][404]LogTemp: Warning: Health: -27650.000000
[2025.06.29-14.53.10:784][405]LogTemp: Warning: Health: -27700.000000
[2025.06.29-14.53.10:793][406]LogTemp: Warning: Health: -27750.000000
[2025.06.29-14.53.10:800][407]LogTemp: Warning: Health: -27800.000000
[2025.06.29-14.53.10:808][408]LogTemp: Warning: Health: -27850.000000
[2025.06.29-14.53.10:818][409]LogTemp: Warning: Health: -27900.000000
[2025.06.29-14.53.10:827][410]LogTemp: Warning: Health: -27950.000000
[2025.06.29-14.53.10:834][411]LogTemp: Warning: Health: -28000.000000
[2025.06.29-14.53.10:843][412]LogTemp: Warning: Health: -28050.000000
[2025.06.29-14.53.10:852][413]LogTemp: Warning: Health: -28100.000000
[2025.06.29-14.53.10:860][414]LogTemp: Warning: Health: -28150.000000
[2025.06.29-14.53.10:869][415]LogTemp: Warning: Health: -28200.000000
[2025.06.29-14.53.10:877][416]LogTemp: Warning: Health: -28250.000000
[2025.06.29-14.53.10:886][417]LogTemp: Warning: Health: -28300.000000
[2025.06.29-14.53.10:894][418]LogTemp: Warning: Health: -28350.000000
[2025.06.29-14.53.10:903][419]LogTemp: Warning: Health: -28400.000000
[2025.06.29-14.53.10:911][420]LogTemp: Warning: Health: -28450.000000
[2025.06.29-14.53.10:920][421]LogTemp: Warning: Health: -28500.000000
[2025.06.29-14.53.10:928][422]LogTemp: Warning: Health: -28550.000000
[2025.06.29-14.53.10:937][423]LogTemp: Warning: Health: -28600.000000
[2025.06.29-14.53.10:946][424]LogTemp: Warning: Health: -28650.000000
[2025.06.29-14.53.10:954][425]LogTemp: Warning: Health: -28700.000000
[2025.06.29-14.53.10:962][426]LogTemp: Warning: Health: -28750.000000
[2025.06.29-14.53.10:970][427]LogTemp: Warning: Health: -28800.000000
[2025.06.29-14.53.10:980][428]LogTemp: Warning: Health: -28850.000000
[2025.06.29-14.53.10:988][429]LogTemp: Warning: Health: -28900.000000
[2025.06.29-14.53.10:997][430]LogTemp: Warning: Health: -28950.000000
[2025.06.29-14.53.11:005][431]LogTemp: Warning: Health: -29000.000000
[2025.06.29-14.53.11:013][432]LogTemp: Warning: Health: -29050.000000
[2025.06.29-14.53.11:021][433]LogTemp: Warning: Health: -29100.000000
[2025.06.29-14.53.11:030][434]LogTemp: Warning: Health: -29150.000000
[2025.06.29-14.53.11:039][435]LogTemp: Warning: Health: -29200.000000
[2025.06.29-14.53.11:047][436]LogTemp: Warning: Health: -29250.000000
[2025.06.29-14.53.11:056][437]LogTemp: Warning: Health: -29300.000000
[2025.06.29-14.53.11:065][438]LogTemp: Warning: Health: -29350.000000
[2025.06.29-14.53.11:073][439]LogTemp: Warning: Health: -29400.000000
[2025.06.29-14.53.11:081][440]LogTemp: Warning: Health: -29450.000000
[2025.06.29-14.53.11:090][441]LogTemp: Warning: Health: -29500.000000
[2025.06.29-14.53.11:098][442]LogTemp: Warning: Health: -29550.000000
[2025.06.29-14.53.11:107][443]LogTemp: Warning: Health: -29600.000000
[2025.06.29-14.53.11:115][444]LogTemp: Warning: Health: -29650.000000
[2025.06.29-14.53.11:124][445]LogTemp: Warning: Health: -29700.000000
[2025.06.29-14.53.11:133][446]LogTemp: Warning: Health: -29750.000000
[2025.06.29-14.53.11:141][447]LogTemp: Warning: Health: -29800.000000
[2025.06.29-14.53.11:150][448]LogTemp: Warning: Health: -29850.000000
[2025.06.29-14.53.11:159][449]LogTemp: Warning: Health: -29900.000000
[2025.06.29-14.53.11:167][450]LogTemp: Warning: Health: -29950.000000
[2025.06.29-14.53.11:175][451]LogTemp: Warning: Health: -30000.000000
[2025.06.29-14.53.11:183][452]LogTemp: Warning: Health: -30050.000000
[2025.06.29-14.53.11:191][453]LogTemp: Warning: Health: -30100.000000
[2025.06.29-14.53.11:200][454]LogTemp: Warning: Health: -30150.000000
[2025.06.29-14.53.11:209][455]LogTemp: Warning: Health: -30200.000000
[2025.06.29-14.53.11:218][456]LogTemp: Warning: Health: -30250.000000
[2025.06.29-14.53.11:226][457]LogTemp: Warning: Health: -30300.000000
[2025.06.29-14.53.11:235][458]LogTemp: Warning: Health: -30350.000000
[2025.06.29-14.53.11:243][459]LogTemp: Warning: Health: -30400.000000
[2025.06.29-14.53.11:252][460]LogTemp: Warning: Health: -30450.000000
[2025.06.29-14.53.11:260][461]LogTemp: Warning: Health: -30500.000000
[2025.06.29-14.53.11:268][462]LogTemp: Warning: Health: -30550.000000
[2025.06.29-14.53.11:278][463]LogTemp: Warning: Health: -30600.000000
[2025.06.29-14.53.11:286][464]LogTemp: Warning: Health: -30650.000000
[2025.06.29-14.53.11:295][465]LogTemp: Warning: Health: -30700.000000
[2025.06.29-14.53.11:304][466]LogTemp: Warning: Health: -30750.000000
[2025.06.29-14.53.11:313][467]LogTemp: Warning: Health: -30800.000000
[2025.06.29-14.53.11:322][468]LogTemp: Warning: Health: -30850.000000
[2025.06.29-14.53.11:329][469]LogTemp: Warning: Health: -30900.000000
[2025.06.29-14.53.11:338][470]LogTemp: Warning: Health: -30950.000000
[2025.06.29-14.53.11:347][471]LogTemp: Warning: Health: -31000.000000
[2025.06.29-14.53.11:355][472]LogTemp: Warning: Health: -31050.000000
[2025.06.29-14.53.11:363][473]LogTemp: Warning: Health: -31100.000000
[2025.06.29-14.53.11:371][474]LogTemp: Warning: Health: -31150.000000
[2025.06.29-14.53.11:380][475]LogTemp: Warning: Health: -31200.000000
[2025.06.29-14.53.11:389][476]LogTemp: Warning: Health: -31250.000000
[2025.06.29-14.53.11:397][477]LogTemp: Warning: Health: -31300.000000
[2025.06.29-14.53.11:405][478]LogTemp: Warning: Health: -31350.000000
[2025.06.29-14.53.11:413][479]LogTemp: Warning: Health: -31400.000000
[2025.06.29-14.53.11:421][480]LogTemp: Warning: Health: -31450.000000
[2025.06.29-14.53.11:430][481]LogTemp: Warning: Health: -31500.000000
[2025.06.29-14.53.11:438][482]LogTemp: Warning: Health: -31550.000000
[2025.06.29-14.53.11:447][483]LogTemp: Warning: Health: -31600.000000
[2025.06.29-14.53.11:458][484]LogTemp: Warning: Health: -31650.000000
[2025.06.29-14.53.11:467][485]LogTemp: Warning: Health: -31700.000000
[2025.06.29-14.53.11:475][486]LogTemp: Warning: Health: -31750.000000
[2025.06.29-14.53.11:483][487]LogTemp: Warning: Health: -31800.000000
[2025.06.29-14.53.11:492][488]LogTemp: Warning: Health: -31850.000000
[2025.06.29-14.53.11:500][489]LogTemp: Warning: Health: -31900.000000
[2025.06.29-14.53.11:509][490]LogTemp: Warning: Health: -31950.000000
[2025.06.29-14.53.11:517][491]LogTemp: Warning: Health: -32000.000000
[2025.06.29-14.53.11:526][492]LogTemp: Warning: Health: -32050.000000
[2025.06.29-14.53.11:534][493]LogTemp: Warning: Health: -32100.000000
[2025.06.29-14.53.11:543][494]LogTemp: Warning: Health: -32150.000000
[2025.06.29-14.53.11:551][495]LogTemp: Warning: Health: -32200.000000
[2025.06.29-14.53.11:560][496]LogTemp: Warning: Health: -32250.000000
[2025.06.29-14.53.11:568][497]LogTemp: Warning: Health: -32300.000000
[2025.06.29-14.53.11:577][498]LogTemp: Warning: Health: -32350.000000
[2025.06.29-14.53.11:585][499]LogTemp: Warning: Health: -32400.000000
[2025.06.29-14.53.11:594][500]LogTemp: Warning: Health: -32450.000000
[2025.06.29-14.53.11:602][501]LogTemp: Warning: Health: -32500.000000
[2025.06.29-14.53.11:610][502]LogTemp: Warning: Health: -32550.000000
[2025.06.29-14.53.11:619][503]LogTemp: Warning: Health: -32600.000000
[2025.06.29-14.53.11:627][504]LogTemp: Warning: Health: -32650.000000
[2025.06.29-14.53.11:636][505]LogTemp: Warning: Health: -32700.000000
[2025.06.29-14.53.11:644][506]LogTemp: Warning: Health: -32750.000000
[2025.06.29-14.53.11:652][507]LogTemp: Warning: Health: -32800.000000
[2025.06.29-14.53.11:661][508]LogTemp: Warning: Health: -32850.000000
[2025.06.29-14.53.11:669][509]LogTemp: Warning: Health: -32900.000000
[2025.06.29-14.53.11:679][510]LogTemp: Warning: Health: -32950.000000
[2025.06.29-14.53.11:687][511]LogTemp: Warning: Health: -33000.000000
[2025.06.29-14.53.11:696][512]LogTemp: Warning: Health: -33050.000000
[2025.06.29-14.53.11:704][513]LogTemp: Warning: Health: -33100.000000
[2025.06.29-14.53.11:714][514]LogTemp: Warning: Health: -33150.000000
[2025.06.29-14.53.11:722][515]LogTemp: Warning: Health: -33200.000000
[2025.06.29-14.53.12:593][613]LogTemp: Warning: Health: -33250.000000
[2025.06.29-14.53.12:602][614]LogTemp: Warning: Health: -33300.000000
[2025.06.29-14.53.12:612][615]LogTemp: Warning: Health: -33350.000000
[2025.06.29-14.53.12:620][616]LogTemp: Warning: Health: -33400.000000
[2025.06.29-14.53.12:631][617]LogTemp: Warning: Health: -33450.000000
[2025.06.29-14.53.12:638][618]LogTemp: Warning: Health: -33500.000000
[2025.06.29-14.53.12:648][619]LogTemp: Warning: Health: -33550.000000
[2025.06.29-14.53.12:657][620]LogTemp: Warning: Health: -33600.000000
[2025.06.29-14.53.12:666][621]LogTemp: Warning: Health: -33650.000000
[2025.06.29-14.53.13:197][680]LogTemp: Warning: Health: -33700.000000
[2025.06.29-14.53.13:206][681]LogTemp: Warning: Health: -33750.000000
[2025.06.29-14.53.13:215][682]LogTemp: Warning: Health: -33800.000000
[2025.06.29-14.53.13:224][683]LogTemp: Warning: Health: -33850.000000
[2025.06.29-14.53.13:233][684]LogTemp: Warning: Health: -33900.000000
[2025.06.29-14.53.13:244][685]LogTemp: Warning: Health: -33950.000000
[2025.06.29-14.53.13:253][686]LogTemp: Warning: Health: -34000.000000
[2025.06.29-14.53.13:263][687]LogTemp: Warning: Health: -34050.000000
[2025.06.29-14.53.13:273][688]LogTemp: Warning: Health: -34100.000000
[2025.06.29-14.53.13:283][689]LogTemp: Warning: Health: -34150.000000
[2025.06.29-14.53.13:294][690]LogTemp: Warning: Health: -34200.000000
[2025.06.29-14.53.13:303][691]LogTemp: Warning: Health: -34250.000000
[2025.06.29-14.53.13:311][692]LogTemp: Warning: Health: -34300.000000
[2025.06.29-14.53.13:320][693]LogTemp: Warning: Health: -34350.000000
[2025.06.29-14.53.13:329][694]LogTemp: Warning: Health: -34400.000000
[2025.06.29-14.53.13:337][695]LogTemp: Warning: Health: -34450.000000
[2025.06.29-14.53.13:346][696]LogTemp: Warning: Health: -34500.000000
[2025.06.29-14.53.13:354][697]LogTemp: Warning: Health: -34550.000000
[2025.06.29-14.53.13:837][749]LogTemp: Warning: Health: -34600.000000
[2025.06.29-14.53.13:846][750]LogTemp: Warning: Health: -34650.000000
[2025.06.29-14.53.13:854][751]LogTemp: Warning: Health: -34700.000000
[2025.06.29-14.53.13:864][752]LogTemp: Warning: Health: -34750.000000
[2025.06.29-14.53.13:874][753]LogTemp: Warning: Health: -34800.000000
[2025.06.29-14.53.13:883][754]LogTemp: Warning: Health: -34850.000000
[2025.06.29-14.53.13:893][755]LogTemp: Warning: Health: -34900.000000
[2025.06.29-14.53.13:903][756]LogTemp: Warning: Health: -34950.000000
[2025.06.29-14.53.13:915][757]LogTemp: Warning: Health: -35000.000000
[2025.06.29-14.53.13:926][758]LogTemp: Warning: Health: -35050.000000
[2025.06.29-14.53.13:938][759]LogTemp: Warning: Health: -35100.000000
[2025.06.29-14.53.13:946][760]LogTemp: Warning: Health: -35150.000000
[2025.06.29-14.53.13:955][761]LogTemp: Warning: Health: -35200.000000
[2025.06.29-14.53.13:964][762]LogTemp: Warning: Health: -35250.000000
[2025.06.29-14.53.13:974][763]LogTemp: Warning: Health: -35300.000000
[2025.06.29-14.53.13:982][764]LogTemp: Warning: Health: -35350.000000
[2025.06.29-14.53.13:991][765]LogTemp: Warning: Health: -35400.000000
[2025.06.29-14.53.14:000][766]LogTemp: Warning: Health: -35450.000000
[2025.06.29-14.53.20:131][457]LogTemp: Warning: Health: 50.000000
[2025.06.29-14.53.20:538][501]LogTemp: Warning: Health: -35500.000000
[2025.06.29-14.53.21:719][630]LogTemp: Warning: Health: -35550.000000
[2025.06.29-14.53.26:502][157]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.53.26:502][157]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.53.26:503][157]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.53.26:504][157]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.53.26:506][157]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.29-14.53.26:517][157]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.53.26:552][157]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.29-14.53.26:552][157]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.29-14.53.26:552][157]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=4
[2025.06.29-14.53.26:555][157]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3, StreamState=2
[2025.06.29-14.53.26:559][157]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.53.26:566][157]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.06.29-14.53.26:675][158]LogPlayLevel: Display: Destroying online subsystem :Context_10
[2025.06.29-14.53.27:238][206]Cmd: SELECT NONE
[2025.06.29-14.53.50:857][415]LogHotReload: New module detected: UnrealEditor-ToonTank-0009.dll
[2025.06.29-14.53.51:197][416]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-14.53.51:287][416]LogUObjectHash: Compacting FUObjectHashTables data took   1.34ms
[2025.06.29-14.53.51:469][416]LogUObjectHash: Compacting FUObjectHashTables data took   1.27ms
[2025.06.29-14.53.51:513][416]Display: HotReload took  0.3s.
[2025.06.29-14.53.51:513][416]Display: Reload/Re-instancing Complete: 1 package changed, 5 classes unchanged, 2 functions remapped
[2025.06.29-14.54.01:760][574]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.29-14.54.01:768][574]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.29-14.54.01:791][574]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.54.01:797][574]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006331s)
[2025.06.29-14.54.01:797][574]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006385s)
[2025.06.29-14.54.01:902][574]LogUObjectHash: Compacting FUObjectHashTables data took   1.73ms
[2025.06.29-14.54.01:903][574]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.29-14.54.01:906][574]LogPlayLevel: PIE: World Init took: (0.002271s)
[2025.06.29-14.54.01:906][574]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.29-14.54.01:906][574]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.29-14.54.01:906][574]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.29-14.54.01:906][574]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.29-14.54.01:906][574]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.29-14.54.01:906][574]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.29-14.54.01:907][574]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.29-14.54.01:907][574]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.29-14.54.01:907][574]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.29-14.54.01:907][574]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.29-14.54.01:907][574]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.29-14.54.01:907][574]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.29-14.54.01:909][574]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.29-14.54.01:934][574]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.06.29-14.54.01:934][574]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.29-14.54.01:934][574]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.29-14.54.01:934][574]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.29-14.54.01:935][574]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.29-14.54.01:935][574]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.29-14.54.01:937][574]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.29-14.54.01:937][574]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.29-14.54.01:937][574]LogInit: FAudioDevice initialized with ID 4.
[2025.06.29-14.54.01:937][574]LogAudio: Display: Audio Device (ID: 4) registered with world 'Main'.
[2025.06.29-14.54.01:937][574]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.29-14.54.01:941][574]LogLoad: Game class is 'GameModeBase'
[2025.06.29-14.54.01:943][574]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.29-22.54.01
[2025.06.29-14.54.01:944][574]LogWorld: Bringing up level for play took: 0.002007
[2025.06.29-14.54.01:946][574]LogOnline: OSS: Created online subsystem instance for: :Context_11
[2025.06.29-14.54.01:948][574]PIE: Server logged in
[2025.06.29-14.54.01:950][574]PIE: Play in editor total start time 0.184 seconds.
[2025.06.29-14.54.02:774][651]LogTemp: Warning: BP_PawnTank_C_1 Health: 50.000000
[2025.06.29-14.54.02:928][668]LogTemp: Warning: BP_PawnTank_C_1 Health: 0.000000
[2025.06.29-14.54.02:928][668]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.06.29-14.54.02:945][670]LogTemp: Warning: BP_PawnTank_C_1 Health: -50.000000
[2025.06.29-14.54.08:215][267]LogTemp: Warning: BP_PawnTank_C_1 Health: -100.000000
[2025.06.29-14.54.08:223][268]LogTemp: Warning: BP_PawnTank_C_1 Health: -150.000000
[2025.06.29-14.54.08:232][269]LogTemp: Warning: BP_PawnTank_C_1 Health: -200.000000
[2025.06.29-14.54.08:242][270]LogTemp: Warning: BP_PawnTank_C_1 Health: -250.000000
[2025.06.29-14.54.08:251][271]LogTemp: Warning: BP_PawnTank_C_1 Health: -300.000000
[2025.06.29-14.54.08:259][272]LogTemp: Warning: BP_PawnTank_C_1 Health: -350.000000
[2025.06.29-14.54.08:268][273]LogTemp: Warning: BP_PawnTank_C_1 Health: -400.000000
[2025.06.29-14.54.08:279][274]LogTemp: Warning: BP_PawnTank_C_1 Health: -450.000000
[2025.06.29-14.54.08:287][275]LogTemp: Warning: BP_PawnTank_C_1 Health: -500.000000
[2025.06.29-14.54.08:296][276]LogTemp: Warning: BP_PawnTank_C_1 Health: -550.000000
[2025.06.29-14.54.08:305][277]LogTemp: Warning: BP_PawnTank_C_1 Health: -600.000000
[2025.06.29-14.54.08:316][278]LogTemp: Warning: BP_PawnTank_C_1 Health: -650.000000
[2025.06.29-14.54.08:324][279]LogTemp: Warning: BP_PawnTank_C_1 Health: -700.000000
[2025.06.29-14.54.08:333][280]LogTemp: Warning: BP_PawnTank_C_1 Health: -750.000000
[2025.06.29-14.54.08:343][281]LogTemp: Warning: BP_PawnTank_C_1 Health: -800.000000
[2025.06.29-14.54.08:351][282]LogTemp: Warning: BP_PawnTank_C_1 Health: -850.000000
[2025.06.29-14.54.08:361][283]LogTemp: Warning: BP_PawnTank_C_1 Health: -900.000000
[2025.06.29-14.54.08:369][284]LogTemp: Warning: BP_PawnTank_C_1 Health: -950.000000
[2025.06.29-14.54.08:378][285]LogTemp: Warning: BP_PawnTank_C_1 Health: -1000.000000
[2025.06.29-14.54.08:386][286]LogTemp: Warning: BP_PawnTank_C_1 Health: -1050.000000
[2025.06.29-14.54.08:395][287]LogTemp: Warning: BP_PawnTank_C_1 Health: -1100.000000
[2025.06.29-14.54.08:403][288]LogTemp: Warning: BP_PawnTank_C_1 Health: -1150.000000
[2025.06.29-14.54.08:413][289]LogTemp: Warning: BP_PawnTank_C_1 Health: -1200.000000
[2025.06.29-14.54.08:421][290]LogTemp: Warning: BP_PawnTank_C_1 Health: -1250.000000
[2025.06.29-14.54.08:429][291]LogTemp: Warning: BP_PawnTank_C_1 Health: -1300.000000
[2025.06.29-14.54.08:437][292]LogTemp: Warning: BP_PawnTank_C_1 Health: -1350.000000
[2025.06.29-14.54.08:446][293]LogTemp: Warning: BP_PawnTank_C_1 Health: -1400.000000
[2025.06.29-14.54.08:455][294]LogTemp: Warning: BP_PawnTank_C_1 Health: -1450.000000
[2025.06.29-14.54.08:463][295]LogTemp: Warning: BP_PawnTank_C_1 Health: -1500.000000
[2025.06.29-14.54.08:471][296]LogTemp: Warning: BP_PawnTank_C_1 Health: -1550.000000
[2025.06.29-14.54.08:481][297]LogTemp: Warning: BP_PawnTank_C_1 Health: -1600.000000
[2025.06.29-14.54.08:489][298]LogTemp: Warning: BP_PawnTank_C_1 Health: -1650.000000
[2025.06.29-14.54.08:497][299]LogTemp: Warning: BP_PawnTank_C_1 Health: -1700.000000
[2025.06.29-14.54.08:506][300]LogTemp: Warning: BP_PawnTank_C_1 Health: -1750.000000
[2025.06.29-14.54.08:516][301]LogTemp: Warning: BP_PawnTank_C_1 Health: -1800.000000
[2025.06.29-14.54.08:525][302]LogTemp: Warning: BP_PawnTank_C_1 Health: -1850.000000
[2025.06.29-14.54.08:533][303]LogTemp: Warning: BP_PawnTank_C_1 Health: -1900.000000
[2025.06.29-14.54.08:543][304]LogTemp: Warning: BP_PawnTank_C_1 Health: -1950.000000
[2025.06.29-14.54.08:551][305]LogTemp: Warning: BP_PawnTank_C_1 Health: -2000.000000
[2025.06.29-14.54.08:559][306]LogTemp: Warning: BP_PawnTank_C_1 Health: -2050.000000
[2025.06.29-14.54.08:568][307]LogTemp: Warning: BP_PawnTank_C_1 Health: -2100.000000
[2025.06.29-14.54.08:576][308]LogTemp: Warning: BP_PawnTank_C_1 Health: -2150.000000
[2025.06.29-14.54.08:584][309]LogTemp: Warning: BP_PawnTank_C_1 Health: -2200.000000
[2025.06.29-14.54.08:592][310]LogTemp: Warning: BP_PawnTank_C_1 Health: -2250.000000
[2025.06.29-14.54.08:601][311]LogTemp: Warning: BP_PawnTank_C_1 Health: -2300.000000
[2025.06.29-14.54.08:610][312]LogTemp: Warning: BP_PawnTank_C_1 Health: -2350.000000
[2025.06.29-14.54.08:619][313]LogTemp: Warning: BP_PawnTank_C_1 Health: -2400.000000
[2025.06.29-14.54.08:627][314]LogTemp: Warning: BP_PawnTank_C_1 Health: -2450.000000
[2025.06.29-14.54.08:636][315]LogTemp: Warning: BP_PawnTank_C_1 Health: -2500.000000
[2025.06.29-14.54.08:644][316]LogTemp: Warning: BP_PawnTank_C_1 Health: -2550.000000
[2025.06.29-14.54.08:654][317]LogTemp: Warning: BP_PawnTank_C_1 Health: -2600.000000
[2025.06.29-14.54.08:663][318]LogTemp: Warning: BP_PawnTank_C_1 Health: -2650.000000
[2025.06.29-14.54.08:671][319]LogTemp: Warning: BP_PawnTank_C_1 Health: -2700.000000
[2025.06.29-14.54.08:680][320]LogTemp: Warning: BP_PawnTank_C_1 Health: -2750.000000
[2025.06.29-14.54.08:688][321]LogTemp: Warning: BP_PawnTank_C_1 Health: -2800.000000
[2025.06.29-14.54.08:696][322]LogTemp: Warning: BP_PawnTank_C_1 Health: -2850.000000
[2025.06.29-14.54.08:704][323]LogTemp: Warning: BP_PawnTank_C_1 Health: -2900.000000
[2025.06.29-14.54.08:713][324]LogTemp: Warning: BP_PawnTank_C_1 Health: -2950.000000
[2025.06.29-14.54.08:721][325]LogTemp: Warning: BP_PawnTank_C_1 Health: -3000.000000
[2025.06.29-14.54.08:729][326]LogTemp: Warning: BP_PawnTank_C_1 Health: -3050.000000
[2025.06.29-14.54.08:738][327]LogTemp: Warning: BP_PawnTank_C_1 Health: -3100.000000
[2025.06.29-14.54.08:747][328]LogTemp: Warning: BP_PawnTank_C_1 Health: -3150.000000
[2025.06.29-14.54.08:754][329]LogTemp: Warning: BP_PawnTank_C_1 Health: -3200.000000
[2025.06.29-14.54.08:763][330]LogTemp: Warning: BP_PawnTank_C_1 Health: -3250.000000
[2025.06.29-14.54.08:772][331]LogTemp: Warning: BP_PawnTank_C_1 Health: -3300.000000
[2025.06.29-14.54.08:781][332]LogTemp: Warning: BP_PawnTank_C_1 Health: -3350.000000
[2025.06.29-14.54.08:789][333]LogTemp: Warning: BP_PawnTank_C_1 Health: -3400.000000
[2025.06.29-14.54.08:797][334]LogTemp: Warning: BP_PawnTank_C_1 Health: -3450.000000
[2025.06.29-14.54.08:806][335]LogTemp: Warning: BP_PawnTank_C_1 Health: -3500.000000
[2025.06.29-14.54.08:814][336]LogTemp: Warning: BP_PawnTank_C_1 Health: -3550.000000
[2025.06.29-14.54.08:822][337]LogTemp: Warning: BP_PawnTank_C_1 Health: -3600.000000
[2025.06.29-14.54.10:379][515]LogTemp: Warning: BP_PawnTank_C_1 Health: -3650.000000
[2025.06.29-14.54.10:387][516]LogTemp: Warning: BP_PawnTank_C_1 Health: -3700.000000
[2025.06.29-14.54.10:396][517]LogTemp: Warning: BP_PawnTank_C_1 Health: -3750.000000
[2025.06.29-14.54.10:406][518]LogTemp: Warning: BP_PawnTank_C_1 Health: -3800.000000
[2025.06.29-14.54.10:418][519]LogTemp: Warning: BP_PawnTank_C_1 Health: -3850.000000
[2025.06.29-14.54.10:427][520]LogTemp: Warning: BP_PawnTank_C_1 Health: -3900.000000
[2025.06.29-14.54.10:436][521]LogTemp: Warning: BP_PawnTank_C_1 Health: -3950.000000
[2025.06.29-14.54.10:445][522]LogTemp: Warning: BP_PawnTank_C_1 Health: -4000.000000
[2025.06.29-14.54.10:453][523]LogTemp: Warning: BP_PawnTank_C_1 Health: -4050.000000
[2025.06.29-14.54.10:463][524]LogTemp: Warning: BP_PawnTank_C_1 Health: -4100.000000
[2025.06.29-14.54.10:471][525]LogTemp: Warning: BP_PawnTank_C_1 Health: -4150.000000
[2025.06.29-14.54.14:074][922]LogTemp: Warning: BP_PawnTank_C_1 Health: -4200.000000
[2025.06.29-14.54.17:062][223]LogTemp: Warning: BP_PawnTank_C_1 Health: -4250.000000
[2025.06.29-14.54.17:071][224]LogTemp: Warning: BP_PawnTank_C_1 Health: -4300.000000
[2025.06.29-14.54.17:082][225]LogTemp: Warning: BP_PawnTank_C_1 Health: -4350.000000
[2025.06.29-14.54.17:092][226]LogTemp: Warning: BP_PawnTank_C_1 Health: -4400.000000
[2025.06.29-14.54.17:100][227]LogTemp: Warning: BP_PawnTank_C_1 Health: -4450.000000
[2025.06.29-14.54.17:110][228]LogTemp: Warning: BP_PawnTank_C_1 Health: -4500.000000
[2025.06.29-14.54.17:120][229]LogTemp: Warning: BP_PawnTank_C_1 Health: -4550.000000
[2025.06.29-14.54.17:130][230]LogTemp: Warning: BP_PawnTank_C_1 Health: -4600.000000
[2025.06.29-14.54.17:140][231]LogTemp: Warning: BP_PawnTank_C_1 Health: -4650.000000
[2025.06.29-14.54.17:152][232]LogTemp: Warning: BP_PawnTank_C_1 Health: -4700.000000
[2025.06.29-14.54.17:162][233]LogTemp: Warning: BP_PawnTank_C_1 Health: -4750.000000
[2025.06.29-14.54.17:171][234]LogTemp: Warning: BP_PawnTank_C_1 Health: -4800.000000
[2025.06.29-14.54.17:182][235]LogTemp: Warning: BP_PawnTank_C_1 Health: -4850.000000
[2025.06.29-14.54.17:194][236]LogTemp: Warning: BP_PawnTank_C_1 Health: -4900.000000
[2025.06.29-14.54.17:205][237]LogTemp: Warning: BP_PawnTank_C_1 Health: -4950.000000
[2025.06.29-14.54.17:217][238]LogTemp: Warning: BP_PawnTank_C_1 Health: -5000.000000
[2025.06.29-14.54.17:229][239]LogTemp: Warning: BP_PawnTank_C_1 Health: -5050.000000
[2025.06.29-14.54.17:239][240]LogTemp: Warning: BP_PawnTank_C_1 Health: -5100.000000
[2025.06.29-14.54.17:249][241]LogTemp: Warning: BP_PawnTank_C_1 Health: -5150.000000
[2025.06.29-14.54.17:260][242]LogTemp: Warning: BP_PawnTank_C_1 Health: -5200.000000
[2025.06.29-14.54.17:271][243]LogTemp: Warning: BP_PawnTank_C_1 Health: -5250.000000
[2025.06.29-14.54.17:281][244]LogTemp: Warning: BP_PawnTank_C_1 Health: -5300.000000
[2025.06.29-14.54.18:189][341]LogTemp: Warning: BP_PawnTank_C_1 Health: -5350.000000
[2025.06.29-14.54.21:996][742]LogTemp: Warning: BP_PawnTank_C_1 Health: -5400.000000
[2025.06.29-14.54.22:006][743]LogTemp: Warning: BP_PawnTank_C_1 Health: -5450.000000
[2025.06.29-14.54.22:016][744]LogTemp: Warning: BP_PawnTank_C_1 Health: -5500.000000
[2025.06.29-14.54.22:026][745]LogTemp: Warning: BP_PawnTank_C_1 Health: -5550.000000
[2025.06.29-14.54.22:035][746]LogTemp: Warning: BP_PawnTank_C_1 Health: -5600.000000
[2025.06.29-14.54.22:044][747]LogTemp: Warning: BP_PawnTank_C_1 Health: -5650.000000
[2025.06.29-14.54.22:052][748]LogTemp: Warning: BP_PawnTank_C_1 Health: -5700.000000
[2025.06.29-14.54.22:062][749]LogTemp: Warning: BP_PawnTank_C_1 Health: -5750.000000
[2025.06.29-14.54.22:070][750]LogTemp: Warning: BP_PawnTank_C_1 Health: -5800.000000
[2025.06.29-14.54.22:083][751]LogTemp: Warning: BP_PawnTank_C_1 Health: -5850.000000
[2025.06.29-14.54.22:091][752]LogTemp: Warning: BP_PawnTank_C_1 Health: -5900.000000
[2025.06.29-14.54.22:099][753]LogTemp: Warning: BP_PawnTank_C_1 Health: -5950.000000
[2025.06.29-14.54.22:110][754]LogTemp: Warning: BP_PawnTank_C_1 Health: -6000.000000
[2025.06.29-14.54.22:118][755]LogTemp: Warning: BP_PawnTank_C_1 Health: -6050.000000
[2025.06.29-14.54.22:127][756]LogTemp: Warning: BP_PawnTank_C_1 Health: -6100.000000
[2025.06.29-14.54.22:136][757]LogTemp: Warning: BP_PawnTank_C_1 Health: -6150.000000
[2025.06.29-14.54.22:144][758]LogTemp: Warning: BP_PawnTank_C_1 Health: -6200.000000
[2025.06.29-14.54.22:153][759]LogTemp: Warning: BP_PawnTank_C_1 Health: -6250.000000
[2025.06.29-14.54.22:162][760]LogTemp: Warning: BP_PawnTank_C_1 Health: -6300.000000
[2025.06.29-14.54.22:171][761]LogTemp: Warning: BP_PawnTank_C_1 Health: -6350.000000
[2025.06.29-14.54.22:180][762]LogTemp: Warning: BP_PawnTank_C_1 Health: -6400.000000
[2025.06.29-14.54.22:189][763]LogTemp: Warning: BP_PawnTank_C_1 Health: -6450.000000
[2025.06.29-14.54.22:197][764]LogTemp: Warning: BP_PawnTank_C_1 Health: -6500.000000
[2025.06.29-14.54.22:207][765]LogTemp: Warning: BP_PawnTank_C_1 Health: -6550.000000
[2025.06.29-14.54.22:216][766]LogTemp: Warning: BP_PawnTank_C_1 Health: -6600.000000
[2025.06.29-14.54.22:225][767]LogTemp: Warning: BP_PawnTank_C_1 Health: -6650.000000
[2025.06.29-14.54.22:234][768]LogTemp: Warning: BP_PawnTank_C_1 Health: -6700.000000
[2025.06.29-14.54.22:242][769]LogTemp: Warning: BP_PawnTank_C_1 Health: -6750.000000
[2025.06.29-14.54.22:251][770]LogTemp: Warning: BP_PawnTank_C_1 Health: -6800.000000
[2025.06.29-14.54.22:261][771]LogTemp: Warning: BP_PawnTank_C_1 Health: -6850.000000
[2025.06.29-14.54.22:270][772]LogTemp: Warning: BP_PawnTank_C_1 Health: -6900.000000
[2025.06.29-14.54.22:278][773]LogTemp: Warning: BP_PawnTank_C_1 Health: -6950.000000
[2025.06.29-14.54.22:288][774]LogTemp: Warning: BP_PawnTank_C_1 Health: -7000.000000
[2025.06.29-14.54.22:296][775]LogTemp: Warning: BP_PawnTank_C_1 Health: -7050.000000
[2025.06.29-14.54.22:304][776]LogTemp: Warning: BP_PawnTank_C_1 Health: -7100.000000
[2025.06.29-14.54.22:313][777]LogTemp: Warning: BP_PawnTank_C_1 Health: -7150.000000
[2025.06.29-14.54.22:322][778]LogTemp: Warning: BP_PawnTank_C_1 Health: -7200.000000
[2025.06.29-14.54.22:330][779]LogTemp: Warning: BP_PawnTank_C_1 Health: -7250.000000
[2025.06.29-14.54.22:339][780]LogTemp: Warning: BP_PawnTank_C_1 Health: -7300.000000
[2025.06.29-14.54.22:347][781]LogTemp: Warning: BP_PawnTank_C_1 Health: -7350.000000
[2025.06.29-14.54.22:355][782]LogTemp: Warning: BP_PawnTank_C_1 Health: -7400.000000
[2025.06.29-14.54.22:363][783]LogTemp: Warning: BP_PawnTank_C_1 Health: -7450.000000
[2025.06.29-14.54.22:372][784]LogTemp: Warning: BP_PawnTank_C_1 Health: -7500.000000
[2025.06.29-14.54.22:380][785]LogTemp: Warning: BP_PawnTank_C_1 Health: -7550.000000
[2025.06.29-14.54.22:388][786]LogTemp: Warning: BP_PawnTank_C_1 Health: -7600.000000
[2025.06.29-14.54.22:397][787]LogTemp: Warning: BP_PawnTank_C_1 Health: -7650.000000
[2025.06.29-14.54.22:406][788]LogTemp: Warning: BP_PawnTank_C_1 Health: -7700.000000
[2025.06.29-14.54.22:414][789]LogTemp: Warning: BP_PawnTank_C_1 Health: -7750.000000
[2025.06.29-14.54.22:424][790]LogTemp: Warning: BP_PawnTank_C_1 Health: -7800.000000
[2025.06.29-14.54.22:433][791]LogTemp: Warning: BP_PawnTank_C_1 Health: -7850.000000
[2025.06.29-14.54.22:441][792]LogTemp: Warning: BP_PawnTank_C_1 Health: -7900.000000
[2025.06.29-14.54.22:449][793]LogTemp: Warning: BP_PawnTank_C_1 Health: -7950.000000
[2025.06.29-14.54.22:458][794]LogTemp: Warning: BP_PawnTank_C_1 Health: -8000.000000
[2025.06.29-14.54.22:467][795]LogTemp: Warning: BP_PawnTank_C_1 Health: -8050.000000
[2025.06.29-14.54.22:476][796]LogTemp: Warning: BP_PawnTank_C_1 Health: -8100.000000
[2025.06.29-14.54.22:484][797]LogTemp: Warning: BP_PawnTank_C_1 Health: -8150.000000
[2025.06.29-14.54.22:492][798]LogTemp: Warning: BP_PawnTank_C_1 Health: -8200.000000
[2025.06.29-14.54.22:501][799]LogTemp: Warning: BP_PawnTank_C_1 Health: -8250.000000
[2025.06.29-14.54.22:509][800]LogTemp: Warning: BP_PawnTank_C_1 Health: -8300.000000
[2025.06.29-14.54.22:517][801]LogTemp: Warning: BP_PawnTank_C_1 Health: -8350.000000
[2025.06.29-14.54.22:526][802]LogTemp: Warning: BP_PawnTank_C_1 Health: -8400.000000
[2025.06.29-14.54.22:535][803]LogTemp: Warning: BP_PawnTank_C_1 Health: -8450.000000
[2025.06.29-14.54.22:543][804]LogTemp: Warning: BP_PawnTank_C_1 Health: -8500.000000
[2025.06.29-14.54.22:551][805]LogTemp: Warning: BP_PawnTank_C_1 Health: -8550.000000
[2025.06.29-14.54.22:559][806]LogTemp: Warning: BP_PawnTank_C_1 Health: -8600.000000
[2025.06.29-14.54.22:568][807]LogTemp: Warning: BP_PawnTank_C_1 Health: -8650.000000
[2025.06.29-14.54.22:577][808]LogTemp: Warning: BP_PawnTank_C_1 Health: -8700.000000
[2025.06.29-14.54.22:585][809]LogTemp: Warning: BP_PawnTank_C_1 Health: -8750.000000
[2025.06.29-14.54.22:594][810]LogTemp: Warning: BP_PawnTank_C_1 Health: -8800.000000
[2025.06.29-14.54.22:603][811]LogTemp: Warning: BP_PawnTank_C_1 Health: -8850.000000
[2025.06.29-14.54.22:611][812]LogTemp: Warning: BP_PawnTank_C_1 Health: -8900.000000
[2025.06.29-14.54.22:619][813]LogTemp: Warning: BP_PawnTank_C_1 Health: -8950.000000
[2025.06.29-14.54.22:627][814]LogTemp: Warning: BP_PawnTank_C_1 Health: -9000.000000
[2025.06.29-14.54.22:637][815]LogTemp: Warning: BP_PawnTank_C_1 Health: -9050.000000
[2025.06.29-14.54.22:645][816]LogTemp: Warning: BP_PawnTank_C_1 Health: -9100.000000
[2025.06.29-14.54.22:875][842]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.06.29-14.54.24:949][ 68]LogTemp: Warning: BP_PawnTank_C_1 Health: -9150.000000
[2025.06.29-14.54.25:199][ 95]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.06.29-14.54.29:051][519]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.06.29-14.54.29:372][554]LogTemp: Warning: BP_PawnTurret_C_6 Health: 0.000000
[2025.06.29-14.54.36:003][287]LogTemp: Warning: BP_PawnTank_C_1 Health: -9200.000000
[2025.06.29-14.54.36:435][335]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.06.29-14.54.36:884][384]LogTemp: Warning: BP_PawnTank_C_1 Health: -9250.000000
[2025.06.29-14.54.36:910][387]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.06.29-14.54.40:093][733]LogTemp: Warning: BP_PawnTurret_C_5 Health: 0.000000
[2025.06.29-14.54.40:220][747]LogTemp: Warning: BP_PawnTurret_C_5 Health: -50.000000
[2025.06.29-14.54.41:178][850]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4252.130859
[2025.06.29-14.54.41:455][880]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.29-14.54.41:455][880]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4252.396484, Update Interval: 340.059204
[2025.06.29-14.54.51:606][ 22]LogTemp: Warning: BP_PawnTurret_C_1 Health: -50.000000
[2025.06.29-14.54.52:382][104]LogTemp: Warning: BP_PawnTank_C_1 Health: -9300.000000
[2025.06.29-14.54.52:391][105]LogTemp: Warning: BP_PawnTank_C_1 Health: -9350.000000
[2025.06.29-14.54.52:401][106]LogTemp: Warning: BP_PawnTank_C_1 Health: -9400.000000
[2025.06.29-14.54.52:409][107]LogTemp: Warning: BP_PawnTank_C_1 Health: -9450.000000
[2025.06.29-14.54.52:418][108]LogTemp: Warning: BP_PawnTank_C_1 Health: -9500.000000
[2025.06.29-14.54.52:426][109]LogTemp: Warning: BP_PawnTank_C_1 Health: -9550.000000
[2025.06.29-14.54.52:437][110]LogTemp: Warning: BP_PawnTank_C_1 Health: -9600.000000
[2025.06.29-14.54.52:446][111]LogTemp: Warning: BP_PawnTank_C_1 Health: -9650.000000
[2025.06.29-14.54.52:456][112]LogTemp: Warning: BP_PawnTank_C_1 Health: -9700.000000
[2025.06.29-14.54.52:465][113]LogTemp: Warning: BP_PawnTank_C_1 Health: -9750.000000
[2025.06.29-14.54.52:475][114]LogTemp: Warning: BP_PawnTank_C_1 Health: -9800.000000
[2025.06.29-14.54.52:483][115]LogTemp: Warning: BP_PawnTank_C_1 Health: -9850.000000
[2025.06.29-14.54.52:492][116]LogTemp: Warning: BP_PawnTank_C_1 Health: -9900.000000
[2025.06.29-14.54.52:502][117]LogTemp: Warning: BP_PawnTank_C_1 Health: -9950.000000
[2025.06.29-14.54.52:510][118]LogTemp: Warning: BP_PawnTank_C_1 Health: -10000.000000
[2025.06.29-14.54.52:519][119]LogTemp: Warning: BP_PawnTank_C_1 Health: -10050.000000
[2025.06.29-14.54.52:528][120]LogTemp: Warning: BP_PawnTank_C_1 Health: -10100.000000
[2025.06.29-14.54.52:538][121]LogTemp: Warning: BP_PawnTank_C_1 Health: -10150.000000
[2025.06.29-14.54.52:547][122]LogTemp: Warning: BP_PawnTank_C_1 Health: -10200.000000
[2025.06.29-14.54.52:560][123]LogTemp: Warning: BP_PawnTank_C_1 Health: -10250.000000
[2025.06.29-14.54.52:568][124]LogTemp: Warning: BP_PawnTank_C_1 Health: -10300.000000
[2025.06.29-14.54.52:576][125]LogTemp: Warning: BP_PawnTank_C_1 Health: -10350.000000
[2025.06.29-14.54.52:586][126]LogTemp: Warning: BP_PawnTank_C_1 Health: -10400.000000
[2025.06.29-14.54.52:594][127]LogTemp: Warning: BP_PawnTank_C_1 Health: -10450.000000
[2025.06.29-14.54.52:603][128]LogTemp: Warning: BP_PawnTank_C_1 Health: -10500.000000
[2025.06.29-14.54.52:612][129]LogTemp: Warning: BP_PawnTank_C_1 Health: -10550.000000
[2025.06.29-14.54.52:621][130]LogTemp: Warning: BP_PawnTank_C_1 Health: -10600.000000
[2025.06.29-14.54.52:630][131]LogTemp: Warning: BP_PawnTank_C_1 Health: -10650.000000
[2025.06.29-14.54.52:639][132]LogTemp: Warning: BP_PawnTank_C_1 Health: -10700.000000
[2025.06.29-14.54.52:648][133]LogTemp: Warning: BP_PawnTank_C_1 Health: -10750.000000
[2025.06.29-14.54.52:656][134]LogTemp: Warning: BP_PawnTank_C_1 Health: -10800.000000
[2025.06.29-14.54.52:665][135]LogTemp: Warning: BP_PawnTank_C_1 Health: -10850.000000
[2025.06.29-14.54.52:674][136]LogTemp: Warning: BP_PawnTank_C_1 Health: -10900.000000
[2025.06.29-14.54.52:683][137]LogTemp: Warning: BP_PawnTank_C_1 Health: -10950.000000
[2025.06.29-14.54.52:692][138]LogTemp: Warning: BP_PawnTank_C_1 Health: -11000.000000
[2025.06.29-14.54.52:700][139]LogTemp: Warning: BP_PawnTank_C_1 Health: -11050.000000
[2025.06.29-14.54.52:708][140]LogTemp: Warning: BP_PawnTank_C_1 Health: -11100.000000
[2025.06.29-14.54.52:718][141]LogTemp: Warning: BP_PawnTank_C_1 Health: -11150.000000
[2025.06.29-14.54.52:726][142]LogTemp: Warning: BP_PawnTank_C_1 Health: -11200.000000
[2025.06.29-14.54.52:735][143]LogTemp: Warning: BP_PawnTank_C_1 Health: -11250.000000
[2025.06.29-14.54.52:744][144]LogTemp: Warning: BP_PawnTank_C_1 Health: -11300.000000
[2025.06.29-14.54.52:752][145]LogTemp: Warning: BP_PawnTank_C_1 Health: -11350.000000
[2025.06.29-14.54.52:760][146]LogTemp: Warning: BP_PawnTank_C_1 Health: -11400.000000
[2025.06.29-14.54.52:771][147]LogTemp: Warning: BP_PawnTank_C_1 Health: -11450.000000
[2025.06.29-14.54.52:780][148]LogTemp: Warning: BP_PawnTank_C_1 Health: -11500.000000
[2025.06.29-14.54.52:790][149]LogTemp: Warning: BP_PawnTank_C_1 Health: -11550.000000
[2025.06.29-14.54.52:798][150]LogTemp: Warning: BP_PawnTank_C_1 Health: -11600.000000
[2025.06.29-14.54.52:807][151]LogTemp: Warning: BP_PawnTank_C_1 Health: -11650.000000
[2025.06.29-14.54.52:816][152]LogTemp: Warning: BP_PawnTank_C_1 Health: -11700.000000
[2025.06.29-14.54.52:825][153]LogTemp: Warning: BP_PawnTank_C_1 Health: -11750.000000
[2025.06.29-14.54.52:835][154]LogTemp: Warning: BP_PawnTank_C_1 Health: -11800.000000
[2025.06.29-14.54.52:844][155]LogTemp: Warning: BP_PawnTank_C_1 Health: -11850.000000
[2025.06.29-14.54.52:853][156]LogTemp: Warning: BP_PawnTank_C_1 Health: -11900.000000
[2025.06.29-14.54.52:861][157]LogTemp: Warning: BP_PawnTank_C_1 Health: -11950.000000
[2025.06.29-14.54.52:871][158]LogTemp: Warning: BP_PawnTank_C_1 Health: -12000.000000
[2025.06.29-14.54.52:982][170]LogTemp: Warning: BP_PawnTank_C_1 Health: -12050.000000
[2025.06.29-14.54.53:310][202]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.54.53:311][202]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.54.53:311][202]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.54.53:312][202]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.54.53:314][202]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.29-14.54.53:324][202]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.54.53:355][202]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.29-14.54.53:356][202]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.29-14.54.53:356][202]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=4
[2025.06.29-14.54.53:358][202]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4, StreamState=2
[2025.06.29-14.54.53:362][202]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.54.53:366][202]LogUObjectHash: Compacting FUObjectHashTables data took   1.55ms
[2025.06.29-14.54.53:470][203]LogPlayLevel: Display: Destroying online subsystem :Context_11
[2025.06.29-14.56.01:119][532]LogHotReload: New module detected: UnrealEditor-ToonTank-0010.dll
[2025.06.29-14.56.01:573][537]LogHotReload: Starting Hot-Reload from IDE
[2025.06.29-14.56.01:764][537]LogUObjectHash: Compacting FUObjectHashTables data took   1.42ms
[2025.06.29-14.56.01:941][537]LogUObjectHash: Compacting FUObjectHashTables data took   1.40ms
[2025.06.29-14.56.01:991][537]Display: HotReload took  0.4s.
[2025.06.29-14.56.01:992][537]Display: Reload/Re-instancing Complete: 1 package changed, 5 classes unchanged, 2 functions remapped
[2025.06.29-14.56.03:446][602]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.29-14.56.03:455][602]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.29-14.56.03:487][602]LogPlayLevel: Creating play world package: /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.56.03:492][602]LogPlayLevel: PIE: StaticDuplicateObject took: (0.006045s)
[2025.06.29-14.56.03:492][602]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Maps/Main.Main to /Game/Maps/UEDPIE_0_Main.Main (0.006097s)
[2025.06.29-14.56.03:599][602]LogUObjectHash: Compacting FUObjectHashTables data took   1.72ms
[2025.06.29-14.56.03:601][602]LogChaosDD: Creating Chaos Debug Draw Scene for world Main
[2025.06.29-14.56.03:604][602]LogPlayLevel: PIE: World Init took: (0.002559s)
[2025.06.29-14.56.03:605][602]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.29-14.56.03:605][602]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.29-14.56.03:605][602]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.29-14.56.03:605][602]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.29-14.56.03:605][602]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.29-14.56.03:605][602]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.29-14.56.03:608][602]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.29-14.56.03:632][602]LogAudioMixer: Display: Using Audio Hardware Device Headphones (soundcore Liberty 4 NC Stereo)
[2025.06.29-14.56.03:632][602]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.29-14.56.03:632][602]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.29-14.56.03:632][602]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.29-14.56.03:633][602]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.06.29-14.56.03:633][602]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.06.29-14.56.03:636][602]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.06.29-14.56.03:636][602]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.06.29-14.56.03:636][602]LogInit: FAudioDevice initialized with ID 5.
[2025.06.29-14.56.03:636][602]LogAudio: Display: Audio Device (ID: 5) registered with world 'Main'.
[2025.06.29-14.56.03:636][602]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.06.29-14.56.03:640][602]LogLoad: Game class is 'GameModeBase'
[2025.06.29-14.56.03:642][602]LogWorld: Bringing World /Game/Maps/UEDPIE_0_Main.Main up for play (max tick rate 0) at 2025.06.29-22.56.03
[2025.06.29-14.56.03:642][602]LogWorld: Bringing up level for play took: 0.001964
[2025.06.29-14.56.03:645][602]LogOnline: OSS: Created online subsystem instance for: :Context_12
[2025.06.29-14.56.03:648][602]PIE: Server logged in
[2025.06.29-14.56.03:649][602]PIE: Play in editor total start time 0.195 seconds.
[2025.06.29-14.56.04:460][675]LogTemp: Warning: BP_PawnTank_C_1 Health: 50.000000
[2025.06.29-14.56.04:615][692]LogTemp: Warning: BP_PawnTank_C_1 Health: 0.000000
[2025.06.29-14.56.04:616][692]LogTemp: Warning: BP_PawnTurret_C_1 Health: 50.000000
[2025.06.29-14.56.04:633][694]LogTemp: Warning: BP_PawnTank_C_1 Health: -50.000000
[2025.06.29-14.56.05:463][791]LogTemp: Warning: BP_PawnTank_C_1 Health: -100.000000
[2025.06.29-14.56.05:607][807]LogTemp: Warning: BP_PawnTank_C_1 Health: -150.000000
[2025.06.29-14.56.05:619][808]LogTemp: Warning: BP_PawnTurret_C_1 Health: 0.000000
[2025.06.29-14.56.05:629][809]LogTemp: Warning: BP_PawnTank_C_1 Health: -200.000000
[2025.06.29-14.56.06:542][910]LogTemp: Warning: BP_PawnTurret_C_5 Health: 50.000000
[2025.06.29-14.56.06:659][923]LogTemp: Warning: BP_PawnTank_C_1 Health: -250.000000
[2025.06.29-14.56.06:747][933]LogTemp: Warning: BP_PawnTank_C_1 Health: -300.000000
[2025.06.29-14.56.09:024][183]LogTemp: Warning: BP_PawnTurret_C_6 Health: 50.000000
[2025.06.29-14.56.09:552][240]LogTemp: Warning: BP_PawnTank_C_1 Health: -350.000000
[2025.06.29-14.56.09:570][242]LogTemp: Warning: BP_PawnTurret_C_6 Health: 0.000000
[2025.06.29-14.56.10:533][349]LogTemp: Warning: BP_PawnTurret_C_6 Health: -50.000000
[2025.06.29-14.56.12:790][594]LogTemp: Warning: BP_PawnTurret_C_7 Health: 50.000000
[2025.06.29-14.56.13:311][651]LogTemp: Warning: BP_PawnTurret_C_7 Health: 0.000000
[2025.06.29-14.56.13:532][676]LogTemp: Warning: BP_PawnTurret_C_1 Health: -50.000000
[2025.06.29-14.56.13:640][688]LogTemp: Warning: BP_PawnTank_C_1 Health: -400.000000
[2025.06.29-14.56.13:840][710]LogTemp: Warning: BP_PawnTurret_C_1 Health: -100.000000
[2025.06.29-14.56.14:666][796]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.56.14:666][796]LogWorld: BeginTearingDown for /Game/Maps/UEDPIE_0_Main
[2025.06.29-14.56.14:667][796]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.56.14:667][796]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.56.14:669][796]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.29-14.56.14:680][796]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.56.14:713][796]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.29-14.56.14:727][796]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.06.29-14.56.14:727][796]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=4
[2025.06.29-14.56.14:729][796]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5, StreamState=2
[2025.06.29-14.56.14:733][796]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.29-14.56.14:755][796]LogUObjectHash: Compacting FUObjectHashTables data took   2.54ms
[2025.06.29-14.56.14:812][797]LogPlayLevel: Display: Destroying online subsystem :Context_12
[2025.06.29-14.58.01:775][359]LogUObjectHash: Compacting FUObjectHashTables data took   1.35ms
[2025.06.29-14.58.03:060][467]LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
[2025.06.29-14.58.03:104][467]LogStall: Shutdown...
[2025.06.29-14.58.03:105][467]LogStall: Shutdown complete.
[2025.06.29-14.58.03:130][467]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:130][467]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:190][467]LogActorComponent: UnregisterComponent: (/Engine/Transient.EditorFloorComp) Not registered. Aborting.
[2025.06.29-14.58.03:190][467]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:190][467]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:251][467]LogSlate: Window 'ToonTank - Unreal Editor' being destroyed
[2025.06.29-14.58.03:393][467]LogUObjectHash: Compacting FUObjectHashTables data took   1.31ms
[2025.06.29-14.58.03:414][467]Cmd: QUIT_EDITOR
[2025.06.29-14.58.03:414][468]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.29-14.58.03:421][468]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.29-14.58.03:421][468]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.29-14.58.03:421][468]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.29-14.58.03:422][468]LogWorld: UWorld::CleanupWorld for Main, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:422][468]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:430][468]LogTedsSettings: UTedsSettingsEditorSubsystem::Deinitialize
[2025.06.29-14.58.03:430][468]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.29-14.58.03:431][468]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.29-14.58.03:435][468]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:435][468]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:440][468]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:440][468]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:444][468]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:444][468]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:448][468]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.29-14.58.03:448][468]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.29-14.58.03:453][468]LogRuntimeTelemetry: Recording EnginePreExit events
[2025.06.29-14.58.03:453][468]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.29-14.58.03:456][468]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::EndSession
[2025.06.29-14.58.03:833][468]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.29-14.58.03:833][468]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.29-14.58.03:833][468]LogAudio: Display: Audio Device unregistered from world 'Main'.
[2025.06.29-14.58.03:833][468]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.29-14.58.03:833][468]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=4
[2025.06.29-14.58.03:835][468]LogAudioMixer: Display: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1, StreamState=2
[2025.06.29-14.58.03:840][468]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.29-14.58.03:840][468]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.29-14.58.03:841][468]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.06.29-14.58.03:844][468]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.29-14.58.03:844][468]LogExit: Preparing to exit.
[2025.06.29-14.58.03:880][468]LogUObjectHash: Compacting FUObjectHashTables data took   1.30ms
[2025.06.29-14.58.03:884][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.03:885][468]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.29-14.58.05:033][468]LogEditorDataStorage: Deinitializing
[2025.06.29-14.58.05:114][468]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.29-14.58.05:126][468]LogExit: Editor shut down
[2025.06.29-14.58.05:127][468]LogExit: Transaction tracking system shut down
[2025.06.29-14.58.05:288][468]LogExit: Object subsystem successfully closed.
[2025.06.29-14.58.05:297][468]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.29-14.58.05:359][468]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.29-14.58.05:406][468]LogMemoryProfiler: Shutdown
[2025.06.29-14.58.05:406][468]LogNetworkingProfiler: Shutdown
[2025.06.29-14.58.05:406][468]LoadingProfiler: Shutdown
[2025.06.29-14.58.05:406][468]LogTimingProfiler: Shutdown
[2025.06.29-14.58.05:406][468]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.06.29-14.58.05:406][468]LogChaosVDEditor: [FChaosVDExtensionsManager::UnRegisterExtension] UnRegistering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.06.29-14.58.05:663][468]LogChaosDD: Chaos Debug Draw Shutdown
[2025.06.29-14.58.05:677][468]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.06.29-14.58.05:677][468]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B970058CB-423A-5FC5-D806-C79341C71D03%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.6.0-43139311%2B%2B%2BUE5%2BRelease-5.6&UserID=736956f4414f060eb3ef6884c4fdf35b%7C2cea6243ea6c4d59a9b9ae3518a528b0%7C2f4bda52-3074-479d-ba0f-dfdc8867a3cc&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.06.29-14.58.07:224][468]LogEOSShared: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.06.29-14.58.07:224][468]RenderDocPlugin: plugin has been unloaded.
[2025.06.29-14.58.07:229][468]LogNFORDenoise: NFORDenoise function shutting down
[2025.06.29-14.58.07:232][468]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.06.29-14.58.07:232][468]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.06.29-14.58.07:232][468]LogPakFile: Destroying PakPlatformFile
[2025.06.29-14.58.07:459][468]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.06.29-14.58.07:482][468]LogExit: Exiting.
[2025.06.29-14.58.07:498][468]Log file closed, 06/29/25 22:58:07
