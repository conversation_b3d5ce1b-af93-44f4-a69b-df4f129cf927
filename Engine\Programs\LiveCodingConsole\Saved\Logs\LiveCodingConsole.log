﻿Log file open, 06/25/25 21:13:04
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-839D769D4CD5B22F26B902BA4A9F6BA0
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../Engine/Programs/LiveCodingConsole/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogInit: ExecutableName: LiveCodingConsole.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=736956f4414f060eb3ef6884c4fdf35b
LogInit: DeviceId=
LogInit: Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 10 (22H2) [10.0.19045.5965] (), CPU: AMD Ryzen 7 3700X 8-Core Processor             , GPU: NVIDIA GeForce RTX 2060
LogInit: Compiled (64-bit): May 31 2025 16:41:40
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -Group=UE_ToonTank_0x08ddcd9e -Hidden -ProjectName="ToonTank"
LogInit: Base Directory: Y:/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 35
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogInit: Presizing for max 100000 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
LogConfig: CVar [[r.DumpShaderDebugInfo:2]] deferred - dummy variable created
LogConfig: CVar [[p.chaos.AllowCreatePhysxBodies:1]] deferred - dummy variable created
[2025.06.25-13.13.04:715][  0]LogConfig: CVar [[fx.SkipVectorVMBackendOptimizations:1]] deferred - dummy variable created
[2025.06.25-13.13.04:715][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.25-13.13.04:715][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.25-13.13.04:715][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.25-13.13.04:715][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.25-13.13.04:717][  0]LogInit: Computer: ADMINISTRATOR
[2025.06.25-13.13.04:717][  0]LogInit: User: maxwe
[2025.06.25-13.13.04:717][  0]LogInit: CPU Page size=4096, Cores=8
[2025.06.25-13.13.04:717][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.25-13.13.04:717][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.06.25-13.13.04:717][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=73.4GB
[2025.06.25-13.13.04:717][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.25-13.13.04:717][  0]LogMemory: Process Physical Memory: 55.22 MB used, 55.23 MB peak
[2025.06.25-13.13.04:717][  0]LogMemory: Process Virtual Memory: 122.52 MB used, 122.52 MB peak
[2025.06.25-13.13.04:717][  0]LogMemory: Physical Memory: 16689.72 MB used,  48750.87 MB free, 65440.59 MB total
[2025.06.25-13.13.04:717][  0]LogMemory: Virtual Memory: 18281.54 MB used,  56887.05 MB free, 75168.59 MB total
[2025.06.25-13.13.04:748][  0]LogUObjectArray: 590 objects as part of root set at end of initial load.
[2025.06.25-13.13.04:748][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.25-13.13.04:749][  0]LogInit: Using OS detected language (en-GB).
[2025.06.25-13.13.04:749][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.25-13.13.04:751][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.25-13.13.04:833][  0]LogInit: Using OS detected language (en-GB).
[2025.06.25-13.13.04:833][  0]LogInit: Using OS detected locale (en-HK).
[2025.06.25-13.13.04:833][  0]LogTextLocalizationManager: No localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.25-13.13.04:833][  0]LogTextLocalizationManager: No localization for 'en-HK' exists, so 'en' will be used for the locale.
[2025.06.25-13.13.04:834][  0]LogPackageLocalizationCache: Processed 2 localized package path(s) for 1 prioritized culture(s) in 0.000140 seconds
[2025.06.25-13.13.04:834][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem:   - English (Hong Kong SAR) - (Keyboard).
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem:   - Chinese (Traditional, Taiwan) - Microsoft Quick (TSF IME).
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem:   - Japanese (Japan) - Microsoft IME (TSF IME).
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - Microsoft Pinyin (TSF IME).
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.06.25-13.13.04:850][  0]LogWindowsTextInputMethodSystem: Activated input method: English (Hong Kong SAR) - (Keyboard).
[2025.06.25-13.13.04:871][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetTouchpadParameters
[2025.06.25-13.13.04:886][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.25-13.13.04:886][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.25-13.13.04:908][  0]LogSlate: Using FreeType 2.10.0
[2025.06.25-13.13.04:910][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.25-13.13.04:917][  0]LogStandaloneRenderer: D3D11 adapters settings:
[2025.06.25-13.13.04:917][  0]LogStandaloneRenderer: D3D11 adapters:
[2025.06.25-13.13.04:917][  0]LogStandaloneRenderer: Testing D3D11 adapter: 0. Description: 'NVIDIA GeForce RTX 2060'. VendorId: 10de. DeviceId: 1f08.
[2025.06.25-13.13.05:137][  0]LogStandaloneRenderer:   0. 'NVIDIA GeForce RTX 2060'. Feature level: 11_1
[2025.06.25-13.13.05:137][  0]LogStandaloneRenderer: Testing D3D11 adapter: 1. Description: 'Microsoft Basic Render Driver'. VendorId: 1414. DeviceId: 008c.
[2025.06.25-13.13.05:139][  0]LogStandaloneRenderer:   1. 'Microsoft Basic Render Driver'. Feature level: 11_1
[2025.06.25-13.13.05:139][  0]LogStandaloneRenderer:   Skip adapter.
[2025.06.25-13.13.05:139][  0]LogStandaloneRenderer: Selected D3D11 Description: 'NVIDIA GeForce RTX 2060'. VendorId: 10de. DeviceId: 1f08.
[2025.06.25-13.13.05:517][  0]LogLiveCodingServer: Display: Registered process Y:\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe (PID: 24116)
[2025.06.25-13.13.15:099][  0]LogLiveCodingServer: Display: Loading module Y:\UE Project\ToonTank\Binaries\Win64\UnrealEditor-ToonTank.dll (0.121 MB)
[2025.06.25-13.13.15:190][  0]LogLiveCodingServer: Display: Loaded 1 module(s), 823 lazy load module(s), and 497 reserved page ranges (0.000s, 6 translation units)
[2025.06.25-13.13.15:190][  0]LogLiveCodingServer: Display: Live coding ready - Save changes and press Ctrl+Alt+F11 to re-compile code
