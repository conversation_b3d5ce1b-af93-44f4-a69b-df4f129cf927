{"configurations": [{"name": "ToonTankEditor Editor Win64 Development (ToonTank)", "compilerPath": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "msvc-x64", "compileCommands": ["Y:\\UE_5.6\\.vscode\\compileCommands_ToonTank.json"]}, {"name": "Win32", "compilerPath": "Y:\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "msvc-x64", "compileCommands": ["Y:\\UE_5.6\\.vscode\\compileCommands_Default.json"]}], "version": 4}